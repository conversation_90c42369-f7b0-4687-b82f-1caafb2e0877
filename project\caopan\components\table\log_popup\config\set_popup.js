import {
  i18n_t,
} from "src/output/common/project-common.js"

export const columns_1 = [
	{
		title: i18n_t('log.table3'), // 操作对象
		key: 'objectName',
		align: 'center',
		wdith: '100'
	},
	{
		title: i18n_t('log.table8'), // 操作类型
		key: 'behavior',
		align: 'center',
		width: '100'
	},
	{
		title: i18n_t('log.table10'), //参数名称
		key: 'parameterName',
		// slot:'paramName',
		align: 'center',
	},
	{
		title: i18n_t('log.table11'), // 修改前
		key: 'beforeVal',
		// slot:'beforeVal',
		align: 'center',
		tooltip: true,
	},
	{
		title: i18n_t('log.table12'), // 修改后
		key: 'afterVal',
		// slot:'afterVal',
		align: 'center',
		tooltip: true,
	},
	{
		title: i18n_t('log.table9'), // 操作人
		key: 'userName',
		align: 'center',
		width: '100'
	},
	{
		title: i18n_t('champion_plays.text52'), // 操作时间
		key: 'operateTimeStr',
		align: 'center',
		width: '160'
	},
]//columns类别为1的
export const columns_2 = [
	{
		title: i18n_t('log.table1'), // 操作页面
		key: 'operatePageName',
		align: 'center',
		width: '100'
	},
	{
		title: i18n_t('log.table3'), // 操作对象名称
		key: 'objectName',
		align: 'center',
		width: '180',
		tooltip: true,
	},
	{
		title: i18n_t('log.table6'), // 操作对象扩展ID
		key: 'extObjectName',
		slot: 'extObjectName',
		align: 'center',
		width: '140',
		tooltip: true,
	},
	{
		title: i18n_t('log.table8'), // 操作类型
		key: 'behavior',
		align: 'center',
		width: '100',
		tooltip: true,
	},
	{
		title: i18n_t('log.table10'), //  参数名称
		key: 'parameterName',
		slot: 'paramName',
		align: 'center',
	},
	{
		title: i18n_t('log.table11'), // 修改前
		key: 'beforeVal',
		align: 'center',
		render: (h, { row }) => {
			const [className, value] = get_tooltip_data(row, "beforeVal");

			return h(
				"div", // ✅ 外层 `div`，确保 `ellipsis` 生效
				{
					style: {
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						maxWidth: "100%",
						overflow: "hidden" // ✅ 防止内容溢出
					}
				},
				[
					h(
						"Tooltip",
						{
							props: {
								placement: "top-start",
								transfer: true, // ✅ 让 Tooltip 正确渲染
								maxWidth: 500
							},
							class: className
						},
						[
							// ✅ `slot="content"` 显示完整换行内容
							h("div", {
								slot: "content",
								domProps: { innerHTML: value.replace(/\n/g, "<br>") }
							}),

							// ✅ `span` 内部确保 `ellipsis` 生效
							h(
								"span",
								{
									style: {
										overflow: "hidden",
										textOverflow: "ellipsis",
										whiteSpace: "nowrap",
										display: "block", // ✅ `block` 确保 `ellipsis` 生效
										maxWidth: "350px" // ✅ 让 `maxWidth` 根据父层宽度调整
									}
								},
								value
							)
						]
					)
				]
			);
		}
	},
	{
		title: i18n_t('log.table12'), // 修改后
		key: 'afterVal',
		align: 'center',
		render: (h, { row }) => {
			const [className, value] = get_tooltip_data(row, "afterVal");

			return h(
				"div", // ✅ 外层 `div`，确保 `ellipsis` 生效
				{
					style: {
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						maxWidth: "100%",
						overflow: "hidden" // ✅ 防止内容溢出
					}
				},
				[
					h(
						"Tooltip",
						{
							props: {
								placement: "top-start",
								transfer: true, // ✅ 让 Tooltip 正确渲染
								maxWidth: 500
							},
							class: className
						},
						[
							// ✅ `slot="content"` 显示完整换行内容
							h("div", {
								slot: "content",
								domProps: { innerHTML: value.replace(/\n/g, "<br>") }
							}),

							// ✅ `span` 内部确保 `ellipsis` 生效
							h(
								"span",
								{
									style: {
										overflow: "hidden",
										textOverflow: "ellipsis",
										whiteSpace: "nowrap",
										display: "block", // ✅ `block` 确保 `ellipsis` 生效
										maxWidth: "350px" // ✅ 让 `maxWidth` 根据父层宽度调整
									}
								},
								value
							)
						]
					)
				]
			);
		}
	},
	{
		// title: '操作人',
		title: i18n_t('log.table9'), // 操作人
		key: 'userName',
		align: 'center',
		width: '100'
	},
	{
		// title: '操作时间',
		title: i18n_t('champion_plays.text52'), // 操作时间
		key: 'operateTimeStr',
		align: 'center',
		width: '160'
	},
]//columns类别为2的
export const columns_3 = [
	{
		title: i18n_t('log.table1'), // 操作页面
		key: 'operatePageName',
		align: 'center',
		width: '100'
	},
	{
		title: i18n_t('log.table2'), // 操作对象ID
		key: 'objectId',
		align: 'center',
		width: '140',
		tooltip: true,
	},
	{
		title: i18n_t('log.table3'), // 操作对象名称
		key: 'objectName',
		align: 'center',
		width: '180',
		tooltip: true,
	},
	{
		title: i18n_t('log.table5'), // 操作对象扩展ID
		key: 'extObjectId',
		align: 'center',
		width: '180',
		tooltip: true,
	},
	{
		title: i18n_t('log.table6'), // 操作对象扩展名称
		key: 'extObjectName',
		slot: 'extObjectName',
		align: 'center',
		width: '140',
		tooltip: true,
	},
	{
		title: i18n_t('log.table8'), // 操作类型
		key: 'behavior',
		align: 'center',
		width: '100',
		tooltip: true,
	},
	{
		title: i18n_t('log.table10'), //  参数名称
		key: 'parameterName',
		align: 'center',
	},
	{
		title: i18n_t('log.table11'), // 修改前
		key: 'beforeVal',
		align: 'center',
		tooltip: true,
	},
	{
		title: i18n_t('log.table12'), // 修改后
		key: 'afterVal',
		align: 'center',
		tooltip: true,
	},
	{
		title: i18n_t('log.table9'), // 操作人
		key: 'userName',
		align: 'center',
		width: '100'
	},
	{
		title: 'IP',// IP
		key: 'ip',
		slot: 'IP',
		align: 'center',
		width: '170'
	},
	{
		title: i18n_t('champion_plays.text52'), // 操作时间
		key: 'operateTimeStr',
		slot: "operateTimeStr",
		align: 'center',
		width: '160'
	},
]//columns类别为3的  操盘日志
export const columns_4 = [
	{
		title: i18n_t('log.table1'), // 操作页面
		key: 'operatePageName',
		align: 'center',
		width: '100',
		fixed: 'left'
	},
	{
		title: i18n_t('log.table2'), // 操作对象ID
		key: 'objectId',
		align: 'center',
		width: '180',
		tooltip: true,
	},
	{
		title: i18n_t('log.table3'), // 操作对象名称
		key: 'objectName',
		align: 'center',
		minWidth: 180,
		tooltip: true,
	},
	{
		title: i18n_t('log.table5'), // 操作对象扩展ID
		key: 'extObjectId',
		align: 'center',
		width: '180',
		tooltip: true,
	},
	{
		title: i18n_t('log.table6'), // 操作对象扩展名称
		key: 'extObjectName',
		slot: 'extObjectName',
		align: 'center',
		width: '140',
		tooltip: true,
	},
	{
		title: i18n_t('log.table8'), // 操作类型
		key: 'behavior',
		align: 'center',
		width: '100',
		tooltip: true,
	},
	{
		title: i18n_t('log.table10'), //  参数名称
		key: 'parameterName',
		align: 'center',
		width: '100',
	},
	{
		title: i18n_t('log.table11'), // 修改前
		key: 'beforeVal',
		align: 'center',
		tooltip: true,
		width: '140',
	},
	{
		title: i18n_t('log.table12'), // 修改后
		key: 'afterVal',
		align: 'center',
		tooltip: true,
		width: '140',
	},
	{
		title: i18n_t('log.table9'), // 操作人
		key: 'userName',
		align: 'center',
		width: '100'
	},
	{
		title: 'IP',// IP
		key: 'ipAddress',
		align: 'center',
		width: '170'
	},
	{
		title: i18n_t('champion_plays.text52'), // 操作时间
		key: 'operateTimeStr',
		slot: 'operateTimeStr',
		align: 'center',
		width: '160',
		fixed: 'right'
	},
]//columns类别为4的  报球版日志
export const columns_5 = [
	// {
	// 	title: i18n_t('log.table1'), // 操作页面
	// 	key: 'operatePageName',
	// 	align: 'center',
	// 	width:'100'
	// }, // rc log 不需要操作页面
	{
		title: i18n_t('log.table2'), // 操作对象ID
		key: 'objectId',
		align: 'center',
		width: '180',
		tooltip: true,
	},
	{
		title: i18n_t('log.table3'), // 操作对象名称
		key: 'objectName',
		align: 'center',
		width: '180',
		tooltip: true,
	},
	{
		title: i18n_t('log.table5'), // 操作对象扩展ID
		key: 'extObjectId',
		align: 'center',
		width: '180',
		tooltip: true,
	},
	{
		title: i18n_t('log.table6'), // 操作对象扩展名称
		key: 'extObjectName',
		slot: 'extObjectName',
		align: 'center',
		width: '140',
		tooltip: true,
	},
	{
		title: i18n_t('log.table8'), // 操作类型
		key: 'behavior',
		align: 'center',
		width: '100',
		tooltip: true,
	},
	{
		title: i18n_t('log.table10'), //  参数名称
		key: 'parameterName',
		slot: 'parameterName',
		align: 'center',
	},
	{
		title: i18n_t('log.table11'), // 修改前
		key: 'beforeVal',
		align: 'center',
		tooltip: true,
	},
	{
		title: i18n_t('log.table12'), // 修改后
		key: 'afterVal',
		slot: 'afterVal',
		align: 'center',
		tooltip: true,
	},
	{
		title: i18n_t('log.table9'), // 操作人
		key: 'userName',
		align: 'center',
		width: '100'
	},
	{
		title: 'IP',// IP
		key: 'ipAddress',
		align: 'center',
		width: '170'
	},
	{
		title: i18n_t('champion_plays.text52'), // 操作时间
		key: 'operateTimeStr',
		slot: 'operateTimeStr',
		align: 'center',
		width: '160'
	},
]//columns类别为5的  RC玩法日志


