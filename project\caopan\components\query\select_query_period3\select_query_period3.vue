<template>
  <div
    :class="`row bg-panda-base-title-light line-height-32px items-center full-width p-pl--sm ${has_border ? 'border-bottom-light2' : ''}`"
    style="height: 34px"
  >
    <div v-if="left_btn">
      <span class="dib p1-df p1-aic p1-jcc pr10x cp">
        <span class="panda-left-info-toogle-button" :class="show_more_query ? 'active' : ''" @click="show_more_query = !show_more_query">
          <q-tooltip>
            <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
            <!-- '隐藏左侧信息':'展示左侧信息' -->
            {{ show_more_query ? i18n_t('common.hideLeft') : i18n_t('common.t5') }}
          </q-tooltip>
          <span class="panda-left-info-toogle-button-inner"></span>
        </span>
      </span>
    </div>

    <div class="pr10x text-panda-text-light" v-if="typeof total == 'number'">
      {{ i18n_t('traderTable.text107')
      }}<span style="color: rgb(255, 73, 74); width: 30px; display: inline-block; text-align: center">{{ total || 0 }}</span
      >{{ i18n_t('traderTable.text108') }}
    </div>
    <div
      @click="query_form_date_tab_click(20)"
      :class="selected_date_tab == 20 ? 'bg-panda-query-date-active' : ''"
      class="float-left text-panda-text-dark text-center cursor-pointer"
      v-if="$route.name != 'unsold_match'"
    >
      <span
        class="panda-py-4px panda-px-10px border-radius-2px panda-query-date-tab-label mr-10 panda-border-grey"
        :class="selected_date_tab == 20 ? 'text-panda-date-base' : 'text-panda-date-light'"
      >
        {{ i18n_t('traderTable.Z_0') }}({{ optional_events_num }})
      </span>
      <!--自选赛事-->
    </div>
    <div
      @click="query_form_date_tab_click(21)"
      :class="selected_date_tab == 21 ? 'bg-panda-query-date-active' : ''"
      class="float-left text-panda-text-dark text-center cursor-pointer"
    >
      <span
        class="panda-py-4px panda-px-10px border-radius-2px panda-query-date-tab-label mr-10 panda-border-grey"
        :class="selected_date_tab == 21 ? 'text-panda-date-base' : 'text-panda-date-light'"
      >
        {{ i18n_t('common.inPlay') }}
      </span>
    </div>
    <div
      v-for="(items, indexs) in query_form_date_arr"
      :key="`query_form_date_${indexs}`"
      @click="query_form_date_tab_click(items.value)"
      :class="selected_date_tab == items.value ? 'bg-panda-query-date-active' : ''"
      class="float-left text-panda-text-dark text-center cursor-pointer"
    >
      <span
        class="panda-py-4px panda-px-10px border-radius-2px panda-query-date-tab-label mr-10 panda-border-grey"
        :class="selected_date_tab == items.value ? 'text-panda-date-base' : 'text-panda-date-light'"
      >
        {{ items.label }}
      </span>
    </div>
    <div class="float-left text-panda-date-light text-center cursor-pointer">
      <i-date-picker
        type="date"
        v-model="other_date"
        @on-change="query_form_date_tab_click(7)"
        :options="date_option2"
        format="yyyy-MM-dd"
        placement="bottom-end"
        :placeholder="i18n_t('selectPeriodTab.Tab_6')"
        style="width: 120px; margin-right: 10px"
        :class="selected_date_tab == 7 ? 'borderBlue' : ''"
      />
    </div>

    <div
      v-if="show_history"
      :class="selected_date_tab == 8 ? 'bg-panda-query-date-active' : ''"
      class="float-left text-panda-text-dark text-center cursor-pointer panda-query-date-tab relative-position"
    >
      <template v-if="false">
        <span
          @click="query_form_date_tab_click(8)"
          class="panda-py-2px panda-px-10px border-radius-2px panda-query-date-tab-label"
          :class="selected_date_tab == 8 ? 'text-panda-date-light' : 'text-panda-date-base'"
          >{{ i18n_t('selectPeriodTab.Tab_11') }}</span
        >
        <div
          v-show="selected_date_tab == 8 && show_q_date_component"
          class="absolute bg-panda-base-dark border-radius-4px panda-border"
          style="top: 32px; height: 140px; z-index: 11; box-shadow: 0 2px 14px 0 rgba(0, 0, 0, 0.5); border-radius: 4px"
        >
          <q-date
            v-model="history_date"
            @input="handle_history_date_confirm"
            mask="YYYY-MM-DD HH:mm:ss"
            color="panda-primary"
            text-color="panda-text-light"
            :options="startTime_limit"
            dark
          />
        </div>
      </template>
      <template v-else>
        <i-date-picker
          type="date"
          v-model="history_date"
          @on-change="handle_history_date_confirm"
          :options="date_option"
          format="yyyy-MM-dd"
          placement="bottom-end"
          :placeholder="i18n_t('selectPeriodTab.Tab_11')"
          style="width: 120px"
          :class="selected_date_tab == 8 ? 'borderBlue' : ''"
        />
      </template>
    </div>
    <div class="warnNum">
      <span class="panda_icon_alert"></span>
      <span class="num">100</span>
    </div>
    <div style="flex: 1"></div>
    <div class="sort" v-if="selected_date_tab != 20 && ['match_sale', 'match_live'].includes(routeName)">
      <div @click="edit_sort_btn" v-if="!is_edit_sort">{{ i18n_t('playManagement.modify_sort_value') }}</div>
      <!--修改排序值-->
      <div @click="sort_save" v-if="is_edit_sort">{{ i18n_t('common.save') }}</div>
      <!--保存-->
      <div @click="cancel_sort" v-if="is_edit_sort">{{ i18n_t('common.cancel') }}</div>
      <!--取消-->
    </div>
    <div class="operate">
      <img
        @click="$emit('init_tabledata')"
        @mouseenter="hover_refresh = true"
        @mouseleave="hover_refresh = false"
        :class="[
          {
            refresh_menu_active: tabledata_loading,
            gray: !tabledata_loading && !hover_refresh,
          },
        ]"
        src="/assets/icon/refresh.svg"
        alt="refresh"
      />
      <!-- //assets/icon/refresh.svg -->
      <div @click="toOperate" v-if="['match_sale', 'match_live'].includes(routeName)">
        {{ routeName == 'match_sale' ? i18n_t('selectPeriodTab.Tab_13') : i18n_t('selectPeriodTab.Tab_14') }}
      </div>
      <!--早盘操盘 滚球操盘-->
    </div>
    <span v-if="is_export" class="panda-btn-light-dense" @click="$emit('export_data')" style="width: 64px; margin: 0 10px">
      {{ i18n_t('saicheng.common.export_1') }}
    </span>
  </div>
</template>

<script setup>
  import { i18n_t } from 'src/output/common/project-common.js';
  import { project_caopan_components_query_select_query_period3_select_query_period3_variable_componsable_fn } from 'project/caopan/components/query/select_query_period3/componsable/variable.js';
  import { project_caopan_components_query_select_query_period3_select_query_period3_composable_fn } from 'project/caopan/components/query/select_query_period3/componsable/index.js';

  const props = defineProps({
    right_bnt: '',
    default_history_data: [Number, String, Object],
    prop_history_date: '',
    default_select: {
      type: Number,
      default: 0,
    },
    has_border: {
      type: Boolean,
      default: true,
    },
    more_query: {
      type: Boolean,
      default: false,
    },
    icon_visible: {
      type: Boolean,
      default: false,
    },
    icon_count: {
      type: Number,
      default: 0,
    },
    tableDataCount: {
      type: Number,
      default: 0,
    },
    show_show_more_query_btn: {
      type: Boolean,
      default: true,
    },
    show_live_odd: {
      type: Boolean,
      default: false,
    },
    show_history: {
      type: Boolean,
      default: true,
    }, // 显示历史赛程
    show_yesterday: {
      type: Boolean,
      default: false,
    },
    left_btn: {
      type: Boolean,
      default: true,
    },
    total: '',
    query_times: {
      type: Number,
      default: 0,
    },
    tabledata_loading: {
      type: Boolean,
      default: true,
    },
    is_export: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['init_tabledata', 'edit_sort_status', 'sort_save', 'query_period_change', 'icon_click', 'show_more_query_change']);

  const base_payload = project_caopan_components_query_select_query_period3_select_query_period3_variable_componsable_fn();

  const {
    is_edit_sort,
    show_q_date_component,
    selected_date_tab,
    show_more_query,
    query_form_date_arr,
    history_date,
    other_date,
    date_option,
    date_option2,
    optional_events_num,
    routeName,
    hover_refresh,
  } = base_payload;

  const { handle_history_date_confirm, edit_sort_btn, sort_save, cancel_sort, toOperate, query_form_date_tab_click, startTime_limit } =
    project_caopan_components_query_select_query_period3_select_query_period3_composable_fn({
      ...base_payload,
      props,
      emit,
    });
</script>

<style lang="scss" scoped>
  @import url('project/caopan/components/query/select_query_period3/css/index-scoped.scss');
</style>
