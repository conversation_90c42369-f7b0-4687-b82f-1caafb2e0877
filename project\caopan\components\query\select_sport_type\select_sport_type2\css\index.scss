
.sport-box-item {
    padding-top: 10px;
    padding-bottom: 10px;
    .sport-item {
      width: 100%;
      text-align: left;
      padding-left: 15px;
      padding-right: 15px;
    }
  }
  .full-width--1xl {
    width: calc(100% - 1px);
  }

  .select-sport-type .panda-tab-like-tab {
    height: 34px;
    padding: 0px 10px;
  }
  .panda-query-date-tab-label{
    border:none;
    color: var(--q-color-panda-text-light) ;
    &:hover{
      color: var(--q-color-panda-primary)
    }
  }
  .panda-tab-centen-tab {
	 padding: 0 10px;
	 display: block;
	 height: 100%;
	 line-height: 34px;
	 position: relative;
}
 .panda-tab-centen-tab:hover:after {
	 content: "";
	 display: block;
	 width: 100%;
	 height: 2px;
	 background: var(--q-color-panda-primary);
	 position: absolute;
	 bottom: 0;
	 left: 0;
}
  .panda-icon-active{
      &:after {
        content: "";
        display: block;
        width: 100%;
        height: 2px;
        background: var(--q-color-panda-primary);
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  :deep( .ivu-select-dropdown){
    // width 300px
    .ivu-dropdown-menu{
      padding: 0 10px;
      width: 420px;
    }
    .ivu-dropdown-item{
      padding: 0 10px;
      display: block;
      height: 100%;
      line-height: 34px;
      text-align: center;
      font-size: 12px !important;
      color: var(--q-color-panda-text-light);
      &:hover{
        background-color: transparent !important;
        color: var(--q-color-panda-primary)
      }
    }
  }
  .dropdown-item-active{
    color: var(--q-color-panda-primary)
  }