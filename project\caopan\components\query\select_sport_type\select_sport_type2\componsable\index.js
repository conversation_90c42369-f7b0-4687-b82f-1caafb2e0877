
import lodash from 'lodash'
import { sportList } from "src/global_data_config/sport_list.js";
export function project_caopan_components_query_select_sport_type_select_sport_type2_componsable_index_componsable_fn({
    props,emit,
    sport_type_more,
    select_sportId,
    sport_type_constant,
    sport_type_constant_copy,
}) {
    /**
     * @description: 更多的显示颜色
     */
    const sport_type_more_class = (id) => {
        let list = sport_type_more.value.map((e) => {
            return e.id
        })
        return list.includes(id)
    }
    /**
     * @description: 点击赛种
     */
    const sport_type_click = (item, index) => {
        select_sportId.value = item.id;
        emit('updata_select_sportId', select_sportId.value);
    }
    const rebuild_sport_type_constant = () => {
        // 混入对象共享，exclude改变了原始值，先进行深拷贝
        sport_type_constant_copy.value = lodash.cloneDeep(
            sportList
        );
        // all有就增加一个全部
        if (props.all) {
            sport_type_constant_copy.value.unshift(
                {
                    id: -1,
                    title: i18n_t('common.all')//全部
                }
            );
        }
        let sport_arr = sport_type_constant_copy.value;
        // 如果exclude有就把他删掉，不需要
        if (props.exclude.length >= 1) {
            props.exclude.map((el) => {
                sport_arr.splice(sport_arr.findIndex(item => item.id == el), 1)
            })
        }
        sport_type_constant.value = lodash.cloneDeep(
            sport_arr
        );
        select_sportId.value = props.sportId;
    }
    rebuild_sport_type_constant();//处理数据
    return {
        sport_type_click,
        sport_type_more_class
    }
}