import { lodash } from "src/output/common/project-common.js";

export const emit_query_period_change_not_tab_8 = (payload) => {
    // 历史 数据  支持 选定 具体时间 和 起始时间 后做请求 ，避免重复请求
    const { show_live_odd, query_form_date_arr, emit } = payload
  
    let item = lodash.cloneDeep(query_form_date_arr.value);
    let val = lodash.find(item, o => {
      return o.value == selected_date_tab.value;
    });
    if (selected_date_tab.value == 9 && show_live_odd.value) {
  
      val.liveOddSupport = 1;
    }
    emit("query_period_change", val);
  }