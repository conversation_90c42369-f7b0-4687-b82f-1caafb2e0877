import { api_system } from "src/api/index.js";

export const init_serverTime = async () => {
    let res = await api_system.post_systemtypedict_getSystemTime();
    let code = lodash.get(res, "data.code");
    if (code === 200) {
        let data = lodash.get(res, "data.data");
        const get_time = arg => {
            let [y1, m1, d1, h1] = format_date_base_gmt_add_8(arg);
            let value = new Date(`${y1}-${m1}-${d1} 12:00:00`).valueOf();
            let time = value - arg;
            return time <= 0 ? time + 24 * 60 * 60 * 1000 : time;
        };
        let time;
        set_serverTime(data);
        init_query_form_date_arr();
        query_form_date_tab_click(default_select.value);
        time = get_time(data);
        clearTimeout(timer.value);
        timer.value = setTimeout(init_serverTime.value, time);
    }
}
//过滤数据列表为只显示动画下架预警数据
export const animationoOfflineClick = (isFilter) => {
    emit("animationoOfflineClick", isFilter);
    isFilter.value = isFilter;
}

export const init_query_form_date_arr = () => {
    let arr = [];
    let _isNaN;
    let [y1, m1, d1, h1, mm1, s1] = format_date_base_gmt_add_8(
        serverTime.value
    );
    // 当天中午12点
    let fmt_8_h_12 = new Date(`${y1}/${m1}/${d1} 12:00:00`).getTime();
    let t_s = ""; // 开始时间戳
    if (Number(h1) < 12) {
        // 美国的前一天 的凌晨零点   开始时间 是中国前一天的中午12点
        t_s = fmt_8_h_12 - 24 * 60 * 60 * 1000;
    } else {
        // 美国的同一天 的凌晨零点   开始时间 是中国 当天的中午12点
        t_s = fmt_8_h_12;
    }
    /**
     * 因为 t_s 转换后 日月  再 中国和美国一样的 ，所以 不在做处理
     */
    let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(t_s);

    first_day_startTimeFrom.value = t_s;

    for (let i = 0; i < 7; i++) {
        let time_str = t_s + i * 24 * 60 * 60 * 1000;
        let time = new Date(time_str);
        let [y_, m_, d_, h_, mm_, s_] = format_date_base_gmt_add_8(
            time_str
        );
        _isNaN = typeof time_str === "number" && Number.isNaN(time_str);
        arr.push({
            value: i,
            label: _isNaN ? `--${i18n_t('selectPeriodTab.Tab_1')}--${i18n_t('selectPeriodTab.Tab_2')}` :
                `${m_}${i18n_t('selectPeriodTab.Tab_1')}${d_}${i18n_t('selectPeriodTab.Tab_2')}`,//${m_}月${d_}日
            true_label: _isNaN ? `--${i18n_t('selectPeriodTab.Tab_1')}--${i18n_t('selectPeriodTab.Tab_2')}` :
                `${m_}${i18n_t('selectPeriodTab.Tab_1')}${d_}${i18n_t('selectPeriodTab.Tab_2')}`,//${m_}月${d_}日//${m_}月${d_}日
            nav_label: _isNaN ? `--${i18n_t('selectPeriodTab.Tab_1')}--${i18n_t('selectPeriodTab.Tab_2')}` :
                `${m_}${i18n_t('selectPeriodTab.Tab_1')}${d_}${i18n_t('selectPeriodTab.Tab_2')}`,//${m_}月${d_}日
            startTimeFrom: time.getTime(),
            tab: i,

            historyFlag: 0,
            week_str: format_week(time.getTime())
        });
    }

    arr[0].label = `${i18n_t('selectPeriodTab.Tab_4')}`;//"今日";
    arr[1].label = `${i18n_t('selectPeriodTab.Tab_5')}`;//"明日";
    if (!other_time_model.value) {
        arr.push({
            value: 7,
            tab: 7,
            label: `${i18n_t('selectPeriodTab.Tab_6')}`,//`其他早盘`,
            startTimeFrom: new Date(t_s + 7 * 24 * 60 * 60 * 1000).valueOf(),
            time: new Date(t_s + 7 * 24 * 60 * 60 * 1000).valueOf()
        });
    }

    if (show_live_odd.value) {
        arr.unshift({
            value: 9,
            label: `${i18n_t('common.inPlay')}`,//"滚球",
            true_label: `${m}${i18n_t('selectPeriodTab.Tab_1')}${d}${i18n_t('selectPeriodTab.Tab_2')}`,//`${m}月${d}日`,
            nav_label: `${i18n_t('common.inPlay')}`,//"滚球",
            startTimeFrom: t_s,
            tab: 9,
            historyFlag: 0,
            week_str: format_week(t_s)
        });
    }
    //昨日
    if (show_yesterday.value) {
        let [__y, __m, __d, __h, __mm, __ss] = format_date_base_gmt_add_8(
            t_s - 24 * 60 * 60 * 1000
        );
        arr.unshift({
            value: 10,
            label: _isNaN ? `--${i18n_t('selectPeriodTab.Tab_1')}--${i18n_t('selectPeriodTab.Tab_2')}` :
                `${__m}${i18n_t('selectPeriodTab.Tab_1')}${__d}${i18n_t('selectPeriodTab.Tab_2')}`,//`--月--日` : `${__m}月${__d}日`,
            true_label: _isNaN ? `--${i18n_t('selectPeriodTab.Tab_1')}--${i18n_t('selectPeriodTab.Tab_2')}` :
                `${__m}${i18n_t('selectPeriodTab.Tab_1')}${__d}${i18n_t('selectPeriodTab.Tab_2')}`,//`--月--日` : `${__m}月${__d}日`,
            nav_label: _isNaN ? `--${i18n_t('selectPeriodTab.Tab_1')}--${i18n_t('selectPeriodTab.Tab_2')}` :
                `${__m}${i18n_t('selectPeriodTab.Tab_1')}${__d}${i18n_t('selectPeriodTab.Tab_2')}`,//`--月--日` : `${__m}月${__d}日`,
            startTimeFrom: +new Date(t_s - 24 * 60 * 60 * 1000),
            tab: 10,
            historyFlag: 0,
            week_str: format_week(t_s - 24 * 60 * 60 * 1000)
        });
    }
    query_form_date_arr.value = arr
    query_form_date_arr1.value = arr
}

export const watch_date_and_change_query_form_date_arr = () => {
    let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(
        serverTime.value
    );

    current_h.value = mm;
}

export const comoute_showrightdetail_by_router = () => { }

export const handle_history_date_confirm = (payload, val) => {
    const { show_q_date_component } = payload
    show_q_date_component.value = false;
    emit_query_period_change_when_tab_8(payload);
}



export const emit_query_period_change_not_tab_8 = () => {
    // 历史 数据  支持 选定 具体时间 和 起始时间 后做请求 ，避免重复请求
    let item = lodash.cloneDeep(query_form_date_arr.value);
    let val = lodash.find(item, o => {
        return o.value == selected_date_tab.value;
    });
    if (selected_date_tab.value == 9 && show_live_odd.value) {
        val.liveOddSupport = 1;
    }
    if (selected_date_tab.value === 7 && other_time_model.value) {
        const format_date = arg => {
            let pattern = /-/g;
            let res = "";
            res = arg;
            if (typeof res === "string") {
                res = res.replace(pattern, "/");
            }
            return res;
        };
        let ts = Date.parse(format_date(other_date.value));
        if (ts) {
            let [y, m, d] = format_date_base_gmt_add_8(
                Date.parse(format_date(other_date.value))
            );
            ts = new Date(`${y}-${m}-${d} 12:00:00`).valueOf();
        }
        val = {
            value: 7,
            tab: 7,
            label: `${i18n_t('selectPeriodTab.Tab_6')}`,//`其他早盘`,
            startTimeFrom: ts,
            time: ts
        };
    }
    set_breadcrumbsPart3([val.label]); // 和头部一致
    emit("query_period_change", val);
}

export const emit_query_period_change_when_tab_8 = (payload) => {
    const { history_date, selected_date_tab, } = payload
    const format_date = arg => {
        let pattern = /-/g;
        let res = "";
        res = arg;
        if (typeof res === "string") {
            res = res.replace(pattern, "/");
        }
        return res;
    };
    let val = {};
    let ts = Date.parse(format_date(history_date.value));
    if (ts) {
        let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(
            Date.parse(format_date(history_date.value))
        );
        ts = new Date(`${y}-${m}-${d} 12:00:00`).valueOf();
    }
    set_history_startTimeFrom(ts);
    val = {
        startTimeFrom: ts
    };
    val.tab = selected_date_tab.value;
    val.historyFlag = 1;
    val.week_str = format_week(ts);
    val.value = 8;
    val.label = `${i18n_t('selectPeriodTab.Tab_11')}`//`历史赛程`;

    let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(
        Date.parse(format_date(history_date.value))
    );
    val.history_date = history_date.value;
    val.true_label = `${m}${i18n_t('selectPeriodTab.Tab_1')}${d}${i18n_t('selectPeriodTab.Tab_2')}`;// `${m}月${d}日`;
    val.nav_label = `${m}${i18n_t('selectPeriodTab.Tab_1')}${d}${i18n_t('selectPeriodTab.Tab_2')}`;//`${m}月${d}日`;
    set_breadcrumbsPart3([val.label]); // 和头部一致
    emit("query_period_change", val);
}

export const query_form_date_tab_click = (value) => {
    if (value != 7) { other_date.value = "" }
    selected_date_tab.value = value;
    if (!history_startTimeFrom.value) {
        set_history_startTimeFrom(first_day_startTimeFrom.value);
    }
    if (value == 8) {
        show_q_date_component.value = !show_q_date_component.value;
        if (route.query.history_date) {
            // history_date.value = route.query.history_date;
            // 新版时间器
            let value = route.query.startTimeFrom;
            if (value) {
                history_date.value = format_date(value);
            }
            route.query.history_date = 0;
        } else {
            if (!history_date.value) {
                history_date.value = format_date(
                    history_startTimeFrom.value || first_day_startTimeFrom.value
                );
            }
        }
        if (!history_date.value) {
            history_date.value = format_date(
                history_startTimeFrom.value || first_day_startTimeFrom.value
            );
        }
        emit_query_period_change_when_tab_8();
    } else {
        history_date.value = "";
        selected_date_tab.value = value;
        emit_query_period_change_not_tab_8();
    }
}

export const startTime_limit = (date) => {
    return (
        date >= "2018/08/01" &&
        date <= format_day(Date.now() - 12 * 60 * 60 * 1000)
    );
}

export const handle_queryform_starttime_abort = () => {
    show_q_date_component.value = false;
}

export const icon_click = (payload) => {
    const { icon_count } = payload;

    if (icon_count.value) {
        emit("icon_click");
    }
}
