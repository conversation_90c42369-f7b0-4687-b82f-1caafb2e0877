.bluedots {
    width: 6px;
    height: 6px;
    background-color: #00aa98;
    border-radius: 50%;
    display: inline-block;
    margin-right: 4px;
}

.table-f {
    border-left: 1px solid var(--q-color-panda-table-border);
    padding-bottom: 42px;
}

:deep(.q-header) {
    height: 40px;
    overflow: hidden;
}

:deep(tr.ivu-table-row-hover td) {
    background-color: var(--q-color-panda-dark-light) !important;
}

:deep(.ivu-table-row-highlight td) {
    background-color: var(--q-color-panda-dark-light) !important;
}

.table-f .bottom-page {
    border-top: 0px;
}

:deep(.ivu-select-input) {
    color: var(--q-color-panda-text-light);
    background-color: var(--q-color-panda-field-grey);
    height: 26px;
}

.tournament-option-id {
    display: inline-block;
    width: 42px;
    overflow: hidden;
    vertical-align: middle;
    margin-right: 20px;
}

:deep(.ivu-poptip-content .ivu-poptip-popper[x-placement^='bottom'] .ivu-poptip-arrow:after) {
    top: 0;
}

.index-set :deep(.ivu-input) {
    padding: 4px 5px;
    -moz-appearance: textfield;
}

.index-set :deep(.ivu-input::-webkit-outer-spin-button),
.index-set :deep(.ivu-input::-webkit-inner-spin-button) {
    -webkit-appearance: none;
}

.amend-icon {
    right: 12px;
    opacity: 0;
    line-height: 24px;
    cursor: pointer;
}

.amend-icon:hover {
    opacity: 1 !important;
}

.sortable-ghost {
    opacity: 0.4;
    background-color: #f4e2c9;
}

.operation-icon {
    margin-right: 5px;
    width: 70px;
    opacity: 0;
}

.operation-icon span {
    font-size: 17px;
    margin-right: 6px;
    cursor: pointer;
}

.operation-icon span:before {
    color: #788299;
}

.operation-icon span:hover:before {
    color: #4186d6;
}

.operation-icon:hover {
    opacity: 1;
}

.drag-srot:hover {
    text-decoration: underline;
}

:deep(.ivu-table .ivu-table-header table tr th:nth-child(6) .ivu-table-cell) {
    margin-left: 75px;
}

.set-bth {
    width: 100%;
    text-align: center;
    margin-top: 40px;
}

:deep(.ivu-input[disabled], fieldset[disabled] .ivu-input) {
    background: none;
}

.use-ivew .btn {
    line-height: 20px;
    padding: 0 10px;
    font-size: 10px !important;
}

:deep(.panda-btn-light-dense) {
    padding: 0 8px !important;
}

.table_a {
    color: red;
    padding: 0 5px
}

.list_box {
    position: relative;
    padding: 8px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--q-color-panda-text-dark);
    color: var(--q-color-panda-text-dark);
}

.list_felx {
    margin-top: 5px;
}

:deep(.ivu-poptip-inner) {
    color: var(--q-color-panda-text-light) !important;
    background: var(--q-color-panda-text-light) !important;
}