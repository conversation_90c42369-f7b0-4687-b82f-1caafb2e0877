import { watch } from 'vue';
import { get_window_size_info } from 'src/output/common/store-common.js';
import { compute_base_per_sportType_style, mutation_sport_type_constant, forceUpdate } from '../module/index';

export const watcher_register = payload => {
  const { select_sportId, route } = payload;
  return [
    // TODO: 此处完全可以使用 select_sportId 作为组件的 key，起到 select_sportId 变了组件更新
    watch(
      () => select_sportId.value,
      () => {
        // forceUpdate(payload);
      },
    ),
    watch(
      () => get_window_size_info(),
      () => {
        compute_base_per_sportType_style(payload);
      },
      { deep: true },
    ),
    watch(
      () => route.name,
      () => {
        let arr = [
          'virtual_play_management',
          'virtual_play_collection_manage_main',
          'play_management',
          'play_collection_manage_main',
          'e_play_management',
          'e_play_collection_manage_main',
        ];
        if (arr.includes(route.name)) {
          mutation_sport_type_constant(payload);
        }
      },
      { deep: true },
    ),
  ];
};
