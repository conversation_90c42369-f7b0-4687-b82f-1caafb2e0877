import { api_login, api_trader, api_order} from "src/api/index.js";
import {  lodash} from "src/output/common/project-common.js"
/**
 * @description: 待处理异常赛事 红点统计接口调用
 * @param {*}
 * @return {*}
 */
export  const getExceptionMatchSize = (payload)=>{    
  const {abnormal_event_total} = payload
  api_order.getExceptionMatchSize({sportId: -1}).then(res => {
    let { code, data, msg } = lodash.get(res, "data");
    if(code == 200) {
      data.forEach(item => {
        if(item.sportId == '-1') abnormal_event_total.value = item.exceptionMatchSize
      })

    }else {
      console.error(msg)
      // Message.error(msg)
    }
  }).catch(err => {
    console.error(err)
    // Message.error(`${err}`)
  })
}

