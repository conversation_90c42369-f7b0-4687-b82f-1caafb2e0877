import { get_log_data } from "../api-request/get_log_data";
import { getOperateType } from "../api-request/get_operate_type";
import { get_search_params } from "./params";
import {columns_1,
  columns_2,
  columns_3,
  columns_4,
  columns_5,
  } from "project/caopan/components/table/log_popup/config/set_popup.js";

    /**
     * @description: 初始化配置
     */
    export  const init = async (payload)=>{
      const {
        route,
        routerName,
        search_state,


      } = payload
      routerName.value = route.name;
      if (routerName.value == "setLog") {
        // 操作类型
        getOperateType();
        // 操作页面
        // getPageList()
      }
      await get_columns(payload); //初始配置列表columns
      if (search_state.value) {
        await get_search_params(payload); //获取搜索默认值
      } else {
        await get_log_data(payload); //如果不是搜索直接加载
      }
    }



    /**
     * @description: 初始配置列表columns
     */
    export const get_columns = (payload) => {
      const {
        route,
        routerName,

        columns,
        // columns_5,
        columnsType,
        // columns_1,
        // columns_2,
        // columns_4,
        // columns_3,


      } = payload
      let columns_ = [];
      // 先判断columnsType有没有传值是否为1 ，没有传值或传2都进行下一层判断
      // 判断route.query.columnsType 有且为1， 没有则为2
      if (routerName.value == "redcat_plays") {
        columns.value = columns_5
        return
      }
      let columnsType_1 = route.query.columnsType
        ? route.query.columnsType
        : columnsType.value;
      if (columnsType_1 == 1) {
        columns_ = columns_1;
      } else if (columnsType_1 == 2) {
        columns_ = columns_2;
      } else if (columnsType_1 == 4) {
        columns_ = columns_4;
      } else {
        columns_ = columns_3;
      }
      columns.value = columns_;
    }





    /**
     * @description: 点击确定
     */
    export const confirm = (payload) => {
      const {

        traders_log_remember,
        search_params,
        post_params,


      } = payload
      if (traders_log_remember.value) {
        localStorage.setItem(
          "traders_log_search_params",
          JSON.stringify(search_params.value)
        );
      }
      post_params.value.pageNum = 1;
      getApi();
      get_log_data();
    }
