import{get_categorySetId} from 'project/caopan/pages/alog/basketball/module/index.js';
import * as config_state from "project/caopan/pages/alog/basketball/config/config";
import {lodash,Message} from 'src/output/common/project-common.js';
  export const get_params = (payload,type) => {
    const {
      aoId,
      mode,
      input_data,
      dataSorceValue,
      CMinputParams,
      lineVersion,
      is_change,
      get_user_name,
      route,
      refreshStatus1,
      autoApply,
      autoRevValue,
      matchType,
      followedStatus,
      stageKeyArr,
      reverse
    } = payload;
    const { modeCode,} = config_state;
    let post = {
      aoMatchId: aoId.value,
      type,
      requestType: get_categorySetId(payload),
    };
    let marketParam = {
      aoMatchId: aoId.value,
      sportId: 2,
      mode: mode.value
    };
    let isSub = true;
    if(type == 'reverse'){
      marketParam = Object.assign(reverse.value, marketParam);
    }else{
      if(is_change.value){
        marketParam = Object.assign(marketParam,input_data.value);
      }else{
        // if(reverse.value.ftSup){
        //   marketParam = Object.assign(reverse.value, marketParam);
        // }else{
          marketParam = Object.assign(input_data.value, marketParam);
        // }
      }
    }

    // 版本区分
    marketParam.lineVersion = lineVersion.value ? "green" : "red";
    post.dataSourceCode = dataSorceValue.value;
    // CM输入的参数
    if (dataSorceValue.value == "CM") {
      Object.values(CMinputParams.value).forEach((item) => {
        if (isNaN(item)) {
          isSub = false;
        }
      });
      marketParam = Object.assign(marketParam, CMinputParams.value);
    }
    if (type != "reverse") {
      if (
        Math.abs(marketParam["ftSup"]) >  Math.abs(marketParam["ftGe"]) || (marketParam['ftGe'] < 22 || marketParam['ftGe'] > 400) ||
        (marketParam["ahSd"] > 50 || marketParam["ahSd"] < 2) ||
        (marketParam["ouSd"] > 50 || marketParam["ouSd"] < 2)
      ) {
        // isSub = false;
        Message.error("params wrong,plz re-input params");
        return
      }

      if (marketParam['ht1IsSelected'] === '1') {
        if (
          (marketParam["h1Sup"] === null || marketParam["h1Ge"] === null || marketParam["h1AhSd"] === null || marketParam["h1OuSd"] === null) ||
          Math.abs(marketParam["h1Sup"]) >  Math.abs(marketParam["h1Ge"]) || (marketParam['h1Ge'] < 22 || marketParam['h1Ge'] > 400) ||
          (marketParam["h1AhSd"] > 50 || marketParam["h1AhSd"] < 2) ||
          (marketParam["h1OuSd"] > 50 || marketParam["h1OuSd"] < 2)
        ) {
          console.log(941,marketParam)
          Message.error("params wrong,plz re-input params");
          return
        }
      }

      if (marketParam['q1IsSelected'] === '1') {
        if (
          (marketParam["q1Sup"] === null || marketParam["q1Ge"] === null || marketParam["q1AhSd"] === null || marketParam["q1OuSd"] === null) ||
          Math.abs(marketParam["q1Sup"]) >  Math.abs(marketParam["q1Ge"]) || (marketParam['q1Ge'] < 22 || marketParam['q1Ge'] > 400) ||
          (marketParam["q1AhSd"] > 50 || marketParam["q1AhSd"] < 2) ||
          (marketParam["q1OuSd"] > 50 || marketParam["q1OuSd"] < 2)
        ) {
          console.log(9411,marketParam)
          Message.error("params wrong,plz re-input params");
          return
        }
      }

      if (marketParam['q3IsSelected'] === '1') {
        if (
          (marketParam["q3Sup"] === null || marketParam["q3Ge"] === null || marketParam["q3AhSd"] === null || marketParam["q3OuSd"] === null) ||
          Math.abs(marketParam["q3Sup"]) >  Math.abs(marketParam["q3Ge"]) || (marketParam['q3Ge'] < 22 || marketParam['q3Ge'] > 400) ||
          (marketParam["q3AhSd"] > 50 || marketParam["q3AhSd"] < 2) ||
          (marketParam["q3OuSd"] > 50 || marketParam["q3OuSd"] < 2)
        ) {
          console.log(94111,marketParam)
          Message.error("params wrong,plz re-input params");
          return
        }
      }
      // sup和ge参数判断 绝对值 FT>HT>Q1/Q3
      if (marketParam['ht1IsSelected'] == '1' && marketParam['q1IsSelected'] == '1' && marketParam['q3IsSelected'] == '1') {
        if (
        Math.abs(marketParam["ftSup"]) <=  Math.abs(marketParam["h1Sup"]) ||
        Math.abs(marketParam["h1Sup"]) <=  Math.abs(marketParam["q1Sup"]) ||
        Math.abs(marketParam["h1Sup"]) <=  Math.abs(marketParam["q3Sup"]) ||
        Math.abs(marketParam["ftGe"]) <=  Math.abs(marketParam["h1Ge"]) ||
        Math.abs(marketParam["h1Ge"]) <=  Math.abs(marketParam["q1Ge"]) ||
        Math.abs(marketParam["h1Ge"]) <=  Math.abs(marketParam["q3Ge"])
        ) {
          Message.error("params wrong,plz re-input params");
          return
        }
      }
    }
    let modeObj = lodash.find(modeCode, item => item.mode == mode.value);
    marketParam = {...marketParam,...modeObj};
    post.marketParam = marketParam;
    post.userName = get_user_name.value
    post.matchManageId = route.query.matchManageId
    if(type == 'apply' ){
      // 比分数据apply 不需要传（跟后端开发 cason、samuel确认） 2023-2-23
      stageKeyArr.value.forEach(item => {
        delete post.marketParam[item]
      })
      // 后续增加的参数
      let add_param = {
        followedStatus: followedStatus.value,
        autoRev: refreshStatus1.value,
        autoApply: autoApply.value,
        matchUiStatus: matchType.value
      }
      post.applyParam = {...marketParam, ...add_param}

    }else if(type == 'calc'){
       post.applyParam = {
        aoMatchId: route.query.aoId,
        dataSourceCode: dataSorceValue.value,
        autoRev:autoRev.value,
        autoRevValue: autoRevValue.value,
        sportId: 2,
        matchUiStatus: matchType.value
      }
      post.applyParam = {...post.applyParam,...marketParam}
      // 比分数据apply 不需要传（跟后端开发 cason、samuel确认） 2023-2-23
      stageKeyArr.value.forEach(item => {
        delete post.applyParam[item]
      })
    }else{
      post.reverseParam = {
        aoMatchId: route.query.aoId,
        dataSourceCode: dataSorceValue.value,
        autoRev: refreshStatus1.value,
        autoApply: autoApply.value,
        autoRevValue: autoRevValue.value,
        sportId: 2,
        quarters: mode.value?mode.value.slice(0,1):'',
        quarterMin: mode.value?mode.value.slice(1,3):'',
        lineVersion : lineVersion.value ? "green" : "red",
        matchUiStatus: matchType.value,
        ht1IsSelected: reverse.value.ht1IsSelected || '0',
        q1IsSelected:reverse.value.q1IsSelected || '0',
        q3IsSelected:reverse.value.q3IsSelected || '0'
      }
    }
    post.userName = get_user_name.value;
    delete post.marketParam
    console.log(post,reverse.value,'get_params')
    if (isSub) {
      return post;
    } else {
      // 参数输入错误，请修改后重试
      Message.error(i18n_t("credit_controls_page.market_8"));
      return null;
    }
  }
