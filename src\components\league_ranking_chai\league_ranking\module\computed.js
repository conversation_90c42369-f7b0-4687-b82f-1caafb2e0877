/**
 * @description: 初始列表处理数据、
 * @param {*} arr
 * @return {*}
 */
export const computed_table = (payload, arr) => {
  const { params } = payload;
  let list = arr;
  list.map((item, index) => {
    let index_ = index + 1 + (params.value.start - 1) * params.value.size;
    item.sort_index = index_;
    item.orderNo_10 = item.sort; //排序值
    item.set = false;
    item.versionNewStatus = item.versionNewStatus ? true : false; //是否展示
    item.swith_loading = false;
    let topShowTime = item.topShowTime || null;
    let time = topShowTime ? topShowTime.split("-") : "";
    item.endTime = time[1] || "";
    item.startTime = time[0] || "";
    item.isEdit = false;
  });
  return list;
};
