import { api_system_set } from "src/api/index.js";
import { Message, lodash, i18n_t } from "src/output/common/project-common.js";
const get_data_ok = (payload, res) => {
  const { table_list, total, qjStatus } = payload;
  let { code, data, msg } = lodash.get(res, "data");
  if (code == 200) {
    let tabel = JSON.parse(JSON.stringify(data.list)) || [];
    tabel.forEach((item) => {
      // 金额区间
      if (item.maxMoney && item.minMoney) {
        item.bettingRange = `${item.minMoney}-${item.maxMoney}`;
      } else if (!item.maxMoney && !item.minMoney) {
        item.bettingRange = `${i18n_t("common.nothing")}`;
      } else {
        item.bettingRange = item.maxMoney
          ? `${item.maxMoney}${i18n_t("basic_setting.setting_14")}`
          : `${item.minMoney}${i18n_t("basic_setting.setting_15")}`;
      }
      return;
    });
    table_list.value = tabel;
    total.value = data.total;
    qjStatus.value = data.rcsSwitch.switchStatus;
  }
};
// 列表数据获取
export const get_data = async (payload) => {
  const { tabledata_loading, params } = payload;
  tabledata_loading.value = true;
  let param = Object.assign(params.value);
  try {
    let res = await api_system_set.get_data_setting(param);
    get_data_ok(payload, res);
  } catch (err) {
    console.log(err);
    Message.error(err);
  } finally {
    tabledata_loading.value = false;
  }
};
