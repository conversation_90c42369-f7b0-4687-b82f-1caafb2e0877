
import {ref,computed,watch,reactive,onMounted,onUnmounted} from 'vue'

import { init } from  "project/caopan/components/table/log_popup/module/process.js"

import { get_sportList ,changea , remember_func} from  "project/caopan/components/table/log_popup/module/index.js"

import { change_select,clear_params ,change_page_num ,change_page_size} from  "project/caopan/components/table/log_popup/module/params.js"


export  const  project_caopan_components_table_log_popup_componsable_fn=(raw_payload)=>{

const payload={
  ...raw_payload
}

  onMounted(()=>{
    init(payload); //初始请求
    get_sportList(payload);
  })


  return {
change_select : (value, key)=>change_select(payload,value, key),
changea : (value)=>changea(value),
clear_params : ()=>clear_params(payload),
remember_func : (value)=>remember_func(value),
change_page_num : (value)=>change_page_num(payload,value),
change_page_size : (value)=>change_page_size(payload,value),

  }
}
