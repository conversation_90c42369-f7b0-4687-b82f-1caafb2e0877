import {ref,computed,watch,reactive,onMounted,onUnmounted, inject} from 'vue'
import {
  set_get_r_list,
  set_sportType_array,
  set_resp_serverTime,
  set_get_getByMultilingualism,
  set_get_playAllList,
  set_champion_activem,
  set_redcat_activem,
  get_user_info
} from "src/output/common/store-common.js"

import { lodash} from "src/output/common/project-common.js"

import {get_menudata} from "project/caopan/layouts/module/left_menu.js"
import {change_theme,compute_container_style,compute_active_menu_level, onResize } from "project/caopan/layouts/module/style.js"
import {should_click, handle_mouseenter, change_env, change_loge_url} from "project/caopan/layouts/module/common.js"


import {getExceptionMatchSize} from "project/caopan/layouts/api-request/left_menu.js"
import { login_out, get_bet_cancel_total, getUnsettleMatchSize, marquee_click} from "project/caopan/layouts/api-request/header.js"




export const layout_composable_fn = (payload) => {


    let onResize_fn;
    const {
      i18nmodel,
      timer,
      // 同步服务器时间定时器
      timer1,
      userName,
      route
    } = payload

    onMounted(() => {
      try {

        // 权限 初始化菜单
        get_menudata(payload)
        i18nmodel.value = sessionStorage.getItem("i18n") || "zs";
        //菜单样式选中
        compute_active_menu_level(payload, route);
        //获取基础设置配置  马来 ，欧赔等 赔率 转换
        set_get_r_list();
        // 这个地方 直接设置值 存在两次渲染的BUG  需要判断路由     TODO-JINNIAN
        // 设置 球类 枚举 数组
        set_sportType_array();
        userName.value = get_user_info().value.user.userCode || "";
        let theme = localStorage.getItem("Skin") || 'black'
        console.warn('~~~~~~')
        console.log(theme)
        change_theme(payload, theme);
        // 同步服务器时间

        set_resp_serverTime();
        timer.value = setInterval(() => {
          set_resp_serverTime();
        },300000);
        // 查询玩法名
        set_get_getByMultilingualism()
        // 立即调用红点接口
        get_bet_cancel_total(payload)
        // 异常待处理赛事
        getExceptionMatchSize(payload)
        // 未结算赛事
        getUnsettleMatchSize(payload)
        // 冠军 红点
        set_champion_activem()
        // 红猫 红点
        set_redcat_activem()
        // 查询玩法名称id所对应的
        set_get_playAllList()

        compute_container_style(payload);
     
        // 添加节流函数
        onResize_fn = lodash.throttle(() => onResize(payload), 1500)
        window.addEventListener('resize', onResize_fn)
        window.addEventListener('storage', change_loge_url)
        // 页面齁起 计时同步
        window.addEventListener('visibilitychange', set_resp_serverTime)
        // 打开A01面板的统计对象
        window.window_obj = {}
      } catch (error) {
        console.error(error)
      }
    })

    onUnmounted(() => {
       // 释放节流内存
      // onResize_fn.cancel()
      window.removeEventListener('resize', onResize_fn)
      window.removeEventListener('storage', change_loge_url)
      window.removeEventListener('visibilitychange', set_resp_serverTime)
      // 清空计时器
      clearInterval(timer.value);
      clearInterval(timer1.value);
      window.window_obj = null;
    })

    return {
      should_click: () => should_click(payload),
      handle_mouseenter,
      marquee_click: (item) => marquee_click(item),
      change_env: (e) => change_env(e),
      login_out,
      change_theme: data => change_theme(payload, data),
      compute_container_style: (size) => compute_container_style(payload, size )
    }
}
