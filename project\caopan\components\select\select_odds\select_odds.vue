<!--
 * @FilePath: /project/caopan/components/select/select_odds.vue
 * @Description:
-->
<template>
  <q-select
    filled
    rounded
    :disable="get_trader_tab_status.liveOddBusiness ? true : false"
    class="w120x q-mr-lg"
    color="panda-text-grey"
    bg-color="panda-base-dark"
    v-model="model"
    hide-bottom-space
    :options="odds_select_constant"
    emit-value
    map-options
    option-value="value"
    option-label="label"
    dense
    options-dense
    @input="modify_odds_fields"
    popup-content-class="bg-panda-base-dark text-panda-text-dark"
  >
    <template #prepend>
      <span class="p1-df"
        ><img
          :src="`/img/${(model
            ? `${odds_icon_constant[model]}`
            : ''
          ).toLowerCase()}.png`"
          style="width: 14px"
          alt=""
      /></span>
    </template>
    <template #option="scope">
      <q-item v-bind="scope.itemProps" v-on="scope.itemEvents" class="row">
        <q-item-section avatar class="col-2 rml5x">
          <span
            ><img
              :src="`/img/${(model
                ? `${odds_icon_constant[scope.opt.value]}`
                : ''
              ).toLowerCase()}.png`"
              style="width: 14px; margin-left: 5px"
              alt=""
          /></span>
        </q-item-section>
        <q-item-section class="ml5x">
          <q-item-label v-html="scope.opt.label" />
          <q-item-label caption>{{ scope.opt.description }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<script setup>
// import { get } from "http";
import { openURL, SessionStorage, LocalStorage } from "quasar";
import { project_caopan_components_select_select_odds_componsable_variable_componsable_fn } from "project/caopan/components/select/select_odds/componsable/variable.js";

const base_state =
  project_caopan_components_select_select_odds_componsable_variable_componsable_fn();

const { model, flag } = base_state;

const base_payload = {
  ...base_state,
};

import { project_caopan_components_select_select_odds_componsable_index_componsable_fn } from "project/caopan/components/select/select_odds/componsable/index.js";
const { modify_odds_fields } = project_caopan_components_select_select_odds_componsable_index_componsable_fn(base_payload);
</script>

<style lang="scss" scoped>
.icon {
  line-height: 20px;
  font-size: 8px;
}
:deep( .q-field__native ){
  color: #788299;
}
</style>
