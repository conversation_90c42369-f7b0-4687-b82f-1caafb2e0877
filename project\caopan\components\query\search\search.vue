<template>
  <div :class="css_prefix">
    <iInput
      v-bind="attrs"
      v-on="listeners"
      @on-search="close_drop_down"
      @on-focus="open_drop_down"
      @on-blur="close_drop_down"
      :value="value"
    />
    <ul
      #list
      v-show="visible"
      :class="[`${css_prefix}__option`, 'ivu-select-dropdown']"
    >
      <li
        v-for="(item, index) in options"
        :key="index"
        class="ivu-select-item"
        @mousedown="handle_click(item)"
      >
        {{ item.label }} {{ value }}
      </li>
    </ul>
  </div>
</template>

<script setup>
// 组合函引入
import { project_caopan_components_query_search_componsable_fn } from 'project/caopan/components/query/search/componsable/index.js';
import { project_caopan_components_query_search_variable_componsable_fn } from 'project/caopan/components/query/search/componsable/variable.js';
/**
 * 定义emit 
 */
const emit = defineEmits(["on-select"]);
/***
 * 获取props
 */
const props = defineProps({
  value: null,
  options: {
    type: Array,
    default: () => []
  }
});
/**
 * 获取base_payload
 */
const base_payload = project_caopan_components_query_search_variable_componsable_fn();

/***
 * 获取 组装payload
 */
const {
  css_prefix,
  visible,
  attrs,
  listeners
} = {
  ...base_payload
}


/**
 * 处理payload数据
 */
const raw_payload = {
  ...base_payload,
  emit,
};
/**
 * 初始化 组合式 事务
 */
const {
  close_drop_down,
  open_drop_down,
  handle_click
} = project_caopan_components_query_search_componsable_fn(raw_payload)
</script>

<style lang="scss">
@import "./css/index.scss";
</style>
