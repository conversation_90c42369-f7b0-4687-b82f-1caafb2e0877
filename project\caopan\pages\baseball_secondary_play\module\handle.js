/**
 * @description: 数据处理
 * @param {*} data
 * @return {*}
 */
export const sort_data = (payload, data) => {
  const { route } = payload;
  let { playSetId } = route.query;
  data.forEach(item => {
    // 把数据处理成空数组
    if (!item.playInfoList) {
      item.playInfoList = [];
    }
    // 玩法
    item.playInfoList.forEach(item1 => {
      // 变量玩法的盘口数 需要控制在次级玩法盘口数级别里面去
      if (item1.matchMarketVoList[0] && item1.matchMarketVoList[0].childMarketCategoryId) {
        // 变量玩法
      } else {
        // 常规玩法
        // 最大盘口数量
        item1.matchMarketVoList = item1.matchMarketVoList.slice(0, item1.marketCount);
      }
      // 变量玩法 盘口排序
      if (item1.isChildCategory) {
        item1.matchMarketVoList.sort((x, y) => {
          let x_sort = Number(x.childMarketCategoryId);
          let y_sort = Number(y.childMarketCategoryId);
          return x_sort - y_sort;
        });
      }
      // 盘口
      if (item1.matchMarketVoList) {
        // 投注项
        item1.matchMarketVoList.forEach(item2 => {
          if (item2.oddsFieldsList) {
            sort_oddsFieldsList_con(payloda, item2.oddsFieldsList, item1.id);
          }
        });
      }
    });
    // 过滤掉没有盘口的玩法
    item.playInfoList = item.playInfoList.filter(p_item => p_item && p_item.matchMarketVoList && p_item.matchMarketVoList.length > 0);
  });
  if (playSetId) {
    return data.filter(item => item.playSetId == playSetId);
  }
  return data;
};
export const handle_cancle = payload => {
  const { open_show, playSetScore } = payload;
  open_show.value = false;
  playSetScore.value = "";
};
export const handle_confirm = payload => {
  const { open_show, open_show_params } = payload;
  change_play_status(payload, "", "", "", "", "", open_show_params.value);
  open_show.value = false;
};
export const set_timer = (payload, item) => {
  // 设置定时器secondsMatchStart字段
  const { settime } = payload;
  let data = item.item;
  if (data.secondsMatchStart && ![301, 302, 303, 304, 100].includes(item.value.matchPeriodId)) {
    if (!data.flag_) {
      settime.value = setTimeout(() => {
        //开启倒计时
        if (data.secondsMatchStart > 0) {
          data.secondsMatchStart--;
          data.flag_ = false;
        }
      }, 1100);
    }
    data.flag_ = true;
  } else {
    clearTimeout(settime.value);
  }
  return get_time(data);
};
