<template>
  <q-tr :props="table_props" class="panda-table bg-panda-base-dark" :class="{'highlight' : table_props.row.matchInfoId == actived_matchInfoId}" style="height:40px;">
    <!-- 左侧颜色 -->
    <q-td key="before" :style="compute_table_col_before_style(table_props.row, 'before')">
      <!-- :style="compute_table_col_before_style(table_props.row, 'before')" -->
    </q-td>
    <!-- 序号 -->
    <q-td class="panda-table" :props="table_props" key="id" style="padding-left: 6px;">
      <span v-if="table_props.row.favoriteStatus === 1" class="collection panda_icon_sc_active cp"
        @click="toggleCollect(table_props.row, 1, table_props.row.serialNumer - 1)"></span>
      <span v-if="table_props.row.favoriteStatus === 0" class="collection panda_icon_zj"></span>
      <span class="collection panda_icon_sc cp" v-if="table_props.row.favoriteStatus === 2"
        @click="toggleCollect(table_props.row, 1, table_props.row.serialNumer - 1)"></span>
      {{ (queryform_form.currentPage - 1) * queryform_form.pageSize + table_props.row.serialNumer }}
    </q-td>
    <!-- 排序值 -->
    <q-td class="panda-table" :props="table_props" key="sortValue" style="padding-left: 6px;">
      <span>
        <i-input v-if="sort_status" v-model="table_props.row.sortValue" @blur="blur_set_data($event, table_props.row)"
          @keypress="build_sort_data($event, table_props.row)" class='numberInput' />
      </span>
    </q-td>
    <!-- 加号 -->
    <q-td class="panda-table" :props="table_props" key="showRelation"
      @click="related_events_click(table_props.row)">
      <div v-if="table_props.row.relatedMatch && table_props.row.relatedMatch.length > 0">
        <span v-if="!table_props.row.showRelation" class="icon panda_icon_add_primary"></span>
        <span v-else class="icon panda_icon_remove_light"></span>
      </div>
    </q-td>
    <!-- 联赛名 -->
    <q-td class="panda-table no-padding panda-table-col-text-indent" key="tournamentNames" :props="table_props">
      <div class="row" style="flex-wrap: nowrap;"
        :class="table_props.row.tournamentFavoriteStatus == 1 ? 'yellow-color' : ''"
        @dblclick="toggleCollect(table_props.row, 2)">
        <div class="ellipsis2">
          {{ compute_team_table_item_show_name(table_props.row.leagueName) }}
          <q-tooltip anchor="bottom middle" self="center middle" :offset="[0, 10]">
            <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
            <div>
              {{ table_props.row.tournamentFavoriteStatus == 1 ? i18n_t("saleTable.sa_82") :
                i18n_t("saleTable.sa_81") }}（{{ table_props.row.tournamentId }}）<!-- 双击取消收藏联赛  双击收藏联赛-->
            </div>
          </q-tooltip>
        </div>
        <div v-if="table_props.row.tournamentChangeStatus == 1">
          <span class="waring-icon"></span>
          <q-tooltip anchor="bottom middle" self="bottom middle" :offset="[30, 30]">
            <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
            <span>{{ i18n_t("saleTable.sa_2927_1") }}</span>
          </q-tooltip>
        </div>
      </div>
    </q-td>
    <!-- 模板等级 (有操盘的球类展示模板等级 其余的展示联赛等级)-->
    <q-td class="panda-table" :props="table_props" key="templateLevel">
      <!-- v-if="caopan_sportId.includes(select_sportId)" -->
      <span v-if="true">
        <!-- 英文    由于中英文 等级 文字顺序不同  所以中英文分开展示  sa_228 级   sa_227专用    text28无-->
        <span v-if="compute_i18n_type"> {{
          table_props.row.templateLevel == "-1"
            ? (table_props.row.tournamentLevel && table_props.row.tournamentLevel > 0) ?
              `${i18n_t("saleTable.sa_228")} ${table_props.row.tournamentLevel} ${i18n_t("saleTable.sa_227")}` :
              `${i18n_t("champion_settlement.text28")} ${i18n_t("saleTable.sa_227")}`
            : table_props.row.templateLevel == "0" ? i18n_t("champion_settlement.text28") : `${i18n_t("saleTable.sa_228")}
          ${table_props.row.templateLevel}` }}
        </span>
        <!-- 中文-->
        <span v-else>{{
          table_props.row.templateLevel == "-1"
            ? (table_props.row.tournamentLevel && table_props.row.tournamentLevel > 0) ?
              `${table_props.row.tournamentLevel}${i18n_t("saleTable.sa_228")} ${i18n_t("saleTable.sa_227")}` :
              `${i18n_t("champion_settlement.text28")} ${i18n_t("saleTable.sa_227")}`
            : table_props.row.templateLevel == "0" ? i18n_t("champion_settlement.text28") : table_props.row.templateLevel
              +
              i18n_t("saleTable.sa_228") }}
        </span>
      </span>
      <span v-else>
        <!-- 英文    由于中英文 等级 文字顺序不同  所以中英文分开展示  sa_228 级   sa_227专用    text28无-->
        <span v-if="compute_i18n_type">
          {{ table_props.row.tournamentLevel && table_props.row.tournamentLevel > 0 ? `${i18n_t("saleTable.sa_228")}
          ${table_props.row.tournamentLevel}` : i18n_t('champion_settlement.text28') }}
        </span>
        <!-- 中文-->
        <span v-else>
          {{ table_props.row.tournamentLevel && table_props.row.tournamentLevel > 0 ? table_props.row.tournamentLevel +
            i18n_t('saleTable.sa_228') : i18n_t('champion_settlement.text28') }}
        </span>
      </span>
    </q-td>
    <!-- 开赛时间 -->
    <q-td class="panda-table panda-table-col-text-indent beginTime" :props="table_props" key="beginTime"
      :style="compute_table_col_width('beginTime')">
      <!-- 赛事阶段       赛事状态为 滚球 暂停 中断 的情况 显示赛事阶段   其它显示赛事状态 -->
      <div class="dib" style="position:relative;left:-8px;cursor: pointer;" @click="build_match_click(table_props.row)">
        <matchPeriod class="dib" style="position:relative;left:-8px;top:2px;" :sportId="table_props.row.sportId"
          :items="table_props.row" />
      </div>
      <!-- 完赛确认 END -->
      <span @click="end_click(table_props.row)"
        v-if="table_props.row.beginTime < serverTime && table_props.row.status == 'Enable'"
        class="icons panda_end"></span>
      <!-- 重新开赛 R -->
      <span @click="r_click(table_props.row)" v-if="table_props.row.matchStatus != 3 &&
        table_props.row?.matchStatus != 4 &&
        table_props.row?.status == 'End' &&
        table_props.row?.beginTime < serverTime &&
        (serverTime - table_props.row?.beginTime < 48 * 60 * 60 * 1000)" class="icons panda_r">
      </span>
    </q-td>
    <!-- 主队名称 -->
    <q-td class="panda-table" :props="table_props" key="homeTeamNames"
      :style="compute_table_col_width('homeTeamNames')">
      <div class="row" style="flex-wrap: nowrap;">
        <div v-if="isMatchupChange(table_props.row)" class="mt5x ml5x">
          <span class="waring-icon"></span>
          <q-tooltip anchor="bottom middle" self="bottom middle" :offset="[30, 30]">
            <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
            <span>{{ table_props.row.teamChangeStatus ==
              1 ? i18n_t('saicheng.manage_operation.operation41') : i18n_t("saleTable.sa_2927_2") }}</span>
          </q-tooltip>
        </div>
        <table-cell-ellipsis @click="p_ups(table_props.row)"
          :tooltip_position="{ anchor: 'bottom left', self: 'center left' }"
          :str_all="compute_team_table_item_show_name(table_props.row.homeTeamNames)"></table-cell-ellipsis>
      </div>
    </q-td>
    <!-- 比分 -->
    <q-td class="panda-table panda-table-col-text-indent " :props="table_props" key="score"
      :style="compute_table_col_width('score')" @click="go_event_analyse(table_props.row)"
      :class="table_props.row?.status == 'End' ? 'cp' : ''">
      <scoreItem :row_data="table_props.row"></scoreItem>
      <q-tooltip anchor="bottom middle" self="center right"
        v-if="table_props.row?.status == 'End' && table_props.row.sportId == 1"
        content-style="margin-left: 11px !important;">
        <div class="q-tooltop-arrow" x-placement="bottom"></div>
        {{ i18n_t('eventsAnalyse.t1') }}
        <!-- 单击可进行赛事复盘 -->
      </q-tooltip>
    </q-td>
    <!-- 客队名称 -->
    <q-td class="panda-table" :props="table_props" key="awayTeamNames"
      :style="compute_table_col_width('awayTeamNames')">
      <div class="row" style="flex-wrap: nowrap;">
        <div v-if="isMatchupChange(table_props.row)" class="mt5x ml5x">
          <span class="waring-icon"></span>
          <q-tooltip anchor="bottom middle" self="bottom middle" :offset="[30, 30]">
            <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
            <span>{{ table_props.row.teamChangeStatus ==
              1 ? i18n_t('saicheng.manage_operation.operation41') : i18n_t("saleTable.sa_2927_2") }}</span>
          </q-tooltip>
        </div>
        <table-cell-ellipsis @click="p_ups(table_props.row)"
          :tooltip_position="{ anchor: 'bottom left', self: 'center left' }"
          :str_all="compute_team_table_item_show_name(table_props.row.awayTeamNames)"></table-cell-ellipsis>
      </div>
    </q-td>
    <!-- 中立场 -->
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="neutralGround"
      :style="compute_table_col_width('neutralGround')">
      <span>{{ table_props.row.neutralGround == 1 ? "N" : "-" }}</span>
    </q-td>
    <!-- 商业数据源 -->
    <q-td class="panda-table" :props="table_props" key="businessEventList">
      <div class="BusinessSources">
        <span
          v-for="(item, index) in business_dataSourceCode.map(x => (table_props.row.businessEventList || []).includes(x) ? x : '')"
          :key="index"
          :class="[(table_props.row.oddsDataSourceList || []).includes(item) ? 'panda-text-orange' : '', 'p1-w--9']">{{
            item
          }}</span>
      </div>
    </q-td>
    <!-- 竞品数据源 -->
    <q-td class="panda-table" :props="table_props" key="unCommerceDataCode">
      <span>{{ table_props.row.unCommerceDataCode }}</span>
    </q-td>
    <!-- 早盘操盘手 -->
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="preTrader"
      :style="compute_table_col_width('preTrader')">
      <div class="flex just-between pr8x">
        <div class="panda-row-td-box" style="flex:1">
          <span
            v-if="btn_permissions('sale:table:preTrader:view') && (table_props.row.preTrader || table_props.row.preRiskManagerCode)"
            class="pan-dont-basic" :class="computed_trader_row_dot(table_props.row.preMatchSellStatus)"
            style="margin-right: 4px"></span>
          <span style="margin-right: 10px"
            v-if="btn_permissions('sale:table:preTrader:view') && table_props.row.preTrader">{{
              table_props.row.preTrader }}</span>
          <!-- 逾期 -->
          <span class="red-color"
            v-if="btn_permissions('sale:table:preTrader:view') && table_props.row.preMatchSellStatus == 'Overdue_Unsold'">{{
              i18n_t('saleTable.sa_16') }}</span>
          <span v-if="btn_permissions('sale:table:xts:view') && is_new_trader(table_props.row.preRiskManagerCode)"
            :disabled="matchStatus == 'End' || table_props.row.oddsLive == 1 ||
              computed_weight_value(table_props.row) == 'OD'" @click="(matchStatus == 'End' || table_props.row.oddsLive == 1 ||
              computed_weight_value(table_props.row) == 'OD') ? '' : change_preRiskManagerCode(table_props.row)"
            style="float: right;color: #4186d6;" class="cp">{{ table_props.row.preRiskManagerCode }}</span>
          <span class="material-icons cursor-pointer"
            v-if="btn_permissions('sale:table:preTrader:view') && get_user_power(table_props.row.preTrader)"
            @click="$emit('vieworAddTrader', { type: 0, row: table_props.row, rowIndex: table_props.rowIndex })"
            style="width: 18px;font-size: 20px; margin-right: 5px;color:#f59a23">person_add</span>
          <span class="material-icons cursor-pointer"
            v-if="btn_permissions('sale:table:preTrader:view') && table_props.row.preTrader"
            @click="$emit('vieworAddTrader', { type: 1, row: table_props.row, rowIndex: table_props.rowIndex })"
            style="width: 18px;font-size: 20px;color:#f59a23">library_books</span>
        </div>
      </div>
    </q-td>
    <!-- 结算审核 -->
    <q-td class="panda-table">
      <span v-if="btn_permissions('settlements1.0:view')" class="panda-btn-table-handle panda-btn-sale"
        @click="settlements_path(table_props.row)">{{ i18n_t('saleTable.sa_240') }}</span>
    </q-td>
    <!-- 滚球 -->
    <q-td class="panda-table" :props="table_props" key="liveOddSupport"
      :style="compute_table_col_width('liveOddSupport')">
      <span class="panda_icon_kgqp vertical-middle" style="height: 14px; font-size: 14px;position: relative"
        v-if="table_props.row.supportLiveList && table_props.row.supportLiveList.length > 0"
        :class="table_props.row.oddsDataSourceList && table_props.row.oddsDataSourceList.length > 0 ? 'panda-icon-green2' : 'panda-icon-base'">
        <!-- sa_229:已订阅，数据正常   sa_230: 已订阅，无数据  panda-icon-green2 绿色高亮 -->
        <table-cell-ellipsis style="font-size:0;width:15px;position:absolute;left:0;top: 0;height:28px"
          :str_all="table_props.row.oddsDataSourceList && table_props.row.oddsDataSourceList.length > 0 ? i18n_t('saleTable.sa_229') : i18n_t('saleTable.sa_230')"
          :placement="'bottom'"
          :tooltip_position="{ anchor: 'bottom middle', self: 'center middle' }"></table-cell-ellipsis>
      </span>
    </q-td>
    <!-- 主数据源  数据源权重设置 -->
    <q-td class="panda-table" :props="table_props" key="sourceWeight">
      <div class="BusinessSources">
        <span class="ml20x mr10x">{{ computed_weight_value(table_props.row) }}</span>
        <span v-if="!is_new_trader(table_props.row.preRiskManagerCode) &&
          table_props.row?.status == 'Enable' &&
          table_props.row?.businessEventList &&
          table_props.row?.businessEventList.length > 1 &&
          !table_props.row?.oddsLive &&
          table_props.row?.beginTime > serverTime" class="icon panda_icon_edit_light"
          @click="sourceWeightChange(table_props.row)">
        </span>
      </div>
    </q-td>
    <!-- 已开售 待开售 -->
    <q-td class="panda-table" :props="table_props" key="soldMartketCategoryCount" style="padding-left: 8px">
      <template
        v-if="is_new_trader(table_props.row.preRiskManagerCode) && (table_props.row.preMatchSellStatus == 'Unsold' || table_props.row.preMatchSellStatus == 'Overdue_Unsold')">
        <span v-if="table_props.row.beginTime > serverTime && btn_permissions('sale:table:sold:view')"
          class="sale yellow-color ml20x" @click="mts_sold(table_props.row)">
          {{ i18n_t('saleTable.sa_14') }}
        </span><!-- 开售 -->
      </template>
      <template v-else>
        <span v-if="btn_permissions('sale:table:sold:view')">
          <span style="display: inline-block; width: 30px;text-align: right">{{ table_props.row.soldMartketCategoryCount
            ||
            0}}</span> /
          <span class="sale yellow-color" style="width: 25px;" @click="toPlaySalePage(table_props.row)">{{
            table_props.row.preSellMartketCategoryCount || 0 }}</span><!-- 进入玩法开售页面 -->
        </span>
      </template>
      <!-- 同步新玩法按钮 历史赛事不展示此按钮-->
      <span v-if="matchStatus != 'End' && btn_permissions('sale:table:sold:view')" style="width:15px;height:12px;"
        class="dib tac ml3x" @mouseenter="hover_play_icon = true" @mouseleave="hover_play_icon = false">
        <span v-if="hover_play_icon" class="panda_icon_refresh refresh_hover" :class="loading_play ? 'newActive' : ''"
          @click="refresh_play(table_props.row, 1)"></span>
      </span>
    </q-td>
    <!-- 早盘开售时间 -->
    <q-td class="panda-table" :props="table_props" key="preMatchTime" :style="compute_table_col_width('preMatchTime')">
      <span v-if="table_props.row.preMatchTime" class="RobotoBold1 vertical-middle"
        style="height: 13px; font-size: 13px;">{{ preMatchTime(table_props.row.preMatchTime) }}</span>
      <span v-else>-</span>
    </q-td>
    <!-- 开售状态 -->
    <q-td class="panda-table" :props="table_props" key="preMatchSellStatus">
      <span :class="table_props.row.preMatchSellStatus == 'Sold' ? 'yellow-color' : ''">
        {{match_sell_status_arr.filter(item => item.value == table_props.row.preMatchSellStatus)[0].label}}
      </span>
    </q-td>
    <!-- 角球展示  仅足球有 -->
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="cornerShow"
      :style="compute_table_col_width('cornerShow')" v-if="select_sportId == 1">
      <iSwitch true-value="1" false-value="0"
        :disabled="matchStatus == 'End' || !btn_permissions('sale:table:switch:click')" v-model="corner"
        @on-change="settingConfig(table_props.row)" v-if="btn_permissions('sale:table:switch:view')"></iSwitch>
    </q-td>
    <!-- 罚牌展示  仅足球有 -->
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="cardShow"
      :style="compute_table_col_width('cardShow')" v-if="select_sportId == 1">
      <iSwitch true-value="1" false-value="0"
        :disabled="matchStatus == 'End' || !btn_permissions('sale:table:switch:click')" v-model="card"
        @on-change="settingConfig(table_props.row)" v-if="btn_permissions('sale:table:switch:view')"></iSwitch>
    </q-td>
    <!-- 赛事状态源 -->
    <q-td class="panda-table" :props="table_props" key="matchStatusSourceCode">
      <div class="p1-df p1-h--3 cp" @click="edit_Maths_sale(2, table_props.row)">
        <span v-if="table_props.row.matchStatusSourceCode"
          :class="[`p1-df p1-aic p1-border p1-mr--5`, match_status_dataSourceCode_border_color[table_props.row.matchStatusSourceCode]]"
          style="padding:0 10px;border-radius: 2px;">
          {{ compute_dataSourceCode_fullname(table_props.row.matchStatusSourceCode) }}
        </span>
        <span class="p1-df p1-fdc p1-jcsa">
          <span
            v-for="(item, index) in table_props.row.dataSourceCode == 'PD' ? table_props.row.businessEventList : table_props.row.businessEventList.filter(x => x != 'PD') || []"
            :key="index" :class="['pan-dont-basic', match_status_dataSourceCode_dot_color[item]]"></span>
        </span>
      </div>
    </q-td>
    <!-- 赛事ID -->
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="matchManageId"
      :style="compute_table_col_width('matchManageId')">
      <span v-if="table_props.row.matchManageId" @click="copy_id(table_props.row.matchManageId)" class="cp">{{
        table_props.row.matchManageId
      }}</span>
      <span v-else>-</span>
      <span class="cp" v-if="table_props.row.linkMatchManageId" @click="copy_id(table_props.row.linkMatchManageId)">{{
        table_props.row.linkMatchManageId ? `(${i18n_t("broadcast_manage.column6")})` : '' }}</span>
      <q-tooltip v-if="table_props.row.linkMatchManageId" anchor="bottom middle" self="bottom middle"
        :offset="[30, 43]">
        <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
        <div style="font-size:13px;">{{ table_props.row.matchManageId }}({{ i18n_t("broadcast_manage.column_3636_1") }})
        </div>
        <div style="font-size:13px;">{{ table_props.row.linkMatchManageId }}({{ i18n_t("broadcast_manage.column6") }})
        </div>
      </q-tooltip>
    </q-td>
    <!-- 操作人变更 -->
    <q-td class="panda-table" :props="table_props" key="traderChange" :style="compute_table_col_width('traderChange')">
      <span class="panda-btn-table-handle panda-btn-sale" @click="log_click(table_props.row)">{{
        i18n_t('saleTable.sa_97')
        }}</span><!-- 查看日志 -->
      <!-- 只有足蓝有操作日志 -->
      <span v-if="[1, 2].includes(table_props.row.sportId)" class="panda-btn-table-handle panda-btn-sale"
        @click="open_log(table_props.row)">{{ i18n_t("champion_plays.text42") }}</span>
    </q-td>
  </q-tr>
  <!-- 多的 -->
  <q-tr v-table_fixed_columns="{ left: 8 }" :props="props" class="panda-table bg-panda-dark-dark" v-for="(item, index) in table_props.row.showRelation
    ? table_props.row.relatedMatch
    : []" :key="`related_match_${index}_${new Date().getTime()}`">
    <q-td key="before" :style="compute_table_col_before_style(table_props.row, 'before')"></q-td>
    <q-td class="panda-table" :props="table_props" key="id"></q-td>
    <q-td class="panda-table" :props="table_props" key="showRelation"></q-td>
    <q-td class="panda-table no-padding" key="tournamentNames" :props="table_props">
      <table-cell-ellipsis :str_all="compute_team_table_item_show_name(item.tournamentNames)"
        :tooltip_position="{ anchor: 'bottom left', self: 'center left' }"></table-cell-ellipsis>
    </q-td>

    <q-td class="panda-table" :props="table_props" key="templateLevel"></q-td>
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="beginTime"
      :style="compute_table_col_width('beginTime')">

    </q-td>
    <q-td class="panda-table" :props="table_props" key="homeTeamNames" :style="compute_table_col_width('homeTeamNames')">
      <table-cell-ellipsis @click="p_ups(item)" :str_all="compute_team_table_item_show_name(
        item.homeTeamNames
      )
        " :tooltip_position="{ anchor: 'bottom left', self: 'center left' }"></table-cell-ellipsis>
    </q-td>
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="score"
      :style="compute_table_col_width('score')">
      <score-item :row_data="item"></score-item>
    </q-td>
    <q-td class="panda-table" :props="table_props" key="awayTeamNames" :style="compute_table_col_width('awayTeamNames')">
      <table-cell-ellipsis @click="p_ups(item)" :str_all="compute_team_table_item_show_name(
        item.awayTeamNames
      )
        " :tooltip_position="{ anchor: 'bottom left', self: 'center left' }"></table-cell-ellipsis>
    </q-td>
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="neutralGround"
      :style="compute_table_col_width('neutralGround')">
      <span class="td__reverse" v-if="show_match_reverse_sign(item)">
        <span>{{ i18n_t('pre_sale_set.t107') }}</span>
      </span>
      <span>{{ item.neutralGround == 1 ? "N" : "-" }}</span>
    </q-td>
    <q-td class="panda-table" :props="table_props" key="businessEventList">
      <div class="BusinessSources">
        <span v-for="(x, i) in business_dataSourceCode" :key="i">
          <span>{{ item.businessEventList && item.businessEventList.includes(x) ? x : '' }} </span>
        </span>
      </div>
    </q-td>
    <q-td class="panda-table" :props="table_props" key="unCommerceDataCode">
      <span>{{ item.unCommerceDataCode }}</span>
    </q-td>
    <q-td class="panda-table">
      <span class="panda-btn-table-handle panda-btn-sale"></span>
    </q-td>
    <q-td class="panda-table" :props="table_props" key="liveOddSupport" :style="compute_table_col_width('liveOddSupport')">
      <span class="panda_icon_kgqp vertical-middle" style="height: 14px; font-size: 14px;"
        v-if="item.liveOddSupport == 1 && business_dataSourceCode.includes(item.dataSourceCode)"
        :class="item.liveOddSupport == 1 && item.liveMatchSellStatus == 'Sold' ? 'panda-icon-red' : 'panda-icon-base'"></span>
    </q-td>
    <q-td class="panda-table" :props="table_props" key="sourceWeight" style="padding-left: 8px"> </q-td>
    <q-td class="panda-table" :props="table_props" key="soldMartketCategoryCount" style="padding-left: 8px"></q-td>
    <q-td class="panda-table panda-table-col-text-indent" :props="props" key="preTrader"
      :style="compute_table_col_width('preTrader')">
    </q-td>
    <q-td class="panda-table" :props="table_props" key="preMatchTime" :style="compute_table_col_width('preMatchTime')"></q-td>
    <q-td class="panda-table" :props="table_props" key="preMatchSellStatus"></q-td>
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="marketCount"
      :style="compute_table_col_width('marketCount')" v-if="select_sportId == 1">
    </q-td>
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="cornerShow"
      :style="compute_table_col_width('cornerShow')" v-if="select_sportId == 1">
    </q-td>
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="cardShow"
      :style="compute_table_col_width('cardShow')" v-if="select_sportId == 1">
    </q-td>
    <q-td class="panda-table" :props="table_props" key="matchStatusSourceCode"></q-td>
    <q-td class="panda-table panda-table-col-text-indent" :props="table_props" key="matchManageId"
      :style="compute_table_col_width('matchManageId')">
      {{ item.thirdMatchSourceId || "-" }}
    </q-td>
    <q-td class="panda-table" :props="table_props" key="traderChange"></q-td>
  </q-tr>
</template>
<script setup>
import {defineAsyncComponent, toRefs,inject} from "vue"
import { dom, SessionStorage } from "quasar";
import {i18n_t,is_new_trader,btn_permissions,} from "src/output/common/project-common.js"
import {project_caopan_componsable_style_table_col_width_componsable_fn,} from "src/output/common/componsable-common.js";
import { project_caopan_pages_match_sale_component_tablerow_componsable_variable_componsable_fn } from "project/caopan/pages/match/sale/component/tablerow/componsable/variable.js"
import { project_caopan_pages_match_sale_component_tablerow_componsable_index_componsable_fn } from "project/caopan/pages/match/sale/component/tablerow/componsable/index.js"
  import { project_caopan_pages_match_common_sale_componsable_fn } from "project/caopan/pages/match/common/sale/componsable/index.js"
  import {project_caopan_pages_match_common_sale_tablerow_componsable_index_fn} from "project/caopan/pages/match/common/sale_tablerow/componsable/index.js"
  import {project_caopan_componsable_pages_matchmanage_componsable_fn} from "project/caopan/componsable/pages/match/matchManage/componsable/index.js"
  const { height, width } = dom;
const tableCellEllipsis = defineAsyncComponent(() =>import("project/caopan/components/table/table_cell_ellipsis/index.vue"))
const matchPeriod = defineAsyncComponent(() =>import("project/caopan/pages/match/sale/component/match_period"))
const scoreItem = defineAsyncComponent(() =>import("project/caopan/components/score/score_item.vue"))
const emit = defineEmits(['log_click','mts_sold','r_click','submit_success','change_preRiskManagerCode','table_item_click_expanded','match_end','vieworAddTrader','collect_num','item_click', 'related_events', 'change_source_weight',  'p_ups', 'edit_match_sale','build_match_click'])
const props = defineProps({
  table_props: {//别的地方来的
    type: Object,
    default: [],
  },
  props: "",
  is_top_slot: "",
  show_right_detail_obj: "",
  tablecolumns: "",
  label_item: "",
  sort_status: {
    default: false
  },
  matchStatus: {
    type: String,
    default: 'Enable'
  },
  // 判断是否包含sr数据
  find_standard_sr: {
    default: false
  },
  select_sportId: {
    type: Number,
    default: -1
  },
  isFavorite: {
    type: Number,
    default: 0
  },
  play_data: {
    type: Object,
    default: () => ({})
  },
  queryform_form: {
    type: Object,
    default: () => ({})
  },
  match_sell_status_arr: {
    type: Array,
    default: () => []
  },
  actived_matchInfoId: {
    type: String,
    default: ''
  },
})
// 解构并保持响应式
const { table_props,matchStatus,play_data } = toRefs(props);
const base_state = project_caopan_pages_match_sale_component_tablerow_componsable_variable_componsable_fn({props})
const log_click =inject('log_click')
const payload = {...base_state,table_props,emit,play_data}
const {
  business_dataSourceCode,
  match_status_dataSourceCode_dot_color,
  match_status_dataSourceCode_border_color,
  // count,
  corner,
  card,
  loading_play,
  hover_play_icon,
  // 计算属性
  serverTime,
  // breadcrumbs,
  // optional_events_num,
  // market_odds_kind,
  compute_i18n_type,
} = payload
const {
  blur_set_data,
  build_match_click,
  open_log,
  isMatchupChange,
  settingConfig,
  copy_id,
  compute_dataSourceCode_fullname,
  toPlaySalePage,
  all_play
} = project_caopan_pages_match_sale_component_tablerow_componsable_index_componsable_fn(payload)
const { compute_team_table_item_show_name, compute_table_col_width } = project_caopan_componsable_style_table_col_width_componsable_fn(payload);
const {
  get_user_power,
go_settlements,
related_events_click,
sourceWeightChange,
preMatchTime,
edit_Maths_sale,
computed_trader_row_dot,
img_src,
end_click,
open_animation,
open_video,
refresh_play,
toggleCollect
} = project_caopan_pages_match_common_sale_tablerow_componsable_index_fn({...payload})
const {
  export_data,
  vieworAddTrader,
  close_trader_model,
  query_period_change,
  recompute_beginTim_show_label,
  handle_query,
  get_collect_num,
  show_match_reverse_sign,
  row_click,
  query_tournament_info,
  collect_num,
  p_ups,
  handle_change,
  settlements_path,
  go_event_analyse,
  computed_weight_value,
  init_querydata,
  r_click,
  rest_match,
  cancel_click,
  get_special_group_person,
  init_data3,
  init_data4,
  get_my_subordinate,
  init_data2,
  toggle_modal,
  get_dsc_options,
  change,
  size_change,
  get_tab,
  show_more_query_change,
  table_item_click,
  related_events,
} = project_caopan_pages_match_common_sale_componsable_fn({...payload, all_play})
const {
  compute_table_col_before_style
} = project_caopan_componsable_pages_matchmanage_componsable_fn({...payload})
</script>
<style lang="scss" scoped>
@import url("project/caopan/pages/match/common/tablerow.scss");
</style>
<style lang="scss" scoped>
@import url("project/caopan/pages/match/sale/component/tablerow/css/index.scss");
</style>