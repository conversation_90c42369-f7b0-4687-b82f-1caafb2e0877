import { api_system_set, } from "src/api/index.js";
import { Message, lodash,show_msg } from "src/output/common/project-common.js";
import {get_data} from "project/caopan/pages/basic_setting/data_setting/api-request/get_data.js"
const submit_ok=(payload,res)=>{
  let {code,msg,data} = lodash.get(res,'data')
  show_msg(code,msg)
  get_data(payload,data)
}
  //  提交设置
    export const submit = async(payload,param) => {
    
      try {
        let res= await api_system_set.post_batchUpdateConfig(param)
        submit_ok(payload,res)
      } catch (err) {
        console.log(err)
        Message.error(err)
      }
  
    }
