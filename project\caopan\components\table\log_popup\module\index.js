import moment from "moment";
import { sportList } from "src/global_data_config/sport_list.js";

import { get_params  } from "./params";
import { Sett_getOperateType } from "../api-request/set_get_operate_type";
import { getOperateType } from "../api-request/get_operate_type";
import { delete_empty_property_with_exclude } from "src/output/common/project-common";

    export const changea = (val) => {
      console.log(val);
    }
    // 赛种信息
    export const get_sportList = (payload) => {
      const {
        sport_list,


      } = payload
      sportList.map((item) => {
        sport_list.value.push({
          value: item.id,
          label: item.title,
        });
      }); //赛种信息
      return;
    }






    // 操作页面
    export const getPageList = (payload) => {
      const {

        config,


      } = payload
      config.value.operatePageList = [];
    }
    export const getApi = (payload) => {
      const {


        config,
        newline,


      } = payload
      let params = JSON.parse(JSON.stringify(get_params()));
      params = delete_empty_property_with_exclude(params);
      switch (params.operatePageCode) {
        case 112:
        case 113:
        case 114:
          config.value.request_api = "AOseachColunmInfos"; //AO日志
          newline.value = true;
          break;
        case 115:
        case 116:
        case 117:
        case 119:
          config.value.request_api = "get_MatchPDLog"; //网球报球版
          newline.value = false;
          break;
        case 120:
        case 121:
        case 122:
        case 123:
        case 100161:
          config.value.request_api = "get_tleOperateLog"; // 结算中心
          newline.value = false;
          break;
        default:
          config.value.request_api = "t_rcsCommenLogInfos"; //日志入口
          newline.value = false;
      }
    } //Api


    /**
     * @description:  计算页面最大高度
     */
    export const compute_scrollarea_style = (payload) => {
      const {

        search_state,

        open,

        window_size_info,
        minuHeight,
        scroll_area_height,


      } = payload
      if (open.value) {
        let hs = window_size_info.value.height - 73;
        if (open.value && open.value != 3) {
          hs = hs;
        }
        if (minuHeight.value) {
          hs = hs - minuHeight.value;
        }
        if (search_state.value) {
          hs = hs - 34;
        }
        scroll_area_height.value = hs;
      }
    }
    /**
     * @description: 计算列表显示
     * @param {*} data 数据
     * @param {*} key 需要显示的key值
     */
    export const component_text_html = (payload,data, key) => {
      let html = ``;
      let list = JSON.parse(data);
      if (list && list.length >= 1) {
        list.map((el) => {
          html += `<div class="log-item">
                      ${el[key] ? el[key] : key == "paramName" ? "-" : ""}
                  </div>`;
        });
      }
      return html;
    }

    //记住
    export const remember_func = (value) => {

      if (value) {
        localStorage.setItem("traders_log_remember", true);
      } else {
        localStorage.removeItem("traders_log_remember");
        localStorage.removeItem("traders_log_search_params");
        // 清空缓存参数
      }
    }

    export const change_fold = (payload,index, key_) => {
      const {

        table_data,

      } = payload
      console.warn(index);
      table_data.value[index].fold_status = !table_data.value[index].fold_status;
      console.warn(table_data.value[index]);
    }






        //操作类型判断
        export const getOperateTypeList = (payload) => {
          const {

            routerName,
            route



          } = payload
          let params = JSON.parse(JSON.stringify(get_params()));
          let sportId = route.query.sportId;
          // 转换时间戳
          params.beginTime = params.beginTime
            ? new Date(params.beginTime).getTime()
            : "";
          params.endTime = params.endTime ? new Date(params.endTime).getTime() : "";
          params.operateEndTime = params.operateEndTime
            ? new Date(params.operateEndTime).getTime()
            : "";
          params.operateStartTime = params.operateStartTime
            ? new Date(params.operateStartTime).getTime()
            : "";
          let operateType = [];
          // 如果从结算中心跳转的路由
          if (routerName.value == "setLog") {
            // 网球的操作类型
            if (sportId == 5) {
              operateType = getOperateType();
            } else {
              // 其他球种的操作类型
              params.sportId = sportId;
              operateType = getFootType(params);
            }
          } else {
            //  操盘日志原页面
            // 报球版-足蓝冰  联赛参数设置-足球的操作类型获取
            if ([119, 115, 117].includes(params.operatePageCode)) {
              operateType = getFootType(params);
            } else if (params.operatePageCode == 116) {
              // 报球版 -网球的操作类型
              operateType = getOperateType();
            } else if (params.operatePageCode == 100161) {
              operateType = Sett_getOperateType(params);
            } else {
              // 其他操作页面的操作类型获取
              operateType =operateType_other_case
            }
          }
          return operateType;
        }
