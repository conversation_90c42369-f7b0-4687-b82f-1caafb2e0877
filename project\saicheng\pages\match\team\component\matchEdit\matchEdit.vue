<!--
 * <AUTHOR> da<PERSON>win
 * @Description    : 球队库编辑管理
 * @FilePath: /project/bifenwang/pages-saicheng/match/team/component/matchEdit.vue
-->
<template>
  <div>
    <div v-if="!visible_log" class="text-panda-text-dark">
      <div>
        <div
          class="p1-sticky p1-df p1-acc p1-jcsb line-height-33px bg-panda-black text-panda-text-light"
        >
          <q-btn
            class="text-panda-text-dark w30x"
            icon="close"
            size="sm"
            @click="abort_match_modify"
            style="margin-left: 0"
          />&nbsp; {{ i18n_t("saicheng.match_edit.edit_22")
          }}<!-- 编辑管理 -->
          <span class="tac text-ellipsis" style="width: 350px">
            {{ compute_table_item_show_name(editing_obj, "", "sportTeamName") }}
          </span>
          <div>
            <!-- 操作日志 -->
            <q-btn
              class="panda-btn-light-dense"
              style="height: 24px"
              @click="show_log"
              :label="i18n_t('saicheng.match_team.log')"
            />
            <!-- 重置 -->
            <q-btn
              class="panda-btn-dark-dense"
              style="width: 80px; height: 24px"
              @click="reset_match_modify"
              :label="i18n_t('saicheng.match_edit.edit_title3')"
            />
            <!-- 保存并应用 -->
            <q-btn
              class="panda-btn-light-dense"
              style="height: 24px"
              @click="submit_match_modify(2)"
              :label="i18n_t('saicheng.match_edit.edit_title4')"
            />
          </div>
        </div>
        <div
          style="
            border-top: 1px solid var(--q-color-panda-border-color-secondary);
          "
          class="relative-position"
        >
          <div class="row">
            <div class="flex line-height-36px col">
              <div class="float-left panda-edit-component-lable">
                {{ i18n_t("saicheng.match_edit.edit_26") }}
              </div>
              <!-- 球队ID -->
              <div class="float-left text-panda-text-light">{{ edit_obj.teamManageId}}</div>
            </div>

            <div class="flex line-height-36px col">
              <div class="float-left panda-edit-component-lable">{{i18n_t("saicheng.match_team.match_status")}}</div>
              <!-- 匹配状态 -->
              <div class="float-left text-panda-text-light">
                {{ edit_obj.hasRelation == 1 ? i18n_t("saicheng.match_team.match"): i18n_t("saicheng.match_team.no_match") }}
              </div>
            </div>
          </div>
          <div class="flex line-height-36px">
            <div class="float-left panda-edit-component-lable">
              {{ i18n_t("saicheng.match_edit.edit_29") }}
            </div>
            <!-- 球队名称 -->
            <div class="float-left text-panda-text-light">
              {{
                compute_table_item_show_name(editing_obj, "", "sportTeamName")
              }}
            </div>
          </div>

          <div class="flex line-height-36px col">
            <!-- 区域 -->
            <label class="float-left  panda-edit-component-lable"
              >{{i18n_t("saicheng.match_team.regions")}}
              <span style="color: red">*</span> </label
            >
            <iInput readonly v-model="region" style="width: 200px"></iInput>
            <!-- 新增或修改区域 -->
            <span
              class="p1-ml--1 text-panda-text-blue cp"
              @click="show_tournament_list"
              >{{
                region
                  ? i18n_t("saicheng.common.change")
                  : i18n_t("saicheng.common.add")
              }}</span
            >
          </div>

          <div class="row">
            <div class="flex p-aic line-height-36px col">
              <div class="float-left panda-edit-component-lable">{{i18n_t("saicheng.match_team.is_it_a_club")}}</div>
              <!-- 是否为俱乐部 -->
              <div class="float-left text-panda-text-light">
                <iSelect
                  class="bg-panda-field-grey"
                  size="small"
                  style="width: 200px"
                  v-model="edit_obj.clubTeamFlag"
                >
                  <!-- 是/否 -->
                  <iOption
                    v-for="item in is_club_list"
                    :value="item.value"
                    :key="item.value"
                  >
                    {{ item.label }}
                  </iOption>
                </iSelect>
              </div>
            </div>

            <div class="flex p-aic line-height-36px col">
              <div class="float-left panda-edit-component-lable" >{{i18n_t("saicheng.match_team.deactivation")}}</div>
              <!-- 是否停用 -->
              <div class="float-left text-panda-text-light">
                <iSelect
                  class="bg-panda-field-grey"
                  size="small"
                  style="width: 200px"
                  v-model="edit_obj.operatorStatus"
                  :placeholder="i18n_t('saicheng.common.yes')"
                >
                  <!-- 是/否 -->
                  <iOption
                    v-for="item in is_stop_list"
                    :value="item.value"
                    :key="item.value"
                  >
                    {{ item.label }}
                  </iOption>
                </iSelect>
              </div>
            </div>
          </div>


          <div class="row q-my-md">
            <div class="panda-edit-component-lable">
              {{ i18n_t("saicheng.match_edit.edit_27") }}:
            </div>
            <!-- 上传球队Logo -->
            <div class="col">
              <div class="row">
                <div class="text-center" style="width: 144px">
                  <div
                    style="height: 72px"
                    :class="get_move_status_class(edit_obj.logoUrl)"
                    @click="move_status(edit_obj.logoUrl, 'httpUrl_72', 0)"
                  >
                    <img
                      :src="httpUrl_72"
                      v-if="httpUrl_72"
                      class="border-radius-10px"
                      alt
                      width="72px"
                      height="72px"
                      @click.stop
                    />
                  </div>
                <div class="text-panda-text-light q-my-xs">{{i18n_t('saicheng.match_edit.edit_88')}} </div>
                <div class="text-panda-text-light q-my-xs"> {{i18n_t('saicheng.match_edit.edit_89')}}</div>
                  <span class="relative-position">
                    <input
                      class="absolute z-top"
                      style="
                        opacity: 0;
                        height: 34px;
                        line-height: 2px;
                        font-size: 8px;
                        vertical-align: bottom;
                        max-height: 34px;
                        width: 124px;
                        max-width: 124px;
                      "
                      type="file"
                      name="inputfile1"
                      @change="upload_match_logo($event, 720)"
                      ref="inputfile1"
                      accept="image/jpeg, image/gif, image/png, image/bmp"
                    />
                    <!-- 上传 -->
                    <q-btn
                      class="panda-btn-light-dense mt9x"
                      :label="i18n_t('saicheng.match_edit.edit_6')"
                      style="
                        height: 24px;
                        line-height: 24px;
                        width: 100%;
                        margin-left: 0;
                      "
                    />
                  </span>
                </div>
              </div>
            </div>
            <div class="col" v-if="is_show_tow_img({ data: edit_obj })">
              <div class="row">
                <div class="text-center" style="width: 72px">
                  <div
                    style="height: 72px"
                    :class="get_move_status_class(edit_obj.logoUrl2)"
                    @click="move_status(edit_obj.logoUrl2, 'httpUrl2_72', 1)"
                  >
                    <img
                      :src="httpUrl2_72"
                      v-if="httpUrl2_72"
                      class="border-radius-10px"
                      alt
                      width="72px"
                      height="72px"
                      @click.stop
                    />
                  </div>
                  <div class="text-panda-text-light q-my-xs">72*72</div>
                  <span class="relative-position">
                    <input
                      class="absolute z-top"
                      style="
                        opacity: 0;
                        height: 34px;
                        line-height: 2px;
                        font-size: 8px;
                        vertical-align: bottom;
                        max-height: 34px;
                        width: 62px;
                        max-width: 62px;
                      "
                      type="file"
                      name="inputfile1"
                      @change="upload_match_logo($event, 720, 'httpUrl2_72')"
                      ref="inputfile1"
                      accept="image/jpeg, image/gif, image/png, image/bmp"
                    />
                    <!-- 上传 -->
                    <q-btn
                      class="panda-btn-light-dense mt9x"
                      :label="i18n_t('saicheng.match_edit.edit_6')"
                      style="
                        height: 24px;
                        line-height: 24px;
                        width: 100%;
                        margin-left: 0;
                      "
                    />
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 3113 关联球员 -->
          <!-- <div class="flex line-height-36px">
            <div class="float-left panda-edit-component-lable">球员总数:</div> -->
          <!-- <div class="float-left panda-edit-component-lable">{{ i18n_t('saicheng.match_edit.edit_29') }}</div> -->
          <!-- 球队名称 -->
          <!-- <div class="float-left text-panda-text-light col">10</div>
            <div class="col-2 row">
              <div class="float-left text-panda-text-light">关联球队</div> -->
          <!-- 球员资讯（俱乐部） -->
          <!-- <q-btn class="panda-btn-light-dense col" style="height:24px;" @click="show_player_info"
                label="球员资讯（非俱乐部）" /> -->
          <!-- 球员资讯（非俱乐部） -->
          <!-- <q-btn class="panda-btn-light-dense col" style="height:24px;" @click="show_player_info"
                label="球员资讯（非俱乐部）" />
            </div> -->
          <!-- </div> -->

          <!-- 球队衣服 -->
          <SKIN :editing_obj="edit_obj"/>

          <!-- 关联联赛详情 -->
          <linked_league_info :id="edit_obj.id" :editing_obj="edit_obj" />
 

          <!-- 多语言开始 -->
          <i-collapse v-model="team_language_show">
            <i-panel name="show" :hide-arrow="true">
              <div
                class="inline-block title"
                :class="team_language_show.includes('show') ? 'show' : ''"
              >
                {{ i18n_t('saicheng.match_team.team_name') }}
                <q-icon name="arrow_forward_ios" class="arrow" />
              </div>

              <template #content>
                <div
                  style="min-height: 100px"
                  class="p1-df p1-fdc pl10x pr10x p1-oxa"
                >
                  <q-markup-table>
                    <thead>
                      <tr
                        class="border-bottom-light text-panda-text-light line-height-30px"
                      >
                      <!-- 锁 -->
                      <th
                    class="text-left bg-panda-table-title"
                    style="width: 40px"
                  >
                    <img
                      class="cp ml5x"
                      @click="change_all_language_lock"
                      :src="`/img/${isLock ? 'close' : 'open'}_lock.svg`"
                      alt=""
                    />
                  </th>
                        <th
                          class="text-left bg-panda-table-title"
                          style="width: 100px"
                        >
                          {{ i18n_t("common.yuyan") }} ID
                        </th>
                        <!--语言ID-->
                        <th
                          class="text-left bg-panda-table-title"
                          style="width: 100px"
                        >
                          {{ i18n_t("saicheng.match_edit.edit_17") }}
                        </th>
                        <!--语言-->
                        <th
                          class="text-left bg-panda-table-title"
                          style="width: 100px"
                        >
                          {{ i18n_t("saicheng.match_team.full_name") }}
                        </th>
                        <!--全称-->
                        <th
                          class="text-left bg-panda-table-title"
                          style="width: 100px"
                        >
                          {{ i18n_t("saicheng.match_team.language_jc") }}
                        </th>
                      </tr>
                    </thead>
                   
                    <tbody v-if="language_list.length > 0"  class="bg-panda-base-dark text-panda-text-light">
                      <tr
                        class="bg-panda-base-dark text-panda-text-light line-height-30px panda-border-color border-bottom"
                        v-for="(item, index) in language_list"
                        :key="index"
                      >

                      <td>
                      <p
                        v-if="
                          language_history_record.find(
                            (x) => (x.languageType == item.key||x.languageType == item.key_jc)
                          )
                        "
                      >
                        <i-tooltip placement="right">
                          <!-- 编辑历史 -->
                          <span
                            ><img
                              src="/statics-saicheng/svg/history.svg"
                              alt=""
                              style="position: relative; top: 3px"
                          /></span>
                          <template #content>
                            <match-edit-history
                              class="p-zi1 none-scrollbar"
                              :data_log="language_history_record"
                              :language_type="item.key"
                              :language_type_jc="item.key_jc"
                            ></match-edit-history>
                          </template>
                        </i-tooltip>
                      </p>
                    </td>

                        <td
                          class="text-left text-panda-text-dark"
                          style="font-size: 12px"
                        >
                          <span>{{ item.key }}</span>
                          <!--语言ID-->
                          <span
                            v-if="['zs', 'en'].includes(item.key)"
                            style="position: relative; top: -2px"
                          ></span>
                        </td>

                        <td
                          class="text-left text-panda-text-dark"
                          style="font-size: 12px"
                        >
                          <span>{{ item.name }}</span
                          ><!--多语言名称-->
                          <span
                            class="must"
                            v-if="['zs', 'en'].includes(item.key)"
                            style="position: relative; top: -2px"
                          ></span>
                        </td>
                        <td class="text-left">
                          <div class="row">
                            <i-input
                              v-model.trim="item.title"
                              style="
                                width: 200px;
                                margin-top: 10px;
                                margin-bottom: 10px;
                              "
                              clearable
                              placeholder
                              maxlength="100"
                              :disabled="isLock"
                            >
                            </i-input>
                     
                          </div>
                        </td>
                        <td class="text-left">
                          <div class="row">
                            <i-input
                              v-model.trim="item.title_jc"
                              style="
                                width: 200px;
                                margin-top: 10px;
                                margin-bottom: 10px;
                              "
                              clearable
                              placeholder
                              maxlength="100"
                              :disabled="isLock"
                            >
                            </i-input>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </q-markup-table>
                </div>
              </template>
            </i-panel>
          </i-collapse>
          <!-- 多语言结束 -->
        </div>
      </div>
    </div>
    <div v-else>
      <div
        class="flex header__title p-h-33x bg-panda-black text-panda-text-light border-bottom-light"
      >
        <div>
          <!-- 关闭 -->
          <q-btn
            style="margin-left: -1px"
            class="text-panda-text-dark w32x h32x"
            icon="close"
            size="sm"
            @click="visible_log = false"
          />&nbsp; {{ i18n_t("saicheng.match_edit.log_1") }}
        </div>
      </div>
      <log
        :operateTargetId="editing_obj?.id"
        :editingObj="editing_obj"
      ></log>
    </div>
    <edit-mask
      ref="edit_mask"
      @latter_edit="$emit('close', 1)"
      :editing_obj="edit_obj"
      module="matchBackstage-mate-team"
    ></edit-mask>
  </div>
  <!-- 球员资讯 -->
  <player_info
    class="panda-table fixed fixed-left bg-panda-title-dard p1-oaa"
    :style="`top:0px; right:0px; left:auto; z-index:1002; width:800px;height:100%;`"
    v-if="visible_player_info"
    @back="hidden_player_info"
  />

  <!-- 联赛区域开始 -->
  <div v-if="region_data?.visible">
    <tournament-list
      v-model="region_data"
      :data="get_regions().filter((item) => item.id)"
    />
    <!-- :data="sport_region" 联赛区域结束 -->
  </div>
</template>

<script setup>
import log from "project/saicheng/pages/match/resource/component/log/log.vue";
import player_info from "project/saicheng/pages/match/team/component/player_info/player_info.vue";


import {
  get_sport_team_type,
} from "src/output/common/store-common.js";
import {
  show_teamType_and_twoLogo,
  compute_table_item_show_name,
} from "src/output/common/project-common.js";

import {
  defineAsyncComponent,
} from "vue";
import { project_saicheng_pages_match_team_component_matchEdit_variable_componsable_fn } from "project/saicheng/pages/match/team/component/matchEdit/componsable/variable.js";
import { team_match_edit_componsable_fn } from "project/saicheng/pages/match/team/component/matchEdit/componsable/index.js";
import { project_saicheng_componsable_pages_match_match_edit_componsable_index_componsable_fn } from "project/saicheng/componsable/pages/match/match_edit/componsable/index.js";
import {
  project_saicheng_componsable_pages_match_log_componsable_fn,
  project_saicheng_componsable_pages_match_match_componsable_fn,
} from "src/output/common/componsable-common.js";
import {
  get_regions,
} from "src/output/common/store-common.js";
import tournamentList from "project/saicheng/pages/match/components/tournament_list.vue";
const matchMulitLanguage = defineAsyncComponent(() =>
  import("project/saicheng/pages/match/components/match_mulit_language.vue")
);
//遮罩
const editMask = defineAsyncComponent(() =>
  import("project/saicheng/pages/match/components/edit_mask.vue")
);
//球衣组件
const SKIN = defineAsyncComponent(() =>
  import("project/saicheng/pages/match/team/component/skin/index.vue")
);
//关联联赛详情组件
const linked_league_info = defineAsyncComponent(() =>
  import("project/saicheng/pages/match/team/component/linked_league_info/linked_league_info.vue")
);
// 编辑历史组件
import matchEditHistory from "project/saicheng/components/table/match_edit_history.vue"; 

const emit = defineEmits(["close", "edit_success"]);
const props = defineProps({
  editing_obj: {
    type: Object,
    default: () => ({}),
  },
  cur_sportId: {
    required: true,
  },
  close: {
    type: Function,
    default: () => {},
  },
  edit_success: {
    type: Function,
    default: () => {},
  },
  showrightdetail_btn: {
    type: Boolean,
    default: "",
  },

  cur_id: {//编辑-请求接口的id
    type: String,
    default: ''
  },
})
const base_state = project_saicheng_pages_match_team_component_matchEdit_variable_componsable_fn({ props });
const {
  httpUrl_72,
  httpUrl2_72,
  sportTeamName,
  language_list,
  language_history_record,
  team_language_show,
  data_log,
  visible_log,
  visible_player_info,
  is_stop_list,
  is_club_list,
  region_data,
  region,
  edit_obj,
    zs_name,
    en_name,
    isLock,
} = base_state;
const row_payload = {
  ...base_state,
  props,
  show_teamType_and_twoLogo,
};
const { is_show_tow_img, computed_language_lock, change_language_lock } =
  project_saicheng_componsable_pages_match_match_edit_componsable_index_componsable_fn(
    {
      ...row_payload,
    }
  );

const { show_log, hidden_log, init_data_log, modify_remark_log } =
  project_saicheng_componsable_pages_match_log_componsable_fn({
    ...row_payload,
  });
const { upload_match_logo } =
  project_saicheng_componsable_pages_match_match_componsable_fn({
    ...row_payload,
  });

const {
  abort_match_modify,
  reset_match_modify,
  get_move_status_class,
  move_status,
  submit_match_modify,
  show_player_info,
  hidden_player_info,
  show_tournament_list,
  change_all_language_lock,
} = team_match_edit_componsable_fn({
  ...row_payload,
  emit,
  is_show_tow_img,
  init_data_log,
});
</script>

<style lang="scss" scoped>
@import url("project/saicheng/pages/match/team/component/matchEdit/css/index.scss");
</style>
