

import { i18n_t } from "src/output/common/project-common";



export const  operatePageList_default = [
  // 这个地方搞不懂为什么要前端定死，这个操作维度是会累加的（三期优化）
  { label: i18n_t("log.code1"), value: 14 }, // 早盘操盘
  { label: i18n_t("log.code10"), value: 110 }, // 早盘操盘-设置
  { label: i18n_t("log.code3"), value: 100 }, // 早盘操盘-调价窗口
  { label: i18n_t("log.code2"), value: 15 }, // 早盘操盘-次要玩法
  { label: i18n_t("log.code4"), value: 101 }, // 早盘操盘-次要玩法-调价窗口
  { label: i18n_t("log.code5"), value: 17 }, // 滚球操盘
  { label: i18n_t("log.code11"), value: 111 }, // 滚球操盘-设置
  { label: i18n_t("log.code7"), value: 102 }, // 滚球操盘-调价窗口
  { label: i18n_t("log.code6"), value: 18 }, // 滚球操盘-次要玩法
  { label: i18n_t("log.code8"), value: 103 }, // 滚球操盘-次要玩法-调价窗口
  { label: i18n_t("log.code9"), value: 21 }, // 联赛参数设置
  { label: "AO FB", value: 112 }, // AO足球
  { label: "AO BK", value: 114 }, // AO篮球
  { label: "AO TT", value: 113 }, // AO乒乓球
  { label: i18n_t("log.code23"), value: 119 }, // 报球版-足球
  { label: i18n_t("log.code24"), value: 115 }, // 报球版-篮球
  { label: i18n_t("log.code25"), value: 116 }, // 报球版-网球
  { label: i18n_t("log.code26"), value: 117 }, // 报球版-冰球
  { label: i18n_t("menus.lm_5.children1"), value: 13 }, // 早盘赛事
  { label: i18n_t("menus.lm_6.children1"), value: 16 }, // 滚球赛事
  { label: i18n_t("menus.lm_13.children1"), value: 10001 }, // 历史赛事
  { label: i18n_t("menus.lm_7.children3"), value: 1601 }, // 玩法管理
  { label: i18n_t("menus.lm_7.children10"), value: 1101 }, // 电子竞技玩法管理
  { label: i18n_t("menus.lm_7.children8"), value: 1201 }, // 虚拟体育玩法管理
  { label: i18n_t("menus.lm_7.children4"), value: 1301 }, // 玩法集管理
  { label: i18n_t("menus.lm_7.children9"), value: 1401 }, // 电子竞技玩法集管理
  { label: i18n_t("menus.lm_7.children11"), value: 1501 }, // 虚拟体育玩法集管理
  { label: i18n_t("log.code33"), value: 100161 }, // 联赛参数设置- 足球
  { label: i18n_t("log.code27"), value: 120 }, // 结算中心(新)-足球
  { label: i18n_t("log.code28"), value: 121 }, // 结算中心(新)-篮球
  { label: i18n_t("log.pre_settings"), value: 1000 }, // 预开售设置
  { label: i18n_t("log.code29"), value: 122 }, // 预开售设置-足种
  { label: i18n_t("log.code30"), value: 123 }, // 预开售设置-蓝种
  { label: i18n_t("log.settle_c01"), value: 1801 }, // 结算中心-C01
  { label: i18n_t('log.c01_morning'),value: 1702},// C01-早盘
  { label: i18n_t('log.c01_ing'),value: 1703},// C01-进行中
  { label: i18n_t('log.code40'),value: 10201},// 上传任务
  { label: i18n_t('log.code41'),value: 10202},// 下载任务
  { label: i18n_t('log.C01_Settlement'),value: 1705},// C01-进行中
  { label: i18n_t('log.CO1_odds'),value: 1706},// C01-进行中
  { label: i18n_t('log.CO1_cancel'),value: 1707},// C01-比赛取消
  { label: i18n_t('log.discounted_odds'),value: 36},// 早盘管理-优惠盘口
  { label: i18n_t('Settlement_log.t2'),value: 201},// 结算中心
  { label: i18n_t('Settlement_log.t4'),value: 203},// 冠军玩法
  { label: i18n_t('Settlement_log.t5'),value: 204},// 注单中心
  { label: i18n_t('Settlement_log.t6'),value: 205},// 取消/冻结注单
  { label: i18n_t('Settlement_log.t7'),value: 206},// 取消/冻结注单ES
  { label: i18n_t('Settlement_log.t8'),value: 207},// 报球版-足球
  { label: i18n_t('Settlement_log.t9'),value: 208},// 报球版-篮球
  { label: i18n_t('Settlement_log.t12'),value: 209},// 结算中心(新)-足球
  { label: i18n_t('Settlement_log.t13'),value: 210},// 结算中心(新)-篮球
]


export   const  operateType_other_case= [
  { label: i18n_t("log.code12"), value: "调赔率" },
  { label: i18n_t("log.code13"), value: "调整参数" },
  { label: i18n_t("log.code14"), value: "调盘口" },
  { label: i18n_t("log.code15"), value: "新增盘口" },
  { label: i18n_t("log.code16"), value: "开关封锁" },
  { label: i18n_t("log.code17"), value: "切换数据源" },
  { label: i18n_t("log.code18"), value: "切换操盘模式" },
  { label: i18n_t("log.code48"), value: "系统切换数据源" },
  { label: i18n_t("log.code19"), value: "赛事模板调整" },
  { label: i18n_t("log.code20"), value: "模板选择" },
  { label: i18n_t("log.code21"), value: "模板修改" },
  { label: i18n_t("log.code22"), value: "模板删除" },
  { label: i18n_t("log.code31"), value: "修改赛事设置" },
  { label: i18n_t("log.code38"), value: "拒单事件" },
  { label: i18n_t("log.code39"), value: "特殊事件" },
  // ao相关参数（value值无需国际化，固定传汉字）
  { label: "apply(pre)", value: "apply(赛前)" },
  { label: "reverse(pre)", value: "reverse(赛前)" },
  { label: "specApply(pre)", value: "specApply(赛前)" },
  { label: "apply(in-play)", value: "apply(滚球)" },
  { label: "reverse(in-play)", value: "reverse(滚球)" },
  { label: "specApply(in-play)", value: "specApply(滚球)" },
  { label: i18n_t("menus.lm_10.children3"), value: "排序" },
  { label: i18n_t('redcat_plays.text8'), value: '限额设置' },
  { label: i18n_t("menus.lm_7.children1"), value: "预开售设置" },
  { label: i18n_t('log.gameplay'), value: '开售玩法' },
  { label: i18n_t('log.add_template'), value: '模板新增' },
  { label: i18n_t('leagueSet.templet.a11'), value: '提前结算开关' },
  { label: i18n_t('assign.text1'), value: '操盘指派' },
  { label: i18n_t('saleTable.sa_76'), value: '数据源权重设置' },
  { label: i18n_t('gameplaySetManagement.add_gameplay_set'), value: '新增玩法集' },
  { label: i18n_t('log.play_sorting'), value: '玩法集排序' },
  { label: i18n_t('log.code35'), value: '玩法集管理-足球' },
  { label: i18n_t('leagueSet.templet.t6'), value: '相邻盘口赔率分差' },
  { label: i18n_t('log.edit_gameplay'), value: '编辑玩法管理' },
  { label: i18n_t('log.edit_playset'), value: '编辑玩法集管理' },
  { label: i18n_t('leagueSet.table.th5_4'), value: '联赛属性' },
  { label: i18n_t('log.confirm_all'), value: '赛事模板调整-确认全部玩法' },
  { label: i18n_t('log.lock_odds'), value: '锁赔率' },
  { label: i18n_t('log.delete_playset'), value: '删除玩法集' },
  { label: i18n_t('log.result_redraw'), value: '一鍵賽果重拉' },
  { label: i18n_t('log.open_close'), value: '开盘/关盘' },
  { label: i18n_t('log.confirmation'), value: '标记确认' },
  { label: i18n_t('log.confirm_total'), value: '确认总开/关' },
  { label: i18n_t('log.data_provider'), value: '系统接收数据商取消修改原因' },
  { label: i18n_t('log.cancelled_match'), value: '系统接收数据商取消状态' },
  { label: i18n_t('stCenter.stc143'), value: '赛果重推' },
  { label: i18n_t('log.a01_opening'), value: 'AO自动开盘状态修改' },
  { label: i18n_t('log.delete_trader'), value: '删除操盘手' },
  { label: i18n_t('basic_setting.title_11'), value: '客户端开关' },
  { label: i18n_t('log.assign_traders'), value: '批量指派操盘手' },
  { label: i18n_t('log.xy_switch'), value: '代操盘-XY开关' },
  { label: i18n_t('log.set_label'), value: '调整风险类型' },
  { label: i18n_t('log.change_state'), value: '赛前转滚球自动开盘' },
  { label: i18n_t('log.change1'), value: '事件源自动切换' },
  { label: i18n_t('log.new_odds'), value: '新增-优惠盘口的单个投注项' },
  { label: i18n_t('log.delete_odds'), value: '删除-优惠盘口的单个投注项' },
  { label: i18n_t('log.editor_odds'), value: '编辑-优惠盘口的总体 上浮百分比' },
  { label: i18n_t('log.edit_switch'), value: '编辑-优惠盘口 系统级开关' },
  { label: i18n_t('log.edit_bets'), value: '编辑-单个投注项的上浮百分比' },
  { label: i18n_t('log.edit_single_bet'), value: '编辑-单个投注项最高投注限额' },
  { label: i18n_t('log.edit_single_switch'), value: '编辑-单个投注项 开关' },
  { label: i18n_t('Settlement_log.t1'), value: '冻结' },
  { label: i18n_t('log.additional_gameplay_adjustments'), value: '附加玩法检测设置调整' },
  { label: i18n_t('log.automatic_shutdown_settings'), value: '自动关盘设置调整' },
  { label: i18n_t('leagueSet.edit_t.t67'), value: '玩法自动赔率源切换设置' },
  { label: i18n_t('leagueSet.edit_t.t68'), value: '自动切换数据源' },
];
