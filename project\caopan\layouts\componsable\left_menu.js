import {
  menu_level_1_mouseenter,
  menu_level_1_mouseleave,
  menu_level_2_mouseleave, 
  menu_level_2_click, 
  menu_level_1_click
} from "project/caopan/layouts/module/left_menu.js"


export const left_menu_componsable_fn = (payload) => {

    return {
      menu_level_1_mouseenter: (item,index) => menu_level_1_mouseenter(payload, item, index ),
      menu_level_1_mouseleave: (e, index) => menu_level_1_mouseleave(payload,e, index ),
      menu_level_2_mouseleave: (e, index) => menu_level_2_mouseleave(payload, e, index ),
      menu_level_2_click: item => menu_level_2_click(payload, item),
      menu_level_1_click: item => menu_level_1_click(payload, item),
    }
}