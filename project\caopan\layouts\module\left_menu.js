import { get_router_matching } from "src/common-202412/project-commom/module/router_matching.js" // 获取用户权限数据

// import menu_data from "project/caopan/config/menu/menu.js"
import menu_data from "project/caopan/config/menu/menu1.js"
import menudata_tw from "project/caopan/config/menu/menu_tw.js"
import {compute_moving_direction, compute_active_menu_level} from "project/caopan/layouts/module/style.js"
import {getExceptionMatchSize} from "project/caopan/layouts/api-request/left_menu.js"
import { get_bet_cancel_total, getUnsettleMatchSize, marquee_click} from "project/caopan/layouts/api-request/header.js"
import {set_champion_activem,set_redcat_activem,} from "src/output/common/store-common.js"

//兼容无缝滚动点击事件为绑定
export  const handle_click = (payload,event)=>{

  if(event.target.dataset.dept==='0'){
    marquee_click(get_announ_cement.value[0])
  }
}

// 获取当前一级菜单权限
export  const get_menudata = (payload)=>{
  const {menudata, showMenu2} = payload

  const project_location = BUILDIN_CONFIG.ENVVARIABLES.project_location
  let menu_data_ = project_location == 'tw' ? menudata_tw : menu_data
  if(BUILDIN_CONFIG.ENVVARIABLES.NODE_ENV == "development" || BUILDIN_CONFIG.ENVVARIABLES.FRONT_WEB_ENV == 'local_dev'){
    // 当前本地开发和线上开发环境不做限制
    menudata.value = menu_data_
  }else {
    menu_data_.forEach(item => {
      if(get_router_matching().includes(item.name)){
        menudata.value.push(item)
        showMenu2.value.push(false)
      }
    })
  }
}


export  const menu_level_1_mouseenter = (payload,item, index)=>{

  const {showMenu2, show_menu_2_data,menu_active_name_pool, menu_index, route} = payload
  showMenu2.value = [];
  if (item.children) {
    //判断如果没有子级菜单则不显示
    menu_index.value = index;
    show_menu_2_data.value = [];
    // 当前本地开发和线上开发环境不做限制
    if(BUILDIN_CONFIG.ENVVARIABLES.NODE_ENV == "development" || BUILDIN_CONFIG.ENVVARIABLES.FRONT_WEB_ENV == 'local_dev'){
      show_menu_2_data.value = item.children;
    }else {
      item.children.forEach(val => {
        if(get_router_matching().includes(val.name)){
          show_menu_2_data.value.push(val)
        }
      })
    }

    showMenu2.value[index] = true
    menu_active_name_pool.value.push(item.name);
    compute_active_menu_level(payload, route);
    if (!showMenu2.value[index]) {
      // showMenu2.value[index] = true
      showMenu2.value.splice(index, 1, true)
    }
  }
  if (menu_index.value != index) {
    //判断如果移入别的菜单项 则隐藏当前子级菜单
    showMenu2.value[menu_index.value] = false
  }
}

export  const menu_level_1_mouseleave = (payload,e, index)=>{
  const {showMenu2} = payload
  let direction = compute_moving_direction(e)
  if (direction !== 1) {
    showMenu2.value.splice(index, 1, false)
  }
}

export  const menu_level_2_mouseleave = (payload,e, index)=>{
  const {showMenu2} = payload
  let direction = compute_moving_direction(e)
  if (direction !== 3) {
    showMenu2.value.splice(index, 1, false)
  }
}

/**
 * @description: 一级菜单点击事件
 * @param {*}
 * @return {*}
 */
export  const menu_level_1_click = (payload,arg)=>{
  console.log("menu_level_1_click",payload,arg)
  const {  router} = payload
  // showMenu2.value[index] = true
  if(arg.name == 'order_manage_') {
    // betcancel 红点统计接口调用 待处理
    // 获取取消订单红点
    get_bet_cancel_total(payload)
    // 获取异常待处理赛事
    getExceptionMatchSize(payload)
    // 获取未结算赛事
    getUnsettleMatchSize(payload)
  }
  // 冠军 红点
  set_champion_activem()
  set_redcat_activem()
  if (!arg) return;
  if (!arg.children) {
    console.log(" 冠军 红点",arg)
    router.push({
      name: arg.name
    });
  }
}

/**
 * @description: 打开新页面
 * @param {*}
 * @return {*}
 */
export  const open_new_page = (payload,arg)=>{

  let res = false;
  if (!arg || typeof arg !== "object") return res;
  let { parent_name } = arg;
  if (parent_name === "match_container") {
    let url = location.href;
    if (arg.name && typeof arg.name === "string" && url) {
      url = url.replace(
        /#\/.+/g,
        `#/main/match/${arg.name.replace("match_", "")}`
      );
      window.open(url);
      res = true;
    }
  }
  return res;
}

/**
 * @description: 二级菜单点击事件
 * @param {*}
 * @return {*}
 */
export  const menu_level_2_click = (payload,item)=>{
  console.log("menu_level_2_click",payload,item)
  const { menu_active_name_pool, router} = payload
  // 处于子页面时 使菜单处于激活状态 并不能跳转
  if(item.name == 'sportsRules') {
    window.open('http://sports-rules-admin.sportxxxr1pub.com/#/', '_blank')
    return
  }
  if (!open_new_page(item) && menu_active_name_pool.value.indexOf(item.name) == -1) {
    router.push({
      name: item.name
    });
  }
}
