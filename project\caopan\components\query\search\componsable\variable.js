
import {
  src_componsable_global_variable_componsable_fn
} from "src/output/common/componsable-common.js";
import { create_base_state_instance_fn } from "project/caopan/components/query/search/variable/index.js";

export const project_caopan_components_query_search_variable_componsable_fn = () => {
  // 基础变量获取实例
  const base_state = create_base_state_instance_fn()
  // 公用 componsble 变量variable 
  const global_obj = src_componsable_global_variable_componsable_fn();

  // 基础上下文组装
  const variable_state = {
    ...base_state,
    ...global_obj,
  }

  return variable_state;

}
