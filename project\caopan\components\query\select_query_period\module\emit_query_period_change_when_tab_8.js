import { i18n_t, format_date_base_gmt_add_8 } from "src/output/common/project-common.js";
// import { format_week } from "project/caopan/common/formartmixin/formartmixin.js";
import {
  format_week
} from "src/output/common/project-common.js"
export const handle_history_date_confirm = (payload, val) => {
  show_q_date_component.value = false;
  emit_query_period_change_when_tab_8(payload);
}
 const emit_query_period_change_when_tab_8 = (payload) => {
    const { history_date, emit } = payload
    let val = {};
    let ts = Date.parse(history_date.value);
    set_history_startTimeFrom(ts);
    val = {
      startTimeFrom: ts
    };
    val.tab = selected_date_tab.value;
    val.historyFlag = 1;
    val.week_str = format_week(ts);
    val.value = 8;
    val.label = `${i18n_t('selectPeriodTab.Tab_11')}`;
  
    let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(
      Date.parse(history_date.value)
    );
    val.history_date = history_date.value;
    val.true_label = `${m}${i18n_t('selectPeriodTab.Tab_1')}${d}${i18n_t('selectPeriodTab.Tab_2')}`;
    val.nav_label = `${m}${i18n_t('selectPeriodTab.Tab_1')}${d}${i18n_t('selectPeriodTab.Tab_2')}`;
    set_breadcrumbsPart3([val.label]); // 和头部一致
    emit("query_period_change", val);
  }