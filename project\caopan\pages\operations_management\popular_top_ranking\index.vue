<!--
 * <AUTHOR> Rank
 * @Date           : 2020-11-08 15:21:05
 * @Description    : 运营管理-联赛排序
 * @FilePath: /project/caopan/pages/operations_management/popular_top_ranking/index.vue
-->
<template>
  <!-- 如果滚球跟早盘都没有操作权限时不展示页面 -->
  <div class="project-caopan-pages-operations-management-popular-top-ranking">
  <div class=" use-ivew table-f p-pr" v-if="
    btn_permissions('popular_top_ranking_trading:view') ||
    btn_permissions('popular_top_ranking_rolling:view')
  ">
    <SearchPanel />
     <Table />
  </div>
  <div v-else style="
      text-align: center;
      font-size: 16px;
      color: var(--q-color-panda-text-dark);
    ">
    {{ i18n_t("common.permissions") }}
  </div>
  </div>
</template>

<script setup>
import {
  i18n_t,
  btn_permissions,
} from "src/output/common/project-common.js";
import SearchPanel from "project/caopan/pages/operations_management/popular_top_ranking/components/search_panel/indeex.vue";
import Table from "project/caopan/pages/operations_management/popular_top_ranking/components/table/index.vue";
import {project_caopan_pages_operations_management_popular_top_ranking_composable_variable_componsable_fn} from 'project/caopan/pages/operations_management/popular_top_ranking/composable/variable.js'
import {project_caopan_pages_operations_management_popular_top_ranking_composable_fn} from 'project/caopan/pages/operations_management/popular_top_ranking/composable/index.js'

const base_payload = project_caopan_pages_operations_management_popular_top_ranking_composable_variable_componsable_fn()

project_caopan_pages_operations_management_popular_top_ranking_composable_fn(base_payload)
</script>
  
<style lang="scss">
.project-caopan-pages-operations-management-popular-top-ranking{
  @import 'project/caopan/pages/operations_management/popular_top_ranking/css/popular_top_ranking.scss'
}
</style>
