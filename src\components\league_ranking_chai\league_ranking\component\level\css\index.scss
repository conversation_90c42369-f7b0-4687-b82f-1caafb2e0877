:deep(.ivu-tabs-bar) {
    border-bottom: none;
}

:deep(.ivu-tabs-tab) {
    color: var(--q-color-panda-text-light);
    border: 1px solid var(--q-color-panda-them--404758);
    line-height: 22px !important;
    height: 22px !important;
    font-size: 12px;
    margin-right: 10px !important;
    padding: 0px 10px !important;
    border-radius: 2px;
}

:deep(.ivu-tabs-tab:hover) {
    border: 1px solid var(--q-color-panda-primary);
    color: var(--q-color-panda-primary);
}

:deep(.ivu-tabs-nav) {
    height: 32px;
    line-height: 35px;
    overflow: hidden;
    padding-left: 10px;
}

:deep(.ivu-tabs) {
    height: 33px;
    margin-left: 46px;
    margin-right: 207px;
}

:deep(.ivu-tabs-ink-bar-animated) {
    display: none;
}

:deep(.ivu-tabs-tab-active) {
    color: #fff !important;
    background: var(--q-color-panda-primary);
    border: 1px solid rgba(255, 255, 255, 0);
}

:deep(.ivu-tabs-tab-active:hover) {
    background: rgba(0, 169, 151, 0.4) !important;
    color: #fff !important;
    border: 1px solid rgba(255, 255, 255, 0) !important;
}

:deep(.ivu-tabs-tab-focused) {
    border: 1px solid var(--q-color-panda-primary) !important;
}

:deep(.ivu-tabs-tab-focused:hover) {
    background: rgba(0, 169, 151, 0.4) !important;
    color: #fff !important;
    border: 1px solid rgba(255, 255, 255, 0) !important;
}

:deep(.ivu-tabs-nav-container:focus .ivu-tabs-tab-focused) {
    color: #fff;
    border-color: rgba(255, 255, 255, 0) !important;
}

:deep(.ivu-tabs-nav-prev:hover),
:deep(.ivu-tabs-nav-next:hover) {
    background: rgba(0, 170, 152, 0.1) !important;
}

:deep(.ivu-icon-ios-arrow-back),
:deep(.ivu-icon-ios-arrow-forward) {
    vertical-align: middle;
}

.show-left {
    top: 0px;
    left: 20px;
    height: 34px;
    line-height: 34px;
    text-align: center;
}

:deep(.show-left .panda-right-info-toogle-button) {
    vertical-align: middle;
}

.show-right {
    top: 0px;
    right: 5px;
    height: 34px;
    line-height: 34px;
    text-align: center;
}