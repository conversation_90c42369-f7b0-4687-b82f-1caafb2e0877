<!--qPage-stickyq-fab-action
 * @FilePath: /project/caopan/components/simulate_order/index.vue
 * @Description:
-->


<template>
  <div class="use-ivew">
    <q-page-sticky
      position="bottom-right"
      :offset="fabPos1"
      style="z-index: 101"
    >
      <q-fab-action v-touch-pan.prevent.mouse="moveFab1">
        <iButton type="warning" size="default" @click="setShowTool(true)"
          >WS模拟推送-测试专用</iButton
        >
      </q-fab-action>
    </q-page-sticky>
    <q-page-sticky
      position="bottom-right"
      :offset="fabPos"
      style="z-index: 101"
      v-if="showTool"
    >
      <q-fab-action
        v-if="showTool"
        v-touch-pan.prevent.mouse="moveFab"
        class="full-width bg-panda-field-grey"
        style="border-radius: 0; z-index: 1"
      >
        <span class="text-panda-text-active">which：</span>
        <iSelect
          class="bg-panda-field-grey"
          size="small"
          style="width: 50px"
          v-model="which"
        >
          <iOption
            v-for="item in [1, 2, 3, 4, 5]"
            :value="item"
            :key="item"
            :label="item"
          />
        </iSelect>
        <span class="text-red q-mr-md q-ml-md"
          >注：部分推送，请将dataSourceTime更新为最新时间，不存库，一次性使用</span
        >
        <iButton
          type="primary"
          @click="
            $copy(
              new Date().getTime(),
              `当前时间戳${new Date().getTime()}复制成功`
            )
          "
          >获取当前时间戳</iButton
        >
      </q-fab-action>
      <div class="bg-panda-base-dark tool_area position-relative">
        <iIcon
          type="ios-close-circle"
          class="position-absolute close-icon"
          @click="setShowTool(false)"
        />
        <div>
          <iButton type="info" long @click="onSend" style="height: 35px"
            >Send</iButton
          >
        </div>
        <textarea v-model="text" class="textarea_container" />
      </div>
    </q-page-sticky>
  </div>
</template>


<script setup>
import {ref} from "vue"
import { set_websocket_data } from "src/output/common/store-common.js";
let obj = {
  ack: 0,
  command: 30035,
  globalId: "123456",
  msgId: "e5b6c404-784f-4a79-8991-a1adb3b48e39",
  responseData: {
    clearZero: 1,
    matchId: 3388979,
  },
};
// import { mapActions } from "vuex";
const fabPos = ref([241, 108]);
const fabPos1 = ref([410, 927]);
const draggingFab = ref(true);
const curCommand = ref(30035);
const commandList = ref([30035, 30001, 30015]);
const text = ref(JSON.stringify(obj));
const showTool = ref(false);
const which = ref(2);

// ...mapActions(["set_websocket_data"]), // 设置vuex数据
const moveFab = (payload, ev) => {
  draggingFab.value = ev.isFirst !== true && ev.isFinal !== true;
  fabPos.value = [fabPos.value[0] - ev.delta.x, fabPos.value[1] - ev.delta.y];
};
const moveFab1 = (payload, ev) => {
  fabPos1.value = [
    fabPos1.value[0] - ev.delta.x,
    fabPos1.value[1] - ev.delta.y,
  ];
};
const setShowTool = (payload, showTool) => {
  showTool.value = showTool;
};
const onSend = (payload) => {
  setTimeout(() => {
    let data = {
      which: which.value,
      data: text.value,
      id: 123,
    };
    console.log(data);
    set_websocket_data(data);
    Message.success("发送成功");
  }, 100);
};
</script>

<style lang="scss" scoped>
.tool_area {
  width: 1200px;
}
.close-icon {
  right: 0;
  top: -40px;
  font-size: 40px;
  z-index: 11;
}
.textarea_container {
  padding: 5px 10px;
  width: 100%;
  min-height: 500px;
  font-size: 14px;
}
</style>

