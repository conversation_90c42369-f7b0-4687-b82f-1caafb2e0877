import { useRoute } from "vue-router"

export const init_query_form_date_arr = (payload) => {
    const route = useRoute();
    const { serverTime, current_promise, first_day_startTimeFrom, query_form_date_arr, show_yesterday } = payload
    let arr = [];
    let timestamp = new Date().getTime()
    if (serverTime.value) {
        typeof current_promise.value == "function" && current_promise();
    }
    let [y1, m1, d1, h1, mm1, s1] = format_date_base_gmt_add_8(
        serverTime.value ? serverTime.value : timestamp
    );
    // 当天中午12点
    let fmt_8_h_12 = new Date(`${y1}-${m1}-${d1} 12:00:00`).getTime();
    let t_s = ""; // 开始时间戳
    if (Number(h1) < 12) {
        // 美国的前一天 的凌晨零点   开始时间 是中国前一天的中午12点
        t_s = fmt_8_h_12 - 24 * 60 * 60 * 1000;
    } else {
        // 美国的同一天 的凌晨零点   开始时间 是中国 当天的中午12点
        t_s = fmt_8_h_12;
    }
    /**
     * 因为 t_s 转换后 日月  再 中国和美国一样的 ，所以 不在做处理
     */
    let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(t_s);
    first_day_startTimeFrom.value = t_s;

    if (route.name !== 'trader_manage_liveOddSupport') {
        for (let i = 0; i < 1; i++) {
            let time_str = t_s + i * 24 * 60 * 60 * 1000;
            let time = new Date(time_str);
            let [y_, m_, d_, h_, mm_, s_] = format_date_base_gmt_add_8(
                time_str
            );

            arr.push({
                value: i,
                label: `${m_}${i18n_t('selectPeriodTab.Tab_1')}${d_}${i18n_t('selectPeriodTab.Tab_2')}`,
                true_label: `${m_}${i18n_t('selectPeriodTab.Tab_1')}${d_}${i18n_t('selectPeriodTab.Tab_2')}`,
                nav_label: `${m_}${i18n_t('selectPeriodTab.Tab_1')}${d_}${i18n_t('selectPeriodTab.Tab_2')}`,
                startTimeFrom: time.getTime(),
                tab: i,

                historyFlag: 0,
                week_str: format_week(time.getTime())
            });
        }

        arr[0].label = i18n_t('selectPeriodTab.Tab_4');
        // arr[1].label =  i18n_t('selectPeriodTab.Tab_5');
        arr.push({
            value: 7,
            tab: 7,
            label: i18n_t('selectPeriodTab.Tab_6'),
            startTimeFrom: new Date(t_s + 1 * 24 * 60 * 60 * 1000).valueOf(),
            time: new Date(t_s + 1 * 24 * 60 * 60 * 1000).valueOf()
        });
    }

    if (show_live_odd.value) {
        if (route.name !== 'trader_manage_morning_o') {
            arr.unshift({
                value: 9,
                label: i18n_t('common.inPlay'),
                true_label: `${m}${i18n_t('selectPeriodTab.Tab_1')}${d}${i18n_t('selectPeriodTab.Tab_2')}`,
                nav_label: i18n_t('common.inPlay'),
                startTimeFrom: t_s,
                tab: 9,
                historyFlag: 0,
                week_str: format_week(t_s)
            });
        }
    }
    //昨日
    if (show_yesterday.value) {
        let [__y, __m, __d, __h, __mm, __ss] = format_date_base_gmt_add_8(
            t_s - 24 * 60 * 60 * 1000
        );
        arr.unshift({
            value: 10,
            label: `${__m}${i18n_t('selectPeriodTab.Tab_1')}${__d}${i18n_t('selectPeriodTab.Tab_2')}`,
            true_label: `${__m}${i18n_t('selectPeriodTab.Tab_1')}${__d}${i18n_t('selectPeriodTab.Tab_2')}`,
            nav_label: `${__m}${i18n_t('selectPeriodTab.Tab_1')}${__d}${i18n_t('selectPeriodTab.Tab_2')}`,
            startTimeFrom: + new Date(t_s - 24 * 60 * 60 * 1000),
            tab: 10,
            historyFlag: 0,
            week_str: format_week(t_s - 24 * 60 * 60 * 1000)
        });
    }
    query_form_date_arr.value = arr;
}