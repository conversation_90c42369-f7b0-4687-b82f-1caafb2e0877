<!--
 * @FilePath: /project/caopan/pages/risk_control_set/risk_task_center/components/task_center/index.vue
 * @Description: 任务中心
-->

<template>
  <div class="risk_task_center" :style="{ height: `${main_area_height}px` }">
    <div class="top_banner">
      <!-- Cash -->
      <p>Hi {{ username }}</p>
    </div>
    <q-card class="my-card" style="width: 50%">
      <q-card-section class="title row items-center">
        <img src="/assets/icon/icon_tasklist.svg" class="img" alt="" />
        <!-- 待办事项统计 -->
        <div>{{ i18n_t('approval_tasks.tasks40') }}</div>
        <div class="btn" @click="refresh_timer(1)">
          <!-- 刷新180s -->
          {{ i18n_t('approval_tasks.tasks08') }}{{ refreshTime }}S
        </div>
      </q-card-section>
      <section class="bottom_warning todo todo_name" >
        <div v-for="(item, index) in allData_clum" :key="item.key" class="item" @click="router_jump(item)">
          <!-- v-if="btn_permissions(item.check) || btn_permissions(item.operate_click)" -->
          <div v-if="btn_permissions(item.check) || btn_permissions(item.operate_click)">
            <p class="num ">{{ allData[item.key] }}</p>
            <p class="text-panda-text-light ">{{ item.text }}</p>
          </div>
        </div>
      </section>
    </q-card>
    <q-card class="my-card" style="width: 50%">
      <q-card-section class="title row items-center">
        <img src="/assets/icon/icon_riskalert.svg" class="img" alt="" />
        <!-- 事件预警 -->
        <div>{{ i18n_t('approval_tasks.tasks27') }}</div>
        <!-- 刷新 -->
        <div class="btn" @click="refresh_timer(0)">{{ i18n_t('approval_tasks.tasks08') }}{{ refreshTime }}S</div>
      </q-card-section>
      <section class="todo bottom_warning sjnameid" >
        <div v-for="item in earlyWarning_clum" :key="item.key" class="item" @click="router_jump(item)">
          <!-- v-if="btn_permissions(item.check) || btn_permissions(item.operate_click)" -->
          <div v-if="btn_permissions(item.check) || btn_permissions(item.operate_click)">
            <p class="num">
              <iButton class="loading" v-if="get_loading(item.loading)?.value" loading shape="circle"></iButton>
              <span v-else> {{ isNaN(earlyWarning[item.key]) ? '-' : earlyWarning[item.key] }} </span>
            </p>
            <!-- 预警类型名称 -->
            <p class="text-panda-text-light">{{ item.text }}</p>
          </div>
        </div>
      </section>
    </q-card>
  </div>
</template>
<script setup>
  import { btn_permissions } from "src/output/common/project-common.js";
// import breadcrumbsPart from "src/mixins-saicheng/footer/breadcrumbsPart.js";
import {i18n} from 'src/boot/common/i18n';
import { allData_clum,earlyWarning_clum } from "project/caopan/pages/risk_control_set/risk_task_center/components/task_center/config/tablecolumns_config.js";

import {project_caopan_pages_risk_control_set_risk_task_center_components_task_center_componsable_composable_fn} from "project/caopan/pages/risk_control_set/risk_task_center/components/task_center/componsable/index.js"
  
  import {  project_caopan_pages_risk_control_set_risk_task_center_components_task_center_componsable_variable_componsable_fn} from "project/caopan/pages/risk_control_set/risk_task_center/components/task_center/componsable/variable.js"
  
 const base_payload =
 project_caopan_pages_risk_control_set_risk_task_center_components_task_center_componsable_variable_componsable_fn();
  
const {
  main_area_height,
    //定时刷新 60s
    refreshTime,
    // 自动刷新定时器 60s
    timer,
    //代办数据
    allData,
    //  预警数据
    earlyWarning,
    // 事件预警
    username, // 存储用户名称
    loading1,
    loading2,
    orderno,
} = base_payload


  // mixins: [breadcrumbsPart, ...mixins],
  
const {
    get_taskcenter_data,
    taskcenter_fk,
    get_groupOrderwarn,
    feature_statistics,
    get_username,
    get_loading,
    refresh_timer,
    router_params, 
    router_jump,
    compute_scrollarea_style
} = project_caopan_pages_risk_control_set_risk_task_center_components_task_center_componsable_composable_fn(base_payload)

</script>
<style lang="scss" scoped>
.risk_task_center {
  margin: 30px;
}
.loading {
  background-color: transparent;border-color: transparent;
}
.loading:before {
  background: transparent;
}
.top_banner {
  width: 50%;
  min-height: 100px;
  background: url("/assets/icon/taskcenter_image.png") no-repeat
    100%/100%;
  margin-bottom: 30px;

  p {
    font-size: 22px;
    color: #fff;
    padding: 10px 40px;
  }
}

.my-card {
  margin-bottom: 30px;

  .title {
    background-color: rgba(41, 50, 67, 1);
    font-size: 18px;
    color: #fff;
    letter-spacing: 2px;

    .img {
      margin-right: 10px;
    }

    .btn {
      color: rgba(255, 186, 114, 1);
      margin-left: 16px;
      border: 1px solid rgba(52, 64, 85, 1);
      padding: 6px;
      font-size: 12px;
      letter-spacing: 1px;
      border-radius: 2px;
    }
  }

  .todo {
    background-color: rgba(30, 39, 53, 1);
    text-align: center;
    padding: 30px;
    min-height: 160px;

    .num {
      font-size: 22px;
      color: #fff;
      transition: font-size 1s;
      letter-spacing: 2px;
      min-height: 40px;
      cursor: pointer;
    }

    .num:hover {
      color: rgba(0, 169, 151, 1);
    }
  }

  .bottom_warning {
    display: flex;
    align-items: center;

    .item {
      flex: 1;

    }
  }
  .sjnameid{
    :nth-last-child(2){
      p{
        // color:red;
      }
    }
  }
  .todo_name{
    :nth-last-child(1){
      p{
        // color:red;
      }
    }
  }
}
</style>
