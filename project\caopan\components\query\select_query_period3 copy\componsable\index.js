import { onMounted, onBeforeUnmount } from 'vue';
import {
  compute_current_h_gmt_add_8,
  handle_history_date_confirm,
  edit_sort_btn,
  sort_save,
  cancel_sort,
  toOperate,
  init_query_form_date_arr,
  query_form_date_tab_click,
  default_data,
  startTime_limit,
} from 'project/caopan/components/query/select_query_period3/module/index.js';
import { emit_generator } from 'project/caopan/components/query/select_query_period3/emit/index.js';
import { watcher_register } from 'project/caopan/components/query/select_query_period3/watch/index.js';

import { src_componsable_util_watcher_register_componsable_fn } from 'src/output/common/componsable-common';
import { get_serverTime, set_history_startTimeFrom } from 'src/output/common/store-common';

export const project_caopan_components_query_select_query_period3_select_query_period3_composable_fn = raw_payload => {
  const all_emists = emit_generator(raw_payload);

  const payload = {
    ...raw_payload,
    ...all_emists,
  };

  src_componsable_util_watcher_register_componsable_fn(watcher_register(payload));

  const { props, route } = payload;
  const serverTime = get_serverTime();

  onMounted(() => {
    // created
    const {
      date_option,
      date_option2,

      show_more_query,
      optional_events_num,
      routeName,
      timer,
    } = payload;

    date_option.value = {
      disabledDate(arg) {
        const now = serverTime.value || new Date().getTime();
        return arg && arg.valueOf() > now - 12 * 60 * 60 * 1000;
      },
    };
    date_option2.value = {
      disabledDate(arg) {
        const now = serverTime.value || new Date().getTime();
        if (new Date(now).getHours() < 12) {
          return arg && arg.valueOf() < now + 5 * 24 * 60 * 60 * 1000;
        } else {
          return arg && arg.valueOf() < now + 6 * 24 * 60 * 60 * 1000;
        }
      },
    };
    // init_query_form_date_arr(payload);
    // 控制更多查询初始值
    show_more_query.value = props.more_query.value;
    // current_.value.then(res => {
    //   query_form_date_tab_click(default_select.value);
    // });

    optional_events_num.value = sessionStorage.getItem('optional_events_num') || 0;
    routeName.value = route.name;

    // mounted
    timer.value = setInterval(() => compute_current_h_gmt_add_8(payload), 1000);
  });

  onBeforeUnmount(() => {
    const { timer } = payload;
    set_history_startTimeFrom(null);
    clearInterval(timer.value);
  });

  return {
    handle_history_date_confirm: () => handle_history_date_confirm(payload),
    edit_sort_btn: () => edit_sort_btn(payload),
    sort_save: () => sort_save(payload),
    cancel_sort: () => cancel_sort(payload),
    toOperate: () => toOperate(payload),
    query_form_date_tab_click: value => query_form_date_tab_click(payload, value),
    init_query_form_date_arr: () => init_query_form_date_arr(payload),
    default_data: () => default_data(payload),
    emit_query_period_change_not_tab_8: () => emit_query_period_change_not_tab_8(payload),
    startTime_limit,
  };
};
