import { set_categoryset_show_arr } from "src/output/common/store-common.js";
import { set_periods_map } from "project/caopan/pages/trader/manage/config/match_config.js";
import { sportId_tab_name } from "project/caopan/pages/sets/config/config.js";
import { SessionStorage } from "quasar";
let i18n2 = SessionStorage.getItem("i18n") || "zs";

// 获取危险球队联赛等级
export const getDangerTermLevel = (payload, index) => {
  const { teamList } = props.value.row;
  if (teamList.length >= 2) {
    const team = teamList[index];
    if (team.dangerTeam && team.dangerTeam.status == 1) {
      return team.dangerTeam.riskLevel ? team.dangerTeam.riskLevel : 0;
    }
  }
  return 0;
};
export const all_play = (payload, row) => {
  //所有玩法弹窗
  // console.log('row-----------------',row)
  let targetTabs =
    sportId_tab_name[row.sportId][liveOddBusiness.value == 1 ? 0 : 1];
  console.log(2333, row.sportId, liveOddBusiness.value, targetTabs);
  let ciyao = 0;
  for (let i = 0; i < targetTabs.length; i++) {
    let tab = targetTabs[i];
    console.log(2333, tab);
    if (
      btn_permissions("sets_page:" + tab) ||
      ((tab.includes("Play") || tab.includes("play")) &&
        btn_permissions("sets_page:secondaryPlay"))
    ) {
      ciyao = ciyao + 1;
    } else {
      console.log(2333, 302, tab, "无次要");
    }
  }
  if (!ciyao) return;

  let obj_language = {
    tournamentNames: rebuild_to_language_obj(row.tournamentNames),
  };
  let name = compute_table_item_show_name(
    obj_language,
    i18n2,
    "tournamentNames"
  );
  const { href } = router.resolve({
    name: "sets",
    path: "/sets",
    query: {
      // 公用参数
      sportId: row.sportId,
      matchId: row.matchId, //【比分矩阵、事件、结算审核】
      // 及时注单参数--------
      // 玩法ID
      playIds: "",
      // row.categoryIds.join(','),
      type: 2,
      // 盘口类型 1 滚球 否则为早盘
      liveType: liveOddBusiness.value == 1 ? 1 : "",
      // 事件页面参数-----
      teamList: JSON.stringify(row.teamList),
      // 足球次要玩法参数添加
      // matchVolumeId: queryform_form.value.quantity_type,
      set_market_odds_kind: get_market_odds_kind.value,
      standardTournamentId: row.standardTournamentId, //结算审核用到的参数
      matchType: liveOddBusiness.value == 1 ? 2 : 1,
      matchSnapshot: row.matchSnapshot,
      tournamentId: row.standardTournamentId, //比分矩阵用到的参数
      name, //比分矩阵用到的参数
      matchDate: row.matchStartDate, //比分矩阵用到的参数
      matchManageId: row.matchManageId, //比分矩阵用到的参数
      categorySetId: row.categorySetId, //赛事阶段id
      score: row.score, //比分
      // index: odd_index.value, //位置
      index: 1,
      neutralGround: row.neutralGround, //是否为中立场。取值为 0  和1  。  1:是中立场，0:非中立场。操盘人员可手动处理
      period: row.period, // 当前赛事阶段 篮球专用，足球则没有
      secondsMatchStart: row.secondsMatchStart, // 备忘录专用
      // dataSourceCode: row.dataSourceMap['37'] || row.dataSourceMap['38'] // 数据源
      volume_switch_status: get_volume_switch_status.value, // 是否藏单
    },
  });
  window.open(
    href,
    "_blank",
    "height=700, width=1900, top=100,left=250, toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no"
  );
};
export const rebuild_to_language_obj = (payload, arg) => {
  let return_obj = {};
  if (arg && Array.isArray(arg)) {
    arg.forEach((x) => {
      x.languageType && (return_obj[x.languageType] = x.text);
    });
  }
  if (arg && typeof arg == "object") {
    let keys = Object.keys(arg);
    keys.forEach((x) => {
      let item = arg[x];
      item.languageType && (return_obj[item.languageType] = item.text);
    });
  }
  return return_obj;
};
/**
 * @description 盘比分只显示在全场比分这里
 * @return {undefined} undefined
 */
export const computed_set_score_show = (payload, row) => {
  const num_of_set = row.categorySetId % 10000; // 第几局
  if (num_of_set == 1) {
    return true;
  } else {
    return false;
  }
};
/**
 * @description 根据categorySetId返回不同的阶段数组
 * @param  {Object} row 表格行数据对象
 * @return {Array} 返回不同的阶段数组
 */
export const computed_set_by_id = (payload, row) => {
  const { sportId, categorySetId, roundType } = row;
  const set_periods = set_periods_map[sportId] || [];
  const num_of_set = categorySetId % 10000; // 第几局
  if (num_of_set == 1) {
    return set_periods.slice(0, roundType);
  } else {
    return [set_periods[num_of_set - 2]];
  }
};
/**
 * @description 根据盘数取对应的比分
 * @return {undefined} undefined
 */
export const computed_score_set = (payload, period) => {
  const { scoreVos } = props.value.row;
  if (scoreVos) {
    const item = scoreVos.find((item) => item.period == period);
    if (item) {
      return item.setScore;
    }
  }
};
export const computed_props_row_scoreVos = (payload, row) => {
  if (
    row.scoreVos &&
    liveOddBusiness.value == 1 &&
    row.matchSnapshot === 0 &&
    row.period > 0
  ) {
    return true;
  } else {
    return false;
  }
};
/**
 * @description 合计行比分计算
 * @param  {Array} scoreVos 各个阶段的比分
 * @param  {Number} type 总比分 差 总
 * @return {String} 9-8 1 17
 */
export const computed_props_row_scoreVos_score = (payload, type) => {
  const { categorySetId, sportId, scoreVos } = props.value.row;
  const num_of_set = categorySetId % 10000; // 第几局
  const set_periods = set_periods_map[sportId] || [];
  const period = set_periods[num_of_set - 2];
  let scoreVosList = [];
  if (num_of_set == 1) {
    scoreVosList = scoreVos;
    // console.log("scoreVosList", scoreVosList);
  } else {
    scoreVosList = scoreVos.filter((item) => item.period == period);
  }

  let sum1 = 0,
    sum2 = 0;

  scoreVosList.forEach((item) => {
    const [score1, score2] = !item.setScore
      ? [0, 0]
      : lodash.split(item.setScore, ":", 3);
    sum1 += Number(score1);
    sum2 += Number(score2);
  });
  if (type == 1) {
    return `${sum1}-${sum2}`;
  } else if (type == 2) {
    return Math.abs(sum1 - sum2);
  } else {
    return sum1 + sum2;
  }
};
// 控制玩法集显隐
export const togglePlay = (payload, { unid }) => {
  const mArr = [...categoryset_show_arr.value];
  let index = mArr.indexOf(unid);
  if (index == -1) {
    mArr.push(unid);
  } else {
    mArr.splice(index, 1);
  }
  set_categoryset_show_arr(mArr);
  // sessionStorage.setItem("categoryset_show_arr", JSON.stringify(mArr));
};
// 收藏
export const collection = (payload, row, type) => {
  if (row.matchCollectStatus) {
    let data = {
      sportId: select_sportId.value,
      matchId: row.matchId,
      userId: 1,
      status: 0,
      type: 1,
    };
    update_collect(data, false); //删除我的收藏
  }
};

export const let_the_ball = (payload, index) => {
  let handicapIds = [172, 176]; // 让球ids
  //让球方显示红色字体
  let matchMarketVoList = [];
  handicapIds.forEach((id) => {
    const item = props.value.row[id];
    if (item) {
      matchMarketVoList = item.matchMarketVoList;
    }
  });
  if (matchMarketVoList.length == 0) return null;
  let data;
  if (
    matchMarketVoList &&
    matchMarketVoList.length &&
    matchMarketVoList[0].oddsFieldsList
  ) {
    for (var i = 0; i <= matchMarketVoList[0].oddsFieldsList.length; i++) {
      if (matchMarketVoList[0].oddsFieldsList[index]) {
        data = matchMarketVoList[0].oddsFieldsList[index].nameExpressionValue;
        if (Number(data) < -0.1) {
          return "let_the_ball";
        }
      }
    }
  }
};

export const copy = (payload, tiem) => {
  //复制日期单号
  let oInput = document.createElement("input");
  oInput.value = tiem;
  document.body.appendChild(oInput);
  oInput.select(); // 选择对象;
  document.execCommand("Copy");
  Message.success(`${i18n_t("champion_plays.text69")}${tiem}`);
  oInput.remove();
};

export const computed_team_list_name = (payload, arg) => {
  const { item, show_which } = arg;
  let suffix = "";
  if (item && typeof item == "object" && item.matchPosition === "home") {
    if (show_which === 1) {
      //  中立场
      suffix = `(${i18n_t("traderTable.text103")})`;
    } else {
      // 主
      suffix = `(${i18n_t("common.home")})`;
    }
  }
  let names = item.names;
  let name = item.names;
  if (names && typeof names == "object") {
    name = compute_table_item_show_name(item, i18n2, "names");
  }
  name = name || "";
  return {
    tite: name + suffix,
    sizi: (name + suffix).length,
  };
};

export const text_color2 = (payload, categorySetId) => {
  //字体颜色
  if (categorySetId == main_categoryset_id.value) {
    return "";
  } else {
    return "yellow";
  }
};
export const text_color = (payload, categorySetId) => {
  //字体颜色
  if (categorySetId == main_categoryset_id.value) {
    return "blue_color";
  } else {
    return "yellow_color";
  }
};
export const text_bg = (payload, categorySetId) => {
  //文案背景色
  if (categorySetId == main_categoryset_id.value) {
    return "blue_bg";
  } else {
    return "yellow_bg";
  }
};
export const text_bg2 = (payload, categorySetId) => {
  //文案背景色
  if (categorySetId == main_categoryset_id.value) {
    return "blue_bg2";
  } else {
    return "yellow_bg2";
  }
};

//操盘模式图标
//返回操盘方式 图标
// panda_new_cp_icon_11  0,null   A   自动操盘
// panda_new_cp_icon_10  1        M	  手动操盘
// panda_type6	         2	      A+	自动加强操盘
// panda_new_cp_icon_13  0,1      M+A
// panda_type1	         0,2	    A+A+
// panda_type3	         1,2	    M+A+
// panda_type2	         0,1,2	  M+A+A+
export const get_icon = (payload, tradeType) => {
  if (String(tradeType) == "null" || String(tradeType) == "undefined") {
    return "panda_new_cp_icon_11";
  }
  if (typeof tradeType === "number") {
    switch (tradeType) {
      case 0:
        return "panda_new_cp_icon_11";
      case 1:
        return "panda_new_cp_icon_10";
      case 2:
        return "panda_type6";
    }
  }
  if (Array.isArray(tradeType)) {
    if (tradeType.length == 1) {
      switch (tradeType[0]) {
        case 0:
          return "panda_new_cp_icon_11";
        case 1:
          return "panda_new_cp_icon_10";
        case 2:
          return "panda_type6";
      }
    } else if (tradeType.length == 3) {
      return "panda_type2";
    } else {
      if (tradeType.indexOf(0) > -1) {
        if (tradeType.indexOf(1) > -1) {
          return "panda_new_cp_icon_13";
        } else {
          return "panda_type1";
        }
      } else {
        return "panda_type3";
      }
    }
  }
};
