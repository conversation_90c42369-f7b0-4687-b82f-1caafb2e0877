import { lodash, Message } from "src/output/common/project-common";

// 玩法相关初始化
export const plays_init = (payload) => {
  const { params, checkedSportIdList, playObj, row, showPlayList_filter } = payload;
  if (params.value.sportIdList) {
    checkedSportIdList.value = params.value.sportIdList;
    playObj.value = row.value.playObj
    nextTick(() => {
      showPlayList_filter.value = row.value.showPlayList_filter.map(item => {
        return {
          ...item,
          show_playIds: item.show_playIds?.map(v => {
            return {
              ...v,
              selected: params.value.playIdList.some(k => k == v.id) // 删除上一次的选中状态
            }
          })
        }
      })
    })
  }
}

export const home_away_change = (payload, val) => {
  const { language_type, home_away } = payload;
  language_type.value = lodash.cloneDeep(language_type)
  home_away.value = val
  change_template()
}

// 获取模板数据
export const set_auto_notice_template = (payload) => {
  const { cancel_data, row, list_select_value } = payload;
  let obj = {}
  let template = cancel_data.value[row.value.cancel_num].template.map(item => {
    obj = {}
    let no_time = {
      zs: "无时间",
      en: 'no tome'
    }
    let result = {
      zs: "含明确赛果",
      en: 'definite result'
    }
    let playing = {
      zs: list_select_value.value == 'tong_var' ? '含玩法' : "含玩法及时间区间",
      en: list_select_value.value == 'tong_var' ? 'Incl Play' : 'playing and time intervals'
    }
    // 2847处理 玩法
    if (item.indexOf('plays') > -1) {
      obj.label = i18n_t('payout_page.text43')
    } else if (item.indexOf('no_time') > -1) {
      if (item == 'tong_no_time') {
        obj.label = `${i18n_t("payout_page.text34")}-${no_time[lang]}`
      } else {
        obj.label = cancel_data.value[row.value.cancel_num][lang] // + "-"+ no_time[lang]
      }
    } else if (item.indexOf('amidithion') > -1) {
      if (item == 'tong_amidithion') {
        obj.label = i18n_t("payout_page.text34") + "-" + result[lang]
      } else {
        obj.label = cancel_data.value[row.value.cancel_num][lang] + "-" + result[lang]
      }
    } else {
      if (item == 'tong_playing_time') {
        obj.label = i18n_t("payout_page.text34") + "-" + playing[lang]
      } else {
        obj.label = cancel_data.value[row.value.cancel_num][lang] + "-" + playing[lang]
      }
    }
    obj.value = item
    return obj
  })
  return template
}

// 结算2.0
export const set_auto_notice_template_v2 = (payload, val) => {
  const { allocation_v2, incorrect_result } = payload;
  let array = allocation_v2
  // 二次结算原因 赛果错误 需要有弹窗
  if (val) {
    array = incorrect_result
  }
  let template = array.map(item => {
    const label = item.isPlayer ? `${item[lang]}${add_player_txt[lang] || ''}` : item[lang]
    return {
      label,
      value: item.template
    }
  })
  return template
}


// 阶段列表 玩法列表 分类
export const set_stage_games_template = (payload, val) => {
  const { stage_list, row, stage_list_template, game_list_template } = payload;
  let arr1 = stage_list[row.value.sportId - 1]
  // 阶段列表
  stage_list_template.value = arr1.map(item => {
    const label = item.isPlayer ? `${item[lang]}${add_player_txt[lang] || ''}` : item[lang]
    return {
      label,
      value: item.template
    }
  })
  // 玩法列表
  if (val) {
    let list = []
    arr1.map(item => {
      if (item.template == val) {
        let arr2 = item.game_list
        arr2.map(item2 => {
          const label = item2.isPlayer ? `${item2[lang]}${add_player_txt[lang] || ''}` : item2[lang]
          let obj = {
            label,
            value: item2.template
          }
          list.push(obj)
        })
      }
    })
    game_list_template.value = list
  }
}


// 切换模板
export const change_template = (payload) => {
  const { query, league_factor, league_team, language_type, obj_settlement_v2, is_show_time, auto_notice_config, hide_time_type_list, list_select_type, language } = payload;
  query.value.stage_type = ''
  query.value.game_type = ''
  query.value.reschedule_time = null
  query.value.error_home_result = ''
  query.value.error_away_result = ''
  query.value.home_result = ''
  query.value.away_result = ''
  query.value.team_type = false
  league_factor.value = false
  league_team.value = false
  language_type.value = lodash.cloneDeep(language_type)
  if (obj_settlement_v2.value.v2) {
    set_list_data_v2()
    set_stage_games_template()
    return
  }
  // 产品要求改成14场景模板都需要时间
  is_show_time.value = auto_notice_config.value.includes(Number(row.value?.cancel_num)) && !hide_time_type_list.includes(list_select_type.value)
  set_reset()
  set_show_type()
  set_list_data()
  language.value = lang
}


// 控制中间部分的显示 和隐藏
export const set_show_type = (payload) => {
  const { show_type, row, list_select_type, home_away } = payload;
  Object.keys(show_type.value).forEach(item => {
    show_type.value[item] = false
  })
  if ([36, 39].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('correct_team') > -1) || (list_select_type.value.indexOf('home_away') > -1)) {
      show_type.value.incorrect_team = true
      home_away.value = ''
    }
  }
  if ([38].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('incorrect_score') > -1)) {
      show_type.value.incorrect_score = true
    }
  }
  if ([37].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('incorrect_league') > -1)) {
      show_type.value.incorrect_league = true
    }
  }
  if ([33].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('com_rules') > -1)) {
      show_type.value.com_rules = true
    }
  }
  if ([40].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('course_') > -1)) {
      show_type.value.course_info = true
    }
  }
  if ([41].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('event_') > -1)) {
      show_type.value.event_info = true
    }
  }
  if ([28].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('evmidway_forgo') > -1)) {
      show_type.value.evmidway_forgo = true
    }
  }
  if ([30].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('statistics_') > -1)) {
      show_type.value.statistics_info = true
    }
  }
  if ([23, 25].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('unable_result') > -1) || (list_select_type.value.indexOf('player_lost') > -1)) {
      show_type.value.unable_result = true
    }
  }
  if ([31].includes(Number(row.value.cancel_num))) {
    if ((list_select_type.value.indexOf('pitcher_change') > -1)) {
      show_type.value.pitcher_change = true
    }
  }
}


// 填写项数据重置
export const set_reset = (payload) => {
  const { query } = payload;
  query.value = {}
  query.value = {
    home_score: "", // 主队比分
    away_score: '', //客队比分
    cause: "", // 取消原因
    the_right_team: "",  //正确队伍资讯
    start_time: '',  //查询开始时间 含时间区间
    end_time: '', // 查询结束时间  含时间区间
    play_name: "", // 玩法名称
    session_info: '', // 场次资讯
    score_figure: "", // 比分数字
    league_info: '', // 联赛资讯
    the_right_league: "",  //正确联赛资讯
    home_away: "", // 主客场资讯
    com_rules_info: "", //赛制资讯
    course_info: "", // 赛程资讯
    event_info: '', // 事件资讯
    player_name: "", // 选手名称
    evmidway_forgo_info: "", // 弃赛原因
    statistics_info: '', //统计资讯
    factor: "", // 取消因素
    pitcher_before: "", // 变更前投手名称
    pitcher_after: "", // 变更后投手名称
  }
}

// 玩法名称多语言处理
export const get_paly_names_i18n = (payload, language) => {
  const { showPlayList_filter, lang_interaction_map, params } = payload;
  const lang = lang_interaction_map[language].toLowerCase().replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
  return params.value.playIdList.map(item => {
    let f_obj = {}
    showPlayList_filter.forEach(v => {
      f_obj = v.playIds.find(v => v.id == item)
    })
    return f_obj ? f_obj[`playName${lang}`] : ''
  }).join(",")
}

// 设置公告内容
export const set_notice = (payload, language = 'zs', cancelCause) => {
  const { data_text, list_saishi, language_type, query, list_select_type, times, params, row, sphere_range_map, score_type_map, game_format_map } = payload;
  const { away, home, tournament } = data_text.value[language];
  let type = list_saishi.value[row.value.sportId][language];
  let home_away = language_type.value.find(item => item.value == language);
  const { home_score, away_score } = query.value;
  let obj = {
    cause: cancelCause, // 原因
    type, //公告类型
    kick_off_time: get_itme(data_text.value.beginTime, 'yyyy/mm/dd hh:ii') + "（GMT+8）", // 开赛时间
    league_name: tournament, // 联赛名称
    against: `${home} VS ${away}`,
    template: list_select_type.value,
    language,
    start_time: get_itme(new Date(times.value.val1).getTime()),
    end_time: get_itme(new Date(times.value.val2).getTime()),
    home_away: home_away.home || home_away.away ? `${home_away.home}${home_away.away ? `${home_away.home ? `-` : ``}${home_away.away}` : ''}` : '',//队伍资讯
    score_figure: (home_score && away_score) ? home_score + '-' + away_score : '', // 比分
    session_info: query.value?.session_info || '', // 比分 场次资讯
    the_right_league: home_away.incorrect_league || '', // 正确联赛
    com_rules_info: query.value.com_rules_info ? `赛制错误 (${query.value.com_rules_info})` : '', // 赛制错误
    course_info: home_away.course_info || '', // 赛程错误
    error_event_info: home_away.error_event_info || '',  //事件错误 (错误事件名)
    right_event_info: home_away.right_event_info || '',  //事件错误 (正确事件名)
    player_name: home_away.player_name || '', // 投手名称
    evmidway_forgo_info: home_away.evmidway_forgo_info || '', // 弃赛原因
    statistics_info: home_away.statistics_info || '', //统计错误
    pitcher_before: home_away.pitcher_before || '', // 投手
    pitcher_after: home_away.pitcher_after || '', // 变更后投手
    factor: home_away.factor || '', // 取消因素
    bet_time: `${params.value.startTime?.replace(/-/g, '/')} - ${params.value.endTime?.replace(/-/g, '/')}` || '', // 左侧搜索条件 投注时间
    cancel_num: row.value.cancel_num,
  }
  // 有没有比分错误的范围和比分类型
  if (query.value?.sphere_range) {
    let sphere_range = sphere_range_map.value.find(item => item.type == query.value?.sphere_range) || {};
    obj.session_info = (sphere_range ? sphere_range[language] : '');
  }
  if (query.value?.score_type) {
    let score_type = score_type_map.value.find(item => item.type == query.value?.score_type) || {};
    obj.session_info += (score_type ? score_type[language] : '');
  }
  // 玩法发id转文字
  if (['match_odds_han_plays', 'incorrect_score_no_time', 'incorrect_score_amidithion'].includes(list_select_type.value) && params.value.playIdList) {
    obj.play_name = get_paly_names_i18n(language);
  } else {
    obj.play_name = marketname.value ? marketname.value[language] : ''; //玩法名称
  }
  // 中途弃赛
  if (query.value?.game_format) {
    let game_format = game_format_map.value.find(item => item.type == query.value?.game_format) || {};
    obj.session_info += (game_format ? game_format[language] : '');
  }
  if (query.value.score_error == 'all') {
    let all = {
      zs: "全场",
      en: "Fulltime"
    }
    obj.session_info = language == 'zs' ? all.zs : all.en;
  }
  // 范围 第多少局
  if (query.value.score_error == 'single') {
    let single = {
      zs: '第',
      en: "Set",
      en_1: "Game"
    }
    let number = ['zs', 'zh'].includes(language) ? single.zs : single.en;
    if (row.value.sportId == 5) {
      if (query.value?.score_tray) {
        obj.session_info = `${number}${['zs', 'zh'].includes(language) ? '' : ` `}
            ${query.value?.score_tray}${['zs', 'zh'].includes(language) ? '盘' : `${['zs', 'zh'].includes(language) ? '' : ` `}`}`;
      }
      if (query.value?.score_single) {
        number = !['zs', 'zh'].includes(language) ? single.en_1 : number
        obj.session_info += `${number}${['zs', 'zh'].includes(language) ? '' : ` `}
            ${query.value?.score_single}${['zs', 'zh'].includes(language) ? '局' : `${['zs', 'zh'].includes(language) ? '' : ` `}`}`;
      }
    } else {
      if (query.value?.score_single) {
        number = !['zs', 'zh'].includes(language) ? single.en_1 : number;
        obj.session_info = `${number}${['zs', 'zh'].includes(language) ? '' : ` `}
            ${query.value?.score_single}${['zs', 'zh'].includes(language) ? '局' : `${['zs', 'zh'].includes(language) ? '' : ` `}`}`;
      }
    }
  }
  // 赛制错误 才有 赛制资讯
  if (query.value?.game_format) {
    let game_format = game_format_map.value.find(item => item.type == query.value?.game_format) || {};
    obj.com_rules_info = (game_format ? '(' + game_format[language] + ')' : '');
  }
  const payload = { ...query.value, ...obj };
  return notice_config(payload);
}


export const notice_config_v2 = (payload, language = 'zs') => {
  const { data_text, list_saishi, language_type, query, list_select_type, row, stage_list } = payload;
  const { away, home, tournament, playNames } = data_text.value[language];
  let type = list_saishi.value[obj_settlement_v2.value.sportId][language];
  let home_away = language_type.value.find(item => item.value == language) || {};
  let obj = {
    type, //公告类型
    kick_off_time: get_itme(data_text.value.beginTime, 'yyyy/mm/dd hh:ii'), // 开赛时间
    league_name: tournament, // 联赛名称
    against: `${home} VS ${away}`,
    template: list_select_type.value,
    language,
    home,
    playNames,
    reschedule_time: get_itme(new Date(query.value.reschedule_time).getTime()) + '(GMT+8)', // 更新开赛时间
    stage_game_value: home_away.stage_game_value, // 阶段&玩法名称
    result: home_away.result, // 正确赛果
    error_result: home_away.error_result,// 错误赛果
    again_time: get_itme(new Date(query.value.reschedule_time).getTime()) + '(GMT+8)', // 复赛时间
  }
  if (['incorrect_result_player', 'match_interrupted_player_', 'league_team_name_update_player', 'delay_unclear_result_player'].includes(list_select_type.value)) {
    let marketname = marketname.value[language] || '';
    obj.play_name = '';
    marketname.split(',').forEach((item, index) => {
      if (index > 4) {
        return
      } else {
        obj.play_name += ',' + item;
      }
    })
    obj.play_name = obj.play_name.slice(1, obj.play_name?.length);
    obj.settleNum = obj_settlement_v2.value.settleNum;
  }
  if (['incorrect_result_player', 'incorrect_result', 'delay_unclear_result'].includes(list_select_type.value)) {
    const { stage_type, game_type, error_home_result, error_away_result, home_result, away_result } = query.value;
    let stage_name, game_name, comma;
    let arr = stage_list[row.value.sportId - 1]
    arr.forEach(item => {
      if (item.template == stage_type) {
        stage_name = item[language];
        item.game_list.forEach(item2 => {
          if (item2.template == game_type) {
            game_name = item2[language];
          }
        })
      }
    })
    comma = stage_name && stage_name != '-' && game_name && game_name != '-' ? ', ' : '';
    stage_name = stage_name && stage_name != '-' ? stage_name : '';
    game_name = game_name && game_name != '-' ? game_name : '';
    obj.stage_game_value = (stage_name || game_name) ? `【${stage_name + comma + game_name}】` : '';
    obj.error_result = error_home_result && error_away_result ? `[${error_home_result + '-' + error_away_result}]` : '';
    obj.result = home_result && away_result ? `[${home_result + '-' + away_result}]` : '';
  }
  // 开启增加队伍资讯才赋值
  if (league_team.value) {
    obj.home = home_away.home || '';
    obj.away = home_away.away || '';
  } else {
    // 不增加队伍资讯 重置数据
    obj.team_type = '';
  }
  // 开启增加队伍资讯才赋值
  if (league_factor.value) {
    obj.factor = home_away.factor || '';
  }
  let params = { ...query.value, ...obj, sportId: row.value.sportId };
  let robj = notice_config_v2(params);
  return robj;
}

export const set_list_data = (payload) => {
  const { auto_notice_config, list_saishi, language_type, cancel_data, list_data, row, marketname, list_data_copy } = payload;
  if (auto_notice_config.value.includes(row.value.cancel_num)) {
    language_type.value.forEach(item => {
      const cancelCause = cancel_data.value[row.value.cancel_num][item.value];
      let type = list_saishi.value[row.value.sportId][item.value];
      list_data.value[item.value] = {
        title: `【${type}】${cancelCause}`,
        content: set_notice(item.value, cancelCause),
        marketname: marketname.value ? marketname.value[item.value] : ''
      }
    })
    list_data_copy.value = { ...list_data.value };
  } else {
    // 生成 时间数据 1 生成 2取消
    generate_template(1)
  }
}


// 结算2.0 公告模板
export const set_list_data_v2 = (payload, bool, val) => {
  const { list_select_type, list_saishi, language_type, allocation_v2, list_data, marketname, list_data_copy } = payload;
  if (bool) { // 选择了第一个下拉框(阶段)后
    set_stage_games_template(val);
  }
  // 结算2.0 二次结算原因 赛果错误 需要弹窗
  // let list_select_type = list_select_type.value
  if (list_select_type.value == 'incorrect_result_player') {
    list_select_type.value = 'incorrect_result';
  }
  let obj_info = allocation_v2.find(obj => obj.template == list_select_type);
  language_type.value.forEach(item => {
    let type = list_saishi.value[obj_settlement_v2.value.sportId][item.value]
    list_data.value[item.value] = {
      title: `【${type}】` + obj_info[item.value],
      content: notice_config_v2(item.value),
      marketname: marketname.value ? marketname.value[item.value] : '',
      isPlayer: obj_info.isPlayer
    }
  })
  list_data_copy.value = { ...list_data.value };
}


export const set_change_language = (payload, language) => {
  const { language } = payload;
  language.value = language;
}


// 现实编辑输入框
export const set_show_input = (payload, bool) => {
  const { no_show_input } = payload;
  no_show_input.value = bool;
}


// 关闭弹窗
export const close_cmp = () => {
  emit('set_close', false)
}


// 取消编辑
export const cancel_alter = (payload) => {
  const { list_data, list_data_copy } = payload;
  list_data.value = { ...list_data_copy.value };
  set_show_input(true);
}


// 格式化时间
export const get_itme = (payload, timestamp, format = 'yyyy/mm/dd hh:ii:ss') => {
  if (timestamp == NaN || !timestamp) return ""
  return (formatDate(timestamp, format))
}


// 设置生成模板
export const generate_template = (payload, type = 1) => {
  const { times, list_select_type, row, list_select_value } = payload;
  // 是指取消生成带时间公告
  if (type == 2) {
    // 取消模板时，默认清空时间
    times.value = {
      val1: '', // 开始时间
      val2: '', // 结束时间
      val3: '', // 滚球时间
    }
    set_title_and_text(list_select_type.value, row.value.cancel_num, row.value.sportId)
    return
  }
  // 这里时生成对应模板 判断是否设置时间
  if (list_select_value.value == "tong_sai" ? times.value.val3 && times.value.val1 && times.value.val2 : times.value.val1 && times.value.val2) {
    // 获取选中时间模板对应值
    let str = get_select_value();
    if (!str) return;
    set_title_and_text(str, row.value.cancel_num, row.value.sportId)
  } else {
    // 生成没有时间的模板
    set_title_and_text(list_select_type.value, row.value.cancel_num, row.value.sportId)
  }
}


// 获取选中时间模板对应值
export const get_select_value = (payload) => {
  const { list_select, list_select_type, list_select_value } = payload;
  try {
    let obj = list_select.value[list_select_value.value].find(item => item.value == list_select_type.value);
    return obj?.shi_val;
  } catch (error) {
    Message.error('获取选中时间模板对应值，参数有问题');
  }
}

// 设置选中模板
export const set_select = (payload, val) => {
  const { is_show_time, no_start_time_type_list, times, row } = payload;
  // 赛事提前没有开始结束时间，只有滚球时间
  is_show_time.value = !no_start_time_type_list.includes(val);
  // 选择对应模板时，默认清空时间
  times.value = {
    val1: '', // 开始时间
    val2: '', // 结束时间
    val3: '', // 滚球时间
  }
  set_title_and_text(val, row.value.cancel_num, row.value.sportId)
}


// 构建玩法名称多语言对象
export const build_marketname = (payload) => {
  const { is_settlement_v2, language_list, data_text, marketname } = payload;
  let key_ = is_settlement_v2.value ? 'playNames' : 'playName';
  language_list.value.forEach(item => {
    // 语种
    let str = lodash.get(data_text.value, `${item}.${key_}`);
    if (!str && item == 'zs') {
      // 中文没有取英文
      str = lodash.get(data_text.value, `en.${key_}`);
    }
    if (!marketname.value) {
      marketname.value = {};
    }
    marketname.value[item] = str;
  })
}



export const get_sport_id = (payload) => {
  const { route, row } = payload;
  let res_sport_id = '';
  const { name, query } = route;
  if (name == 'settlements') { // 结算1.0 结算审核
    res_sport_id = query.sportId;
  } else if (name == 'event_review') { // 结算1.0 事件审核
    res_sport_id = query.spid;
  } else {
    res_sport_id = row.value.sportId || ''; // 结算2.0  冠军玩法 取消注单等
  }
  if (res_sport_id == '') {
    console.error('注意: 赛种id丢失');
    Message.error('注意: 赛种id丢失');
  }
  return res_sport_id;
}


// 过滤没有玩法ID 的内容
export const filter_playName = (payload, lang) => {
  const { row, data_text } = payload;
  if (row.value.marketId) {
    return data_text.value?.[lang]?.playName ? "【" + data_text.value?.[lang]?.playName + "】" : "【" + data_text.value?.['en']?.playName + "】";
  } else {
    return '';
  }
}


// 设置对应多语言标题和内容
export const set_title_and_text = (payload, str_key = 'tong_wu', cancel_num = 7, saishi_num = 1) => {
  const { list_data, other_data, row, cancel_data, marketname, allocation, list_data_copy } = payload;
  // 统一配置且是字符串，需要用eval把改字符串运行并返回最后字符串
  list_data.value = {
    zs: { // 【${list_saishi.value[saishi_num].zs}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.zs : `${cancel_data[cancel_num].zs}`,
      content: allocation[str_key]?.obj?.zs,  //eval把改字符串运行并返回最后字符串
      market: marketname.value?.zs || '' // 取出 玩法名称 中英文
    },
    en: { // 【${list_saishi.value[saishi_num].en}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.en : `${cancel_data[cancel_num].en}`,
      content: allocation[str_key]?.obj?.en,  //eval把改字符串运行并返回最后字符串
      market: marketname.value?.en || '' // 取出 玩法名称 中英文
    },
    yn: { // 【${list_saishi.value[saishi_num].yn}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.yn : `${cancel_data[cancel_num].yn}`,
      content: allocation[str_key]?.obj?.yn,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.yn || marketname.value?.en // 取出 玩法名称 中英文
    },
    zh: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.zh : `${cancel_data[cancel_num].zh}`,
      content: allocation[str_key]?.obj?.zh,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.zh || marketname.value?.zs // 取出 玩法名称 中英文
    },
    th: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.th : `${cancel_data[cancel_num].th}`,
      content: allocation[str_key]?.obj?.th,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.th || marketname.value?.en // 取出 玩法名称 中英文
    },
    bm: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.bm : `${cancel_data[cancel_num].bm}`,
      content: allocation[str_key]?.obj?.bm,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bm || marketname.value?.en // 取出 玩法名称 中英文
    },
    bi: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.bi : `${cancel_data[cancel_num].bi}`,
      content: allocation[str_key]?.obj?.bi,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bi || marketname.value?.en // 取出 玩法名称 中英文
    },
    hin: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.hin : `${cancel_data[cancel_num].hin}`,
      content: allocation[str_key]?.obj?.bi,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bi || marketname.value?.en // 取出 玩法名称 中英文
    },
    ru: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.ru : `${cancel_data[cancel_num].ru}`,
      content: allocation[str_key]?.obj?.bi,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bi || marketname.value?.en // 取出 玩法名称 中英文
    },
    ara: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[row.value.customRemarkIndex]?.ara : `${cancel_data[cancel_num].ara}`,
      content: allocation[str_key]?.obj?.bi,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bi || marketname.value?.en // 取出 玩法名称 中英文
    },
  }
  // 备份多语言数据
  list_data_copy.value = JSON.parse(JSON.stringify(list_data.value))
}