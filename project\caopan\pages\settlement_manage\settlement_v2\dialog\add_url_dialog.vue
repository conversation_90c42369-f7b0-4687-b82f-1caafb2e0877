<!--
 * @FilePath: /project/caopan/pages/settlement_manage/settlement_v2/dialog/website_link_dialog.vue
 * @Description: 结算2.0 竞品链接弹窗
-->

<template>
  <i-Modal
    :model-value="modelValue"
    :title="i18n_t('settlement_v2.v_201')"
    @update:modelValue="(val) => $emit('update:modelValue', val)"
    draggable
    sticky
    scrollable
    :mask-closable="false"
    @on-ok="add_com_web_url"
    @on-visible-change="dialog_show"
    width="400"
  >
    <div style="padding: 20px">
      <i-form ref="formInline" :model="formInline">
        <i-formItem prop="fullName">
          <i-input
            type="text"
            v-model:value.trim="formInline.fullName"
            :placeholder="i18n_t('settlement_v2.v_198')"
          />
        </i-formItem>
        <i-formItem prop="url">
          <i-input
            type="text"
            v-model:value="formInline.url"
            :placeholder="i18n_t('settlement_v2.v_199')"
          >
            <template #prepend>
              <iSelect
                v-model:value.trim="formInline.select"
                style="width: 80px"
              >
                <iOption value="http://">http://</iOption>
                <iOption value="https://">https://</iOption>
              </iSelect>
            </template>
          </i-input>
        </i-formItem>
      </i-form>
    </div>
  </i-Modal>
</template>

<script setup>
import { Message, lodash, i18n_t } from "src/output/common/project-common.js";
import { reactive } from "vue";
import { api_settlement_center } from "src/api/index.js";

const props = defineProps({
  modelValue: Boolean,
});

const emit = defineEmits(["update:modelValue", "success"]);

const formInline = reactive({
  select: "https://",
  url: null,
  fullName: null,
});

// 新增竞品链接
const add_com_web_url = async () => {
  const par = {
    fullName: formInline.fullName,
    url: formInline.select + formInline.url,
  };

  try {
    const res = await api_settlement_center.addComWebUrl(par);
    const { code } = lodash.get(res, "data");

    if (code === 200) {
      Message.success(res?.data?.msg);
      emit("success");
      dialog_close();
    } else {
      Message.error(res?.data?.msg);
    }
  } catch (e) {
    Message.error(e?.message || e);
  }
};

// 弹窗出现触发
const dialog_show = (val) => {
  if (!val) {
    formInline.value && formInline.value.resetFields();
  }
};

const dialog_close = () => {
  emit("update:modelValue", false);
};
</script>

<style lang="scss" scoped>
.link {
  height: 15px;
  vertical-align: -3px;
  display: inline-block;
  width: 15px;
  cursor: pointer;
  background: url("/assets/settlement_v2_icon/link.svg") no-repeat
    100%/100%;
}
.icon {
  font-size: 18px;
}
.icon:hover {
  color: #00a997;
}
:deep(.ivu-input-icon) {
  cursor: pointer;
}
:deep(.ivu-input) {
  border: 1px solid var(--q-color-panda-them--404758);
  background-color: var(--q-color-panda-field-grey);
  color: var(--q-color-panda-text-light);
  font-weight: 400;
}
:deep(.ivu-modal-header) {
  border-bottom: none;
}
:deep(.ivu-modal-body) {
  padding: 0 !important;
}
:deep(.ivu-input-group-prepend, .ivu-input-group-append) {
  background-color: var(--q-color-panda-field-grey);
  border: 1px solid var(--q-color-panda-secondary2);
  color: var(--q-color-panda-text-light);
}
:deep(.ivu-modal-content > .ivu-modal-close :hover) {
  background-color: transparent;
  color: #00a997;
}
:deep(.ivu-select) {
  border: none;
  color: var(--q-color-panda-text-light);
}
:deep(.ivu-modal .ivu-modal-footer) {
  border-top: 1px solid var(--q-color-panda-secondary2) !important;
}
</style>
