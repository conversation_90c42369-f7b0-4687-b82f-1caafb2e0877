import { get_sportType_array } from "src/output/common/store-common.js";
import { set_footer_breadcrumbs_part_all } from "src/output/common/project-common.js";
import Sortable from "sortablejs"; //拖拽
import { set_table_sort } from "src/components/league_ranking/api-request/set_table_sort.js";
import { get_data_list } from "src/components/league_ranking/api-request/get_data_list.js";
export const drag = (payload) => {
  const { table_hover, data_list } = payload;
  console.log(data_list, '+sdj')
  get_data_list(payload);
  // 首先获取需要拖拽的dom节点
  const el1 = document
    .querySelectorAll(".ivu-table .ivu-table-body")[0]
    .querySelectorAll("table > tbody")[0];
  Sortable.create(el1, {
    disabled: false, // 是否开启拖拽
    ghostClass: "sortable-ghost", //当拖拽的时候会出现背景颜色
    handle: ".drag-srot", //该列表元素都可以拖动的区域
    group: {
      // 是否开启跨表拖拽
      pull: false,
      put: false,
    },
    onEnd: (e) => {
      // 这里主要进行数据的处理，拖拽实际并不会改变绑定数据的顺序，这里需要自己做数据的顺序更改
      table_hover.value = false;
      let row = data_list.value[e.oldIndex];
      let params = get_sort_params(payload, row, null, 3);
      console.log(params, "params");
      params.id = row.id;
      params.operation = 3;
      params.sort = data_list.value[e.newIndex].sort;
      set_table_sort(payload, params, () => {
        get_data_list(payload);
      });
    },
  });
};
/**
 * @description: 联赛参数设置/足球
 */
export const set_footer_fn = (payload) => {
  const { select_sportId } = payload;
  const { sport_type_constant } = get_sportType_array();
  if (sport_type_constant) {
    let remark = sport_type_constant.find((n) => {
      return n.id == (select_sportId.value < 0 ? 1 : select_sportId.value);
    });
    set_footer_breadcrumbs_part_all(
      [i18n_t("menus.lm_10.title")], //运营管理
      [i18n_t("menus.lm_10.children2")], //联赛排序
      [remark ? remark.introduction : ""],
      []
    );
  }
};
/**
 * @description: 获取排序操作的params
 * @param {*} tournamentLevel 联赛等级
 * @return {*} params
 */
export const get_sort_params = (payload, row, index, type) => {
  const { data_list, params } = payload;
  let param = {
    sportId: params.value.sportId,
    tournamentLevel: params.value.tournamentLevel,
  };
  if (type != 3 && type) {
    let old_row =
      type == 2 ? data_list.value[index + 1] : data_list.value[index - 1];
    //为2是下降就取出下一位，为1是上升就取出上一位
    param.sortList = [
      {
        id: row.id,
        sort: old_row.sort,
      },
      {
        id: old_row.id,
        sort: row.sort,
      },
    ];
    param.operation = type;
  }
  return param;
};
