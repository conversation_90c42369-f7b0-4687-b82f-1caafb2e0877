import { api_operate } from "src/api/index.js";
import { get_data_list } from "src/components/league_ranking/api-request/get_data_list.js";
import { Message, lodash, i18n_t } from "src/output/common/project-common.js";
//点击确定按钮
export const confirm_btn = (row) => {
  let params = {
    id: row.id,
    topShowTime: `${row.startTime}-${row.endTime}`,
  };
  if (params.topShowTime == "-") {
    params.topShowTime = null;
  }
  if (params.topShowTime == null || (row.startTime && row.endTime)) {
    api_operate
      .setTopShowTime(params)
      .then((res) => {
        let code = lodash.get(res, "data.code");
        let msg = lodash.get(res, "data.msg");
        if (code == 200 || code == "0000000") {
          row.isEdit = false;
          get_data_list();
        } else {
          row.startTime = "";
          row.endTime = "";
          Message.warning(msg);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  } else {
    Message.error(i18n_t("operations.league_ranking.t4")); //请选择时间
  }
};
