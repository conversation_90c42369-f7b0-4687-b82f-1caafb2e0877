import { watch } from 'vue';
import {
  init_query_form_date_arr,
  default_data,
  query_form_date_tab_click,
} from 'project/caopan/components/query/select_query_period3/module/index.js';
import { get_optional_events_num, get_serverTime } from 'src/output/common/store-common.js';
import { format_date_base_time2 } from 'src/output/common/project-common.js';

export const watcher_register = payload => {
  const { route } = payload;
  const { show_more_query, emit_show_more_query_change, current_h } = payload;

  const optional_events_num = get_optional_events_num();
  const serverTime = get_serverTime();
  return [
    watch(
      () => optional_events_num.value,
      () => {
        optional_events_num.value = sessionStorage.getItem('optional_events_num') || 0;
      },
    ),

    watch(
      () => show_more_query.value,
      () => {
        // emit("show_more_query_change", show_more_query.value);
        emit_show_more_query_change(show_more_query.value);
      },
    ),

    watch(
      () => current_h.value,
      () => {
        init_query_form_date_arr(payload);
      },
    ),

    watch(
      () => serverTime.value,
      val => {
        let [y, m, d, h, mm, s] = format_date_base_time2(val);
        if (h == 12 && mm == '00' && s > 0 && s < 31) {
          init_query_form_date_arr(payload);
        }
      },
    ),

    watch(
      () => route.name,
      val => {
        init_query_form_date_arr(payload);

        if (val == 'trader_manage_liveOddSupport') {
          query_form_date_tab_click(payload, 9);
        } else if (val == 'trader_manage_morning_o') {
          query_form_date_tab_click(payload, 0);
        } else {
          default_data(payload);
        }
      },
      {
        deep: true,
        immediate: true,
      },
    ),
  ];
};
