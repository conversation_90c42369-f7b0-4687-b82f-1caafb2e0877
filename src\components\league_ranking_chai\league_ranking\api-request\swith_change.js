import { api_operate } from "src/api/index.js";
import { Message, lodash } from "src/output/common/project-common.js";
/**
 * @description: 是否展示
 * @param {*} row 点击table
 * @param {*} index 点击列的下标
 * @param {*} stant 是否一件开售
 * @return {*} 
 */
export const swith_change = (payload, row, index, stant) => {
  const { data_list } = payload;
  let item = data_list.value[index];
  item.swith_loading = true;
  let params = { id: row.id };
  if (stant) {
    params.versionNewStatus = stant - 1;
  } else {
    params.versionNewStatus = item.versionNewStatus ? 1 : 0; //是否展示
  }
  api_operate
    .updateTournamentStatus(params)
    .then((res) => {
      let code = lodash.get(res, "data.code");
      let msg = lodash.get(res, "data.msg");
      if (code == 200 || code == "0000000") {
        // 成功后则把swith_loading去除
        item.swith_loading = false;
      } else {
        item.swith_loading = false;
        nextTick(() => {
          item.versionNewStatus = !item.versionNewStatus;
        });
        Message.error(`${msg}`);
      }
    })
    .catch((err) => {
      // 如果失败还是之前的值
      item.swith_loading = false;
      nextTick(() => {
        item.versionNewStatus = !item.versionNewStatus;
      });
      Message.error(`${err}`);
    });
};
