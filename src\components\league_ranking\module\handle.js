import {
  Message,
  lodash,
  show_msg,
  i18n_t,
  language_value,
  resource_permissions,
  btn_permissions,
  permissions_route,
  permissions_sale_live,
  sport_permission_has,
} from "src/output/common/project-common.js";
import { get_data_list } from "src/components/league_ranking/api-request/get_data_list.js";
import { set_table_sort } from "src/components/league_ranking/api-request/set_table_sort.js";
import { get_sort_params } from "src/components/league_ranking/module/index.js";
import { swith_change } from "src/components/league_ranking/api-request/swith_change.js";
import { clearSort } from "src/components/league_ranking/module/other.js";
// 优先级排序
export const priority_change = (status) => {
  console.log(status, "value+++++++++++");
};
//  修改排序值
export const modify_playrules_order = (payload) => {
  /**
   * 隐藏翻页器
   * 请求全部当前球类数据
   * 显示排序 可编辑框
   *
   */
  const { is_modifying_playrules_order } = payload;
  is_modifying_playrules_order.value = true;
  get_data_list(payload);
};
export const submit_modify_playrules_order = (payload) => {
  /**
   * 修改排序值 提交
   */
  const { is_modifying_playrules_order, data_list, params } = payload;
  let arr = lodash.sortBy(data_list.value, ["orderNo_10"]); //安orderNo_10字段进行排序

  arr.map((item, index) => {
    item.sort_index = index + 1 + (params.value.start - 1) * params.value.size;
  });
  const sortList =
    arr.map((x) => {
      return { id: x.id, sort: x.orderNo_10 };
    }) || [];
  let param = {
    sportId: params.value.sportId,
    tournamentLevel: params.value.tournamentLevel,
    operation: 4,
    sortList,
    start: params.value.start,
    size: params.value.size,
  };
  set_table_sort(payload, param, () => {
    is_modifying_playrules_order.value = false;
    get_data_list(payload);
  });
};
//   取消修改排序值
export const abort_modify_playrules_order = (payload) => {
  const { is_modifying_playrules_order } = payload;
  is_modifying_playrules_order.value = false; //修改状态恢复
  get_data_list(payload); //请求列表数据
};
/**
 * @description: 输入排序input
 * @param {*} row
 * @param {*} index
 * @return {*}
 */
export const set_sort_beg = (payload, row, index) => {
  const { data_list } = payload;
  const value = Number(row.orderNo_10);
  data_list.value[index].orderNo_10 = value;
};
// 跨页单条数据排序
export const cross_page_sort = (payload, row, index) => {
  const { data_list } = payload;
  data_list.value.forEach((item, itemIndex) => {
    if (index != itemIndex) {
      data_list.value[itemIndex].show_input = false;
    } else {
      data_list.value[itemIndex].show_input = true;
    }
  });
  // row.show_input = true
};
/**
 * @description: 跨页排序
 * @param {*} row 当前数据
 * @return {*}
 */
export const submit_set_table_sort = (payload, row, index) => {
  let params = get_sort_params(payload, row, null, 3);
  params.id = row.id;
  params.operation = 3;
  params.sort = parseInt(row.orderNo_10);
  set_table_sort(payload, params, () => {
    get_data_list(payload);
  });
};
/**
 * @description: 操盘三图标，上升、下降、置顶
 * @param {*}type 1是上升 2是下降 3是置顶
 * @return {*}
 */
export const table_sort = (payload, row, index, type) => {
  let params = get_sort_params(payload, row, index, type);
  if (type == 3) {
    //置顶
    params.id = row.id;
    params.operation = 0;
  }
  set_table_sort(payload, params, () => {
    get_data_list(payload);
  });
};
//点击取消按钮
export const cancel_btn = (row) => {
  row.isEdit = false;
  get_data_list(payload);
};
// 取消
export const Cancel_textanda = (payload) => {
  const { visible, sta_switch } = payload;
  visible.value = false;
  sta_switch.value = 0;
  console.log(visible.value, "取消");
};
/**
 * @description: 一件取消展示
 */
export const all_cancel = (payload) => {
  const { data_list } = payload;
  let arr = data_list.value;
  arr.forEach((element, index) => {
    swith_change(element, index, 1);
    element.versionNewStatus = false;
  });
};
/**
 * @description: 一件全部开售
 */
export const all_show = (payload) => {
  const { data_list } = payload;
  let arr = data_list.value;
  arr.forEach((element, index) => {
    swith_change(element, index, 2);
    element.versionNewStatus = true;
  });
};
/**
 * @description: 模糊查询删除，数据恢复
 */
export const tournament_name_clear = (payload) => {
  const { params } = payload;
  params.value.tournamentName = "";
  (params.value.start = 1), (params.value.id = null);
  get_data_list(payload);
};
/**
 * @description: 模糊查询-点击下拉数据
 * @param {obj} //item.id.name.tournamentLevel
 */
export const set_tournament_name = (payload, item) => {
  const { params } = payload;
  if (item) {
    let obj = JSON.parse(item);
    params.value.tournamentLevel = obj.tournamentLevel;
    params.value.tournamentName = obj.tournamentName || obj.tournamentEname;
    params.value.id = obj.id;
    params.value.start = 1;
    clearSort(payload, 1);
    nextTick(() => {
      get_data_list(payload);
    });
  }
};
/**
 * @description:
 * @param {Object,number}  点击当前数据，下标
 */
export const row_click = (payload, item, index) => {
  const { row_click_id } = payload;
  row_click_id.value = item.id;
};
/**
 * @description:  拖拽排序
 * @param {row} 数据
 */
export const click_back = (payload, row) => {
  const { row_click_id, table_hover } = payload;
  row_click_id.value = row.id;
  table_hover.value = true;
};
/**
 * @description:  联赛等级切换
 * @param {number} 等级
 */
export const get_level = (payload, n) => {
  const { params, checked_level, tournament_name } = payload;
  if (n != params.value.tournamentLevel) {
    checked_level.value = n;
    params.value.tournamentLevel = n;
    params.value.start = 1;
    tournament_name.value = "";
    params.value.tournamentName = "";
    params.value.id = null;
    get_data_list(payload);
  }
};
/**
 * @description:  点击row 单行变色
 * @param {Object,number}  点击当前数据，下标
 * @return {String} 联赛名称
 */
export const row_class_name = (payload, row, index) => {
  const { row_click_id } = payload;
  console.log(row_click_id, '+hang id', payload)
  if (row.id == row_click_id.value) {
    return "ivu-table-row-highlight";
  } else {
    return " ";
  }
};
