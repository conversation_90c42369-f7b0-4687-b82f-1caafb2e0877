<!-- 联赛名称组件 -->
<template>
  <div>
    <i-collapse v-model="collapseValue" class="border-bottom-light">
      <i-panel name="1">
        <span class="text-panda-text-light hover-color p1-sticky">
          
          {{
          title_name
        }}</span>
        <template #content>
          <div
             class="p1-df p1-fdc pl10x pr10x p1-oxa"
          >
            <q-markup-table class="bg-panda-base-dark panda-table panda-sticky-header-table" separator="cell"
          :class="is_season === true ? 'season-height' : ''"

            >
              <thead>
                <tr
                  class="border-bottom-light text-panda-text-light line-height-30px"
                >
                  <th
                    class="text-left bg-panda-table-title"
                    style="width: 40px"
                  >
                    <img
                      v-if="show_lock"
                      class="cp ml5x"
                      @click="change_all_language_lock"
                      :src="`/img/${isLock ? 'close' : 'open'}_lock.svg`"
                      alt=""
                    />
                    <span class="iconUp" v-if="clearName" @click="clear()">{{
                      i18n_t("saicheng.match_edit.edit_70")
                    }}</span
                    ><!-- 一键清除 -->
                  </th>
                  <!--语言id-->

                  <th
                    class="text-left bg-panda-table-title"
                    style="width: 100px"
                  >
                    {{ i18n_t("saicheng.match_create_league.create_title9") }}
                  </th>
                  <!--语言-->

                  <th
                    class="text-left bg-panda-table-title"
                    style="width: 100px"
                  >
                    {{ i18n_t("saicheng.match_create_league.create_title10") }}
                  </th>
                  <!-- 全称 -->
                  <th
                    class="text-left bg-panda-table-title"
                    style="width: 300px"
                  >
                    {{ i18n_t("saicheng.match_create_league.create_title11") }}
                  </th>
                  <!-- 简称 -->
                  <th
                    class="text-left bg-panda-table-title"
                    style="width: 300px"
                  >
                    {{ i18n_t("saicheng.match_create_league.create_title12") }}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-panda-base-dark text-panda-text-light" v-if = "model">
                <tr
                  class="bg-panda-base-dark text-panda-text-light line-height-30px panda-border-color border-bottom"
                  v-for="(item, index) in model"
                  :key="index"
                >
                  <td>
                    <p
                      v-if="
                        language_history_record.find(
                          (x) => (x.languageType == item.key||x.languageType == item.key_jc)
                        )
                      "
                    >
                      <i-tooltip placement="right">
                        <!-- 编辑历史 -->
                        <span
                          ><img
                            src="/statics-saicheng/svg/history.svg"
                            alt=""
                            style="position: relative; top: 3px"
                        /></span>
                        <template #content>
                          <match-edit-history
                            class="p-zi1 none-scrollbar"
                            :data_log="language_history_record"
                            :language_type="item.key"
                            :language_type_jc="item.key_jc"

                          ></match-edit-history>
                        </template>
                      </i-tooltip>
                    </p>
                  </td>
                  <td
                    class="text-left text-panda-text-dark"
                    style="font-size: 12px"
                  >
                    {{ item.key
                    }}<span
                      v-if="(item.key == 'zs' || item.key == 'en'|| item.key == 'zh'|| item.key == 'vi'|| item.key == 'ko'|| item.key == 'th') && show_must_incoming"
                      style="color: red"
                      >*</span
                    >
                  </td>
                  <td
                    class="text-left text-panda-text-dark"
                    style="font-size: 12px"
                  >
                    {{ item.name
                    }}<span
                      v-if="(item.key == 'zs' || item.key == 'en' || item.key == 'zh' || item.key == 'vi'|| item.key == 'ko'|| item.key == 'th') && show_must_incoming"
                      style="color: red"
                      >*</span
                    >
                  </td>

                  <!--多语言名称-->

                  <td class="text-left">
                    <div class="row">
                      <i-input
                        v-model.trim="item.title"
                        :disabled="isLock"
                        style="
                          width: 200px;
                          margin-top: 10px;
                          margin-bottom: 10px;
                        "
                        clearable
                        placeholder
                        maxlength="100"
                      >
                      </i-input>
                    </div>
                  </td>

                  <td class="text-left">
                    <div class="row">
                      <i-input
                      v-model.trim="item.title_jc"
                      :disabled="isLock"
                        style="
                          width: 200px;
                          margin-top: 10px;
                          margin-bottom: 10px;
                        "
                        clearable
                        placeholder
                        maxlength="100"
                      >
                      </i-input>
                    </div>
                  </td>
                </tr>
              </tbody>
            </q-markup-table>
          </div>
        </template>
      </i-panel>
    </i-collapse>
  </div>
</template>

<script setup>
import matchEditHistory from "project/saicheng/components/table/match_edit_history.vue"; // 编辑历史组件

import { project_saicheng_pages_match_resource_component_league_name_variable_componsable_fn } from "project/saicheng/pages/match/resource/component/league_name/componsable/variable.js";
import { constproject_saicheng_pages_match_resource_component_league_name_componsable_fn } from "project/saicheng/pages/match/resource/component/league_name/componsable/index.js";
import { lodash } from "src/output/common/project-common";
const emit = defineEmits(["change_language_lock"]);
const props = defineProps({
  editing_obj: {
    //  编辑对象
    default: {},
    type: Object,
  },
  mulit_language_arr: {
    default: [],
    type: Array,
  },
 

  panel_key: {
    //修改语言历史记录
    default: "",
    type: String,
  },

  show_lock: {
    // 是否展示多语言的锁  创建赛事中 不需要展示
    default: true,
    type: Boolean,
  },

  
  show_must_incoming: {
    // 
    default: true,
    type: Boolean,
  },
  is_lock: {
    // 锁的开关状态 即是否可以编辑多语言
    default: false,
    type: Boolean,
  },
  title_name: {
    //  编辑对象
     type: String,
     default: "",
  },

  operaType: {
    default: "League",
    type: String,
  },

    is_season: {
    default: false,
    type: Boolean,
  },
  
});
const model = defineModel()


const base_payload =
  project_saicheng_pages_match_resource_component_league_name_variable_componsable_fn();

const { language_order, tournamentName_jc, collapseValue, clearName, isLock,language_history_record } =
  base_payload;

const {
  get_arr_data,
  clear,
  change_all_language_lock,
  
} =
  constproject_saicheng_pages_match_resource_component_league_name_componsable_fn(
    {
      ...base_payload,
      props,
      emit,
    }
  );
</script>
<style scoped lang="scss">

.season-height{
max-height: 200px;
}
</style>
