
import { computed  } from "vue";

import { get_reset_query } from "src/output/common/store-common.js"

// import { } from "src/output/common/project-common.js"
// import { } from "src/output/common/componsable-common.js"

export const computed_generator = ()=>{

  const filter_data_sources_constant = computed(()=>{
    if (not_pa.value === 0) {
      //  商业数据
      return dataSource_obj.value.data_sources_constant_commerce;
    } else if (not_pa.value == 2) {
      // 非 PA
      return dataSource_obj.value.data_sources_constant_not_pa;
    } else if (not_pa.value == 1) {
      //  全部
      return dataSource_obj.value.raw_dataSource;
    } else if (not_pa.value == 3) {
      //  无 UM  UMA
      return dataSource_obj.value.data_sources_constant_not_um_uma;
    } else if (not_pa.value == 4) {
      return dataSource_obj.value.data_sources_constant_not_um_uma
        .slice()
        .sort((x, y) => {
          if (x.code === "PA") {
            x.order = 1;
          } else {
            x.order = 2;
          }
          if (y.code === "PA") {
            y.order = 1;
          } else {
            y.order = 2;
          }
          return x.order - y.order;
        })
        .map(x => {
          x = { ...x };
          x.code === "PA" && (x.shortName = x.fullName);
          return x;
        });
    } 
  }) 

  const reset_query = get_reset_query() 

  return {
    filter_data_sources_constant,
    reset_query
  }

}

// computed: {
//   export const filter_data_sources_constant = (payload) => {
//     if (not_pa.value === 0) {
//       //  商业数据
//       return dataSource_obj.value.data_sources_constant_commerce;
//     } else if (not_pa.value == 2) {
//       // 非 PA
//       return dataSource_obj.value.data_sources_constant_not_pa;
//     } else if (not_pa.value == 1) {
//       //  全部
//       return dataSource_obj.value.raw_dataSource;
//     } else if (not_pa.value == 3) {
//       //  无 UM  UMA
//       return dataSource_obj.value.data_sources_constant_not_um_uma;
//     } else if (not_pa.value == 4) {
//       return dataSource_obj.value.data_sources_constant_not_um_uma
//         .slice()
//         .sort((x, y) => {
//           if (x.code === "PA") {
//             x.order = 1;
//           } else {
//             x.order = 2;
//           }
//           if (y.code === "PA") {
//             y.order = 1;
//           } else {
//             y.order = 2;
//           }
//           return x.order - y.order;
//         })
//         .map(x => {
//           x = { ...x };
//           x.code === "PA" && (x.shortName = x.fullName);
//           return x;
//         });
//     }
//   },
//   ...mapGetters({
//     reset_query: "get_reset_query"
//   })
// },