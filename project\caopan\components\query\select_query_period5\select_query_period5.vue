<!--
 * @FilePath: /project/caopan/components/query/select_query_period3.vue
 * @Description:
-->
<template>
  <div
    :class="`row bg-panda-base-title-light line-height-32px items-center full-width p-pl--sm ${
      has_border ? 'border-bottom-light2' : ''
    }`"
    style="height: 34px"
  >
    <div v-if="left_btn">
      <span class="dib p1-df p1-aic p1-jcc pr10x cp">
        <span
          class="panda-left-info-toogle-button"
          :class="show_more_query ? 'active' : ''"
          @click="show_more_query = !show_more_query"
        >
          <q-tooltip>
            <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
            <!--           '隐藏左侧信息':'展示左侧信息'-->
            {{
              show_more_query ? i18n_t("common.hideLeft") : i18n_t("common.t5")
            }}
          </q-tooltip>
          <span class="panda-left-info-toogle-button-inner"></span>
        </span>
      </span>
    </div>

    <div
      @click="query_form_date_tab_click(22)"
      :class="selected_date_tab == 22 ? 'bg-panda-query-date-active' : ''"
      class="float-left text-panda-text-dark text-center cursor-pointer"
    >
      <span
        class="panda-py-4px panda-px-10px border-radius-2px panda-query-date-tab-label mr-10 panda-border-grey"
        :class="
          selected_date_tab == 22
            ? 'text-panda-date-base'
            : 'text-panda-date-light'
        "
        >{{ i18n_t("common.earlyMarket") }}</span
      >
    </div>
    <div
      @click="query_form_date_tab_click(21)"
      :class="selected_date_tab == 21 ? 'bg-panda-query-date-active' : ''"
      class="float-left text-panda-text-dark text-center cursor-pointer"
    >
      <span
        class="panda-py-4px panda-px-10px border-radius-2px panda-query-date-tab-label mr-10 panda-border-grey"
        :class="
          selected_date_tab == 21
            ? 'text-panda-date-base'
            : 'text-panda-date-light'
        "
        >{{ i18n_t("common.inPlay") }}</span
      >
    </div>

    <div
      v-if="show_history"
      :class="selected_date_tab == 8 ? 'bg-panda-query-date-active' : ''"
      class="float-left text-panda-text-dark text-center cursor-pointer panda-query-date-tab relative-position"
    >
      <template v-if="false">
        <span
          @click="query_form_date_tab_click(8)"
          class="panda-py-2px panda-px-10px border-radius-2px panda-query-date-tab-label"
          :class="
            selected_date_tab == 8
              ? 'text-panda-date-light'
              : 'text-panda-date-base'
          "
          >{{ i18n_t("selectPeriodTab.Tab_11") }}</span
        >
        <div
          v-show="selected_date_tab == 8 && show_q_date_component"
          class="absolute bg-panda-base-dark border-radius-4px panda-border"
          style="
            top: 32px;
            height: 140px;
            z-index: 11;
            box-shadow: 0 2px 14px 0 rgba(0, 0, 0, 0.5);
            border-radius: 4px;
          "
        >
          <q-date
            v-model="history_date"
            @input="handle_history_date_confirm"
            mask="YYYY-MM-DD HH:mm:ss"
            color="panda-primary"
            text-color="panda-text-light"
            :options="startTime_limit"
            dark
          />
        </div>
      </template>
      <template v-else>
        <iDatePicker
          type="date"
          v-model="history_date"
          @on-change="handle_history_date_confirm"
          :options="date_option"
          format="yyyy-MM-dd"
          placement="bottom-end"
          :placeholder="i18n_t('selectPeriodTab.Tab_11')"
          style="width: 120px"
          :class="selected_date_tab == 8 ? 'borderBlue' : ''"
        ></iDatePicker>
      </template>
    </div>

    <div class="operate">
      <img
        @click="$emit('init_tabledata')"
        @mouseenter="hover_refresh = true"
        @mouseleave="hover_refresh = false"
        :class="[
          {
            refresh_menu_active: tabledata_loading,
            gray: !tabledata_loading && !hover_refresh,
          },
        ]"
        src="/assets/icon/refresh.svg"
        alt="refresh"
      />
      <div
        @click="toOperate"
        v-if="routeName == 'match_sale' || routeName == 'match_live'"
      >
        {{
          routeName == "match_sale"
            ? i18n_t("selectPeriodTab.Tab_13")
            : i18n_t("selectPeriodTab.Tab_14")
        }}
      </div>
      <!--早盘操盘 滚球操盘-->
    </div>
  </div>
</template>
<script setup>
// import { defineEmits } from "vue";
defineProps({
  right_bnt: "",
  default_history_data: [Number, String, Object],
  prop_history_date: "",
  default_select: {
    type: Number,
    default: 0,
  },
  has_border: {
    type: Boolean,
    default: true,
  },
  more_query: {
    type: Boolean,
    default: false,
  },
  icon_visible: {
    type: Boolean,
    default: false,
  },
  icon_count: {
    type: Number,
    default: 0,
  },
  show_show_more_query_btn: true,
  show_live_odd: {
    type: Boolean,
    default: false,
  },
  show_history: {
    type: Boolean,
    default: true,
  }, // 显示历史赛程
  show_yesterday: {
    type: Boolean,
    default: false,
  },
  left_btn: {
    type: Boolean,
    default: true,
  },
  total: "",
  query_times: {
    type: Number,
    default: 0,
  },
  tabledata_loading: {
    type: Boolean,
    default: true,
  },
  is_export: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["init_tabledata","icon_click","query_period_change","show_more_query_change"]);
///引入 variable—composable-fn
import { project_caopan_components_query_select_query_period5_select_query_period5_variable_composable_fn } from "project/caopan/components/query/select_query_period5/componsable/variable.js";

//最基础的payload
const base_payload =
  project_caopan_components_query_select_query_period5_select_query_period5_variable_composable_fn();

///解构出 参数，符合 template 所需要的  常规变量
const { startTime_limit, date_option,show_more_query } = base_payload;

import { project_caopan_components_query_select_query_period5_select_query_period5_composable_fn } from "project/caopan/components/query/select_query_period5/componsable/index.js";

//解构常规方法y与emit
const {
  ///输出 template中@事件需要使用到的方法
  handle_history_date_confirm,
  toOperate,
  query_form_date_tab_click
} =
  project_caopan_components_query_select_query_period5_select_query_period5_composable_fn(
    {
      ...base_payload,
      props,
    }
  );

</script>

<style lang="scss" scoped>
@import url("project/caopan/components/query/select_query_period5/css/index-scoped.scss");
</style>
