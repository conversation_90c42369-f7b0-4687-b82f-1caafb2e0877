

:deep( .ivu-modal-header ){
  height: 32px;
  background: $panda-base-light;
  background: var(--q-color-panda-base-light);
  padding: 0 0;
  padding-left: 16px;
  border-bottom: none;
  line-height: 32px;
  color: var(--q-color-panda-them--788299-placeholder) !important;
  .ivu-modal-header-inner {
    line-height: 32px;
    height: 32px;
    font-size: 12px;
    color: var(--q-color-panda-them--788299-placeholder) !important;
  }
}
:deep( .ivu-modal-body ){
  padding: 0 0;
  padding-bottom: 42px;
}
:deep( .ivu-modal-close ){
  top: 0px;
}
.open-imodal {
  :deep( .ivu-modal ){
    top: 0;
    width: 100vw !important;
    height: 100vh !important;
  }
}
.bottom-page {
  background: $panda-base-dark !important;
  background: var(--q-color-panda-base-dark) !important;
}
:deep( .ivu-modal-body ){
  position: relative;
}
:deep( .log-item ){
  height: 24px;
  margin: 0 -10px;
  padding: 0 8px;
  border-bottom: 1px solid var(--q-color-panda-table-border);
  &:last-child {
    border-bottom: 0px solid var(--q-color-panda-table-border);
  }
}
.padding-section {
  padding: 5px 10px;
}
.font-14 {
  font-size: 14px;
}
.fold-content {
  max-height: 62px;
}
.btn {
  display: inline-block;
  padding: 0 10px;
  height: 25px;
  line-height: 25px;
  border: 1px solid #00a997;
  cursor: pointer;
  color: #00a997;
  border-radius: 2px;
  margin-right: 5px;
}
