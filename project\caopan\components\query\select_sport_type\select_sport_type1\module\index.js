import { 
  set_match_data, 
  set_sport_type_visible, 
  set_sport_name, 
  set_sportType_array, 
  get_window_size_info,
 } from 'src/output/common/store-common.js';
import {
  i18n_t,
  lodash,
  Message,
  sport_type_mapping_constant,
  sport_permission_has,
  score_center_mapping_constant,
  olympic_type_mapping_constant,
  champion_type_mapping_constant,
} from 'src/output/common/project-common.js';
import { get_champion_activem_atchNum } from 'src/output/common/store-common.js';

export const compute_show_num = payload => {
  console.log('payloadpayload', payload);
  const { showNum, route } = payload;
  showNum.value = route.name.indexOf('match_') > -1 ? true : false;
};
// 是否展示冠军红点
export const compute_show_champion_plays = payload => {
  const { show_champion_badge, route } = payload;
  show_champion_badge.value = route.name.indexOf('champion_plays') > -1 ? true : false;
};
// 根据路由展示头部球类 将头部球类存入vuex
export const mutation_sport_type_constant = payload => {
  const { route, only_show_e_sports, only_show_virtual, rebuild_sport_type_constant } = payload;
  let set = lodash.cloneDeep(sport_type_mapping_constant);
  // 当前只有事件审核不需要展示奥运赛种
  if (route.name == 'event_review') {
    set = set.filter(x => x.id < 17 || x.id == 37);
  }
  // 需要展示娱乐的路由：  玩法管理  冠军玩法  联赛排序  赛事排序
  let show_politics_sport_route = ['event_sequencing'];
  let politics = {
    introduction: i18n_t('traderTable.Z_18'), // 娱乐
    enname: 'zzyl',
    icon: 'panda_zzyl',
    img: '/image/sport/panda_img_sport_zzyl.png',
    id: 18,
    nameCode: 18,
    numb: 0,
  };
  if (show_politics_sport_route.includes(route.name)) {
    set.push(politics);
  }
  // 玩法管理  冠军玩法  将娱乐放在倒数第二个
  if (['play_management', 'champion_plays'].includes(route.name)) {
    set.splice(set.length - 1, 0, politics);
  }
  // 电子竞技玩法管理 电子竞技玩法集管理
  if (only_show_e_sports.value.includes(route.name)) {
    set = e_type_mapping_constant;
  }
  show_all_route(set, route)
  //  只显示 虚拟体育 的  路由     ----------------------------------------------------
  // csid：1001-足球 1002-赛狗 1007-泥地赛车 1008-卡丁车 1009-泥地摩托车 1010-摩托车 1011-赛马 1012-虚拟马车赛
  if (only_show_virtual.value.includes(route.name)) {
    set = [...virtual_type_mapping_constant];
    if (route.name == 'virtual_play_management') {
      // 虚拟体育玩法管理  也要展示 全部
      set.unshift({
        introduction: i18n_t('playManagement.pm_all'), // 全部
        id: -3,
        icon: 'panda_icon_sport_qb',
        enname: 'panda_icon_sport_qb',
      });
    }
  }
  set_sportType_array(set); // 将头部球类存入vuex
  rebuild_sport_type_constant(payload); // 计算赛事数量
};
  // 需要展示 全部 的路由
const show_all_route = (set, route) => {
  // 展示 "全部" 奥运赛种的路由： 冠军玩法 玩法管理 玩法集管理
  let show_all_olympic_sport_route = ['champion_plays', 'play_management', 'play_collection_manage_main'];
  if (show_all_olympic_sport_route.includes(route.name)) {
    set = set.concat(olympic_type_mapping_constant);
  }
   // 联赛路由
   let show_champion_sport_route = ['league_parameter_set', 'league_ranking'];
   if (show_champion_sport_route.includes(route.name)) {
     set = set.concat(champion_type_mapping_constant);
   }
  // 需要展示 全部 的路由： 电子竞技玩法管理
  if (route.name == 'e_play_management') {
    let e_type_mapping_constant_ = [
      {
        introduction: i18n_t('playManagement.pm_all'), //全部
        id: -3,
        icon: 'panda_icon_sport_qb',
        enname: 'panda_icon_sport_qb',
      },
      ...e_type_mapping_constant,
    ];
    set = e_type_mapping_constant_;
  }
  // 需要展示 全部 的路由：  玩法管理  虚拟体育玩法管理
  if (route.name == 'play_management') {
    set.unshift({
      introduction: i18n_t('playManagement.pm_all'), //全部
      id: -3,
      icon: 'panda_icon_sport_qb',
      enname: 'panda_icon_sport_qb',
    });
  }
  // 比分中心暂时只支持部分玩法
  if (route.name == 'score_center') {
    set = score_center_mapping_constant;
  }
}
export const compute_base_per_sportType_style = payload => {
  const { left_side_style, right_side_style, per_sportType_style, more_sportType_btn_style } = payload;
  let ww = '';
  if (get_window_size_info().value) {
    ww = get_window_size_info().value.width - 60 - 1;
  } else {
    ww = window.innerWidth - 60 - 1;
  }
  let per_w = parseInt((ww - 110) / 8);
  left_side_style.value = {
    width: per_w * 8 + 'px',
  };
  right_side_style.value = {
    width: ww - per_w * 8 + 'px',
  };
  per_sportType_style.value = {
    width: per_w + 'px',
    height: 34 + 'px',
  };
  more_sportType_btn_style.value = {
    width: ww - per_w * 8 + 'px',
  };
};
export const findValidSport = payload => {
  const { route } = payload;

  for (let i = 1; i < 17; i++) {
    if (sport_permission_has(i)) {
      return i;
    }
  }
  if (route.name == 'play_management') {
    return -3;
  } else {
    return 0;
  }
};

export const sport_type_click = (payload, item, index) => {
  const { route, sport_type_constant, show_fixed_more_sport_type, set_router_cache_by_path_and_value } = payload;
  // id 为 -3 代表全部，只要当前页面有一个球种权限，即可以显示全部页签
  if (!route.path.includes('settlement_manage') && item.id != -3 && !sport_permission_has(item.id)) {
    Message.error(i18n_t('common.permissions'))
    return;
  }
  if (route.name == 'event_sequencing' && [1, 2].indexOf(item.id) == -1) {
    return;
  }
  set_sport_name(item); //存头部信息
  let len = sport_type_constant.value.length;
  if (item.enname) {
    set_router_cache_by_path_and_value('sportId', item.id);
    sessionStorage.setItem('sportId', item.id);
    set_match_data({
      sportId: item.id,
      sportImg: item.img,
    });
    set_match_data({
      sportId: 0,
      code: 'matchPeriod',
      sportImg: item.img,
    });
    show_fixed_more_sport_type.value = false;
  }
  set_sport_type_visible(false);
};
/*
 * 处理运动种类显示
 */
export const handle_mouseenter = () => {
  set_sport_type_visible(true);
};

export const forceUpdate = payload => {
  payload.componentKey.value = Date.now();
};
// 是否展示徽标
export const cpomputed_show_badge = (payload, id) => {
  const {show_champion_badge} = payload
  let num = lodash.get(get_champion_activem_atchNum().value, `typeDetails[${id}]`)
  let bl = show_champion_badge.value && num
   return bl
}
