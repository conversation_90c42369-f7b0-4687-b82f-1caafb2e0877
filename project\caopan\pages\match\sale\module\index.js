
// ...mapActions([
//   "set_optional_events_num",
//   "set_sportRegion_array_force",
//   'set_dataSource_obj',// 设置数据源
// ]),
import { rebuild_tablecolumns_config } from 'project/caopan/componsable/style/table_col_width/module/index.js';
//teamType 1 主队（队伍1） 2 客队（队伍2）
import { i18n_t,Message } from "src/output/common/project-common.js";
import { nextTick } from "vue";
import { change_weight } from "project/caopan/pages/match/sale/api-request/change_weight.js";
import { init_tabledata } from "project/caopan/pages/match/sale/api-request/init_tabledata.js";
import { data_origin_assign } from 'project/caopan/pages/match/common/data_origin_config.js';
import { computed_weight_value,} from 'project/caopan/pages/match/common/sale/module/index.js';
// import { get_dataSource_obj, serverTime:get_serverTime, } from "src/output/common/store-common.js";
import { init_right_detail_status,set_footer_breadcrumbs_part_all } from "src/output/common/project-common";
import {tablecolumns_config,state_list,list_style,match_sell_status_arr } from "project/caopan/pages/match/sale/config/config.js"

import {query_tournament_info } from "project/caopan/pages/match/common/sale/api-request/index.js";
  
export const remove_team = (payload, teamType) => { //删除选中的待构建范特西赛事的球队
    const { build_match_data} = payload
    if (teamType === 1) {//主队（队伍1）
        build_match_data.value.newHomeMatchId = null
        build_match_data.value.newHomeTeamId = null
        build_match_data.value.newHomeBeginTime = null
        build_match_data.value.newHomeTeamNames = null

    } else {      //客队（队伍2）
        build_match_data.value.newAwayMatchId = null
        build_match_data.value.newAwayTeamId = null
        build_match_data.value.newAwayBeginTime = null
        build_match_data.value.newAwayTeamNames = null
    }
}
//选择需要参与构建范特西赛事的球队
//dataTeamType 源数据的球队类型  1 主队  2 客队
//buildTeamType 需要构建成哪种球队类型  1 主队  2 客队
export const select_team = (payload, dataTeamType, buildTeamType) => {
    const { build_match_data,build_select_row} = payload
    if ((dataTeamType == 1 && buildTeamType == 1 && build_match_data.value.newAwayMatchId === build_select_row.value.matchInfoId)
        || (dataTeamType == 1 && buildTeamType == 2 && build_match_data.value.newHomeMatchId === build_select_row.value.matchInfoId)
        || (dataTeamType == 2 && buildTeamType == 1 && build_match_data.value.newAwayMatchId === build_select_row.value.matchInfoId)
        || (dataTeamType == 2 && buildTeamType == 2 && build_match_data.value.newHomeMatchId === build_select_row.value.matchInfoId)
    ) {
        Message.warning({
            content: i18n_t("saleTable.sa_2550_5"), // 球队1或球队2不能来自同一场赛事
            duration: 5
        });
        return;
    }
    let teamId = dataTeamType === 1 ? build_select_row.value.teamHomeId : build_select_row.value.teamAwayId
    let teamNames = dataTeamType === 1 ? build_select_row.value.homeTeamNames : build_select_row.value.awayTeamNames
    let beginTime = build_select_row.value.beginTime;
    let matchId = build_select_row.value.matchInfoId;
    if (buildTeamType === 1) {//构建为主队（队伍1）
        build_match_data.value.newHomeMatchId = matchId
        build_match_data.value.newHomeTeamId = teamId
        build_match_data.value.newHomeBeginTime = beginTime
        build_match_data.value.newHomeTeamNames = teamNames
    } else {      //构建为客队（队伍2）
        build_match_data.value.newAwayMatchId = matchId
        build_match_data.value.newAwayTeamId = teamId
        build_match_data.value.newAwayBeginTime = beginTime
        build_match_data.value.newAwayTeamNames = teamNames
    }
}
//构建范特西赛事表单
export const build_match_click = (payload, row) => {
    const { build_match_model,build_select_row} = payload
    build_select_row.value = row;
    build_match_model.value = true;
}

// 根据球类id获取表头
export const get_tablecolumns = (payload) => {
    const { select_sportId,tablecolumns} = payload
    if (select_sportId.value == 1) { // 足球多了角球 罚牌
        tablecolumns.value = rebuild_tablecolumns_config(tablecolumns_config);
    } else {
        tablecolumns.value = rebuild_tablecolumns_config(tablecolumns_config.filter(x => x.name != "cornerShow" && x.name != "cardShow"));
    }
}
// 设置数据源权重
export const change_source_weight = (payload, item) => {
    const { source_weight_obj,show_dialog} = payload
    Object.assign(source_weight_obj.value, data_origin_assign(item))
    source_weight_obj.value.old = computed_weight_value(source_weight_obj.value)
    source_weight_obj.value.matchInfoId = item.matchId;
    source_weight_obj.value.supportLiveList = item.supportLiveList;//支持滚球
    source_weight_obj.value.sportId = item.sportId;
    source_weight_obj.value.matchSellStatus = item.preMatchSellStatus;
    source_weight_obj.value.businessEventList = item.businessEventList;
    show_dialog.value = true;
}
//数据源权重弹窗确认按钮
export const set_source_weight = (payload) => {
    const { source_weight_obj} = payload
    let params = data_origin_includes(source_weight_obj.value);;
    let arr = Object.values(params);
    let len0 = arr.filter(item => item == 0).length;
    let len1 = arr.filter(item => item == 1).length;
    if (len0 !== arr.length - 1 || len1 !== 1) {
        Message.warning({
            content: i18n_t("saleTable.sa_51"), // 数据源权重值只有一个为1，其它为0
            duration: 5
        });
        return false;
    }
    let content;
    source_weight_obj.value.new = computed_weight_value(params)

    if (source_weight_obj.value.old == source_weight_obj.value.new) {//如果未修改 提示 数据源权重没有变化，请重新设置或关闭窗口！
        Message.warning({
            content: i18n_t("saleTable.sa_100"), //  数据源权重没有变化，请重新设置或关闭窗口！
            duration: 5
        });
        return false;
    }
    content = `<p style="color: var(--q-color-panda-text-light)">${i18n_t("saleTable.sa_115")}【${source_weight_obj.value.new}】？</p>`; // 请确认是否切换早盘主数据源为
    if (source_weight_obj.value.new == source_weight_obj.value.old) {
        change_weight()
    } else {
        Modal.confirm({
            content: content,
            onOk: () => {
                change_weight()
            },
        });
    }
}

// MTS 切换 PA
export const change_preRiskManagerCode = (payload, item) => {
    const { change_mts_pa,change_mts_pa_obj} = payload
    change_mts_pa.value = true
    change_mts_pa_obj.value = item
}
// MTS 切换 PA  确认



export const init_data = ( arr) => {
    arr &&
        arr.forEach((item, index) => {
            let standardMarketOddsBOList = [];
            arr[index].standardMarketOddsBOList &&
                arr[index].standardMarketOddsBOList.map(item2 => {
                    (item2.oddsType == 1 || item2.oddsType == "Over") &&
                        (standardMarketOddsBOList[0] = item2);
                    (item2.oddsType == 2 || item2.oddsType == "Under") &&
                        (standardMarketOddsBOList[1] = item2);
                    item2.oddsType == "X" && (standardMarketOddsBOList[2] = item2);
                });
            arr[index].standardMarketOddsBOList = standardMarketOddsBOList;
        });
    return arr;
}
// 保存成功
export const submit_success = (payload, val='') => {
    init_tabledata(payload,val);
}
//关闭右侧抽屉
export const close_right_info = (payload) => {
    const {show_edit_match_sale} = payload
    show_edit_match_sale.value = false;
    init_right_detail_status(payload,2);
}
//设置赛事状态源
export const edit_match_sale = (payload, val, item) => {
    // return;
    const {show_right_detail_obj,show_edit_match_sale} =payload;
    console.log('item-edit_match_sale',item,show_right_detail_obj.value);
    // debugger;
    show_right_detail_obj.value = item
    show_edit_match_sale.value = true;
    init_right_detail_status(payload,1);
}
// 设置footer
export const set_footer_fn = (payload, label) => {
    const { sport_type_constant,label_cache,select_sportId} = payload
    if (sport_type_constant.value) {
        let remark = sport_type_constant.value.find(n => {
            return n.id == select_sportId.value;
        });
        if (!remark) {
            remark = {};
            remark.introduction = i18n_t('traderTable.Z_1')
        }
        set_footer_breadcrumbs_part_all(
            [i18n_t("menus.lm_5.children1")],
            [remark.introduction],
            [],
            [label_cache.value || label]
        );
    }
}
//排序值输入框是否显示
export const edit_sort_status = (payload, obj) => {
    const {
        sort_status,
        visibleColumns
    } = payload
    sort_status.value = obj.status;
    if (sort_status.value) {
        visibleColumns.value.splice(2, 0, 'sortValue')
    } else {
        visibleColumns.value = visibleColumns.value.filter((item) => { return item != 'sortValue' })
    }
}


//初始化请求参数
export const compute_init_tabledata_params = (payload, i) => {
    const {
        queryform_form,
        select_sportId,
        isEarlyTrading,
        matchStatus,
        isFavorite,
        query_times,
        route,
        query_module,
        play_data,
        // query_tournament_info
    } = payload
    let params = {
        page: queryform_form.value.currentPage,
        size: queryform_form.value.pageSize,
        sportId: select_sportId.value,
        isErlyTrading: isEarlyTrading.value, //是否其它早盘
        matchStatus: matchStatus.value, //是否历史赛程
        marketType: "PRE", //PRE 早盘 LIVE 滚求
        isFavorite: isFavorite.value //是否查询收藏赛事 0 不是 1 是
        // isFavorite: 1 //是否查询收藏赛事 0 不是 1 是
    };
    if (i) {
        params.matchInfoId = i;
    }
    delete query_module.value.tournamentLevel
    params = { ...query_module.value, ...params };
    if (query_times.value < 1 &&
        sessionStorage.getItem('remember') &&
        JSON.parse(sessionStorage.getItem('remember'))[select_sportId.value + '_' + route.name] &&
        sessionStorage.getItem(select_sportId.value + '_' + route.name)) {
        params = { ...params, ...JSON.parse(sessionStorage.getItem(select_sportId.value + '_' + route.name)) };
        params.sportId = select_sportId.value;
        queryform_form.value.currentPage = Number(params.page);
        queryform_form.value.pageSize = Number(params.size);
    }

    if (route.query.startTimeFrom && !query_times.value) {
        let { page, size, isFavorite, startTimeFrom, live } = route.query
        queryform_form.value.currentPage = page
        queryform_form.value.pageSize = size
        params.isFavorite = isFavorite
        params.startTimeFrom = startTimeFrom

        params.live = live
        params.page = page
        params.size = size
    }
    // params.startTimeFrom = 1746158400000
    // params.endTimeFrom = 1746244800000
    play_data.value = params;
    // let testparams={"matchSellStatus":"","regionId":"","preTraderDepartmentId":"","userId":"","matchManageId":"","tournamentLevelList":[],"teamName":"","isSpecialPersons":0,"orgIdOrPersons":"","tournamentIdList":[],"startTimeFrom":1746158400000,"endTimeFrom":1746244800000,"live":0,"page":1,"size":50,"sportId":1,"isErlyTrading":0,"matchStatus":"Enable","marketType":"PRE","isFavorite":1}
    // query_tournament_info(testparams);
    query_tournament_info(payload, params);
    return params;
}
// 计算滚动条
export const compute_scrollarea_style = (payload) => {
    const { window_size_info,scroll_area_height} = payload
    nextTick(() => {
        let hs = window_size_info.value.height;
        let dft = 41 + 34 + 43; //头部+table表头+翻页器
        scroll_area_height.value = hs - dft;
    });
}


