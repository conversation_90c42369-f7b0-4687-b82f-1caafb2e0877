import { ref } from 'vue';

export const create_base_state_instance_fn = () => {
  //是否正在修改排序值
  const is_edit_sort = ref(false);
  const show_q_date_component = ref(false);
  const selected_date_tab = ref(0);
  const show_more_query = ref(false);
  const queryform_form = ref({});
  // 针对时间选择器 ，真正
  const startTimeFrom = ref('');
  const query_form_date_arr = ref([]);
  const history_date = ref('');
  const other_date = ref('');
  const timer = ref('');
  const current_h = ref('');
  const first_day_startTimeFrom = ref('');
  const date_option = ref({});
  const date_option2 = ref({});
  const optional_events_num = ref(0);
  const routeName = ref('');
  const hover_refresh = ref(false);
  const current_promise = ref(null);
  const current_ = new Promise(res => {
    current_promise.value = res;
  });

  return {
    is_edit_sort, //是否正在修改排序值
    show_q_date_component,
    selected_date_tab,
    show_more_query,
    queryform_form,
    startTimeFrom, // 针对时间选择器 ，真正
    query_form_date_arr,
    history_date,
    other_date,
    timer,
    current_h,
    first_day_startTimeFrom,
    date_option,
    date_option2,
    optional_events_num,
    routeName,
    hover_refresh,
    current_,
    current_promise,
  };
};
