dataSorce: [
  {
    label: "L01",
    value: "LS",
  },
  {
    label: "G01",
    value: "BG",
  },
  {
    label: "PD",
    value: "PD",
  },
  {
    label: "F01",
    value: "F01",
  },
  {
    label: "T01",
    value: "TX",
  },
  {
    label: "N01",
    value: "N01",
  },
  {
    label: "N02",
    value: "N02",
  },
],
timer_priod_list: [
  {
    // 请选择
    label: i18n_t("common.pleaseChoose"),
    value: 0,
  },
  {
    // 上半场
    label: i18n_t("matchPhase.common.w2"),
    value: 1,
  },
  {
    // 下半场
    label: i18n_t("matchPhase.common.w4"),
    value: 2,
  },
],

CMinput: {
  HT: [
    "HT AH",
    "htAhHandicap",
    "htAhOdds1",
    "htAhOdds2",
    "HT OU",
    "htOuHandicap",
    "htOuOdds1",
    "htOuOdds2",
  ],
  FT: [
    "FT AH",
    "ftAhHandicap",
    "ftAhOdds1",
    "ftAhOdds2",
    "FT OU",
    "ftOuHandicap",
    "ftOuOdds1",
    "ftOuOdds2",
  ],
},

old_keys: ["htsup", "htge", "ftsup", "ftge"],
