import {
  api_events_analyse,
  api_risk_control,
  api_order,
  api_settlement_center,
} from "src/api/index.js";

import { lodash, i18n_t } from "src/output/common/project-common";
import { get_params  } from "../module/params";
const compute_api_and_params = (payload) => {
  let params = JSON.parse(JSON.stringify(get_params()));
  if (routerName.value == "redcat_plays") {
    delete params.pageNum;
    delete params.pageSize;
  } else {
    delete params.page;
    delete params.size;
  }
  if (route?.query?.matchManageId) {
    params.matchManageId = route?.query?.matchManageId;
  }
  if (params.beginTime) {
    params.beginTime = new Date(params.beginTime).getTime();
  }
  if (params.endTime) {
    params.endTime = new Date(params.endTime).getTime();
  }

  // 足篮网冰时间戳
  if ([1, 2, 4, 5].includes(Number(route?.query?.sportId))) {
    params.operateStartTime = Date.parse(params.operateStartTime);
    params.operateEndTime = Date.parse(params.operateEndTime);
  }
  if (route?.query?.sportId == 5 || params.operatePageCode == 116) {
    objectNameListShow.value = true;
  } else {
    objectNameListShow.value = false;
  }
  params = delete_empty_property_with_exclude(params);
  config.value.operateTypeList = getOperateTypeList(); // 操作类型判断
  isPost.value = true;
  loading.value = true;
  nodata.value = false;
  let api = config.value.request_api;
  // 报球版跳转到操作日志页面
  if (routerName.value == "setLog") {
    params.sportId = route.query.sportId;
    config.value.operatePageList = getPageList();
    api = "get_MatchPDLog";
    newline.value = false;
    hrefLog.value = true; // 刷新/返回按钮

    //面包屑
    set_footer_breadcrumbs_part_all(
      [i18n_t("event.index.w32")],
      [i18n_t("menus.lm_8.children10")],
      [i18n_t("stCenter.stc141")],
      []
    );
  }
  // RC日志页面
  if (routerName.value == "redcat_plays") {
    config.value.operatePageList = getPageList();
    api = "get_RCLog";
    newline.value = false;
  }

  return {
    params,
    api,
  };
};

const get_log_data_ok = (payload, res) => {
  (res) => {
    let code = lodash.get(res, "data.code");
    let data = lodash.get(res, "data.data") || [];
    let msg = lodash.get(res, "data.msg");
    if (code == 200) {
      let list = data.list || data.records;
      let data1 = list || [];
      if (data1.length >= 1) {
        let sign_list = ["objectName", "extObjectName", "extObjectId"];
        // 部分列表显示字段如果没有显示-
        data1.map((el) => {
          sign_list.map((key) => {
            el[key] = el[key] ? el[key] : "-";
          });
          el.fold_status = false;
          el.createTime = get_times(el.createTime);
          return el;
        });
        // AO足蓝 兵乓球 扩展名称/参数名称/时间格式转换
        if (
          params.operatePageCode == 112 ||
          params.operatePageCode == 113 ||
          params.operatePageCode == 114
        ) {
          data1.forEach((el) => {
            el.extObjectName = el.extObjectName
              ? el.extObjectName.split("@")
              : [];
            el.parameterName = el.parameterName
              ? el.parameterName.split("@")
              : [];
          });
        }

        if (routerName.value == "redcat_plays") {
          data1.forEach((el) => {
            if (el.parameterName)
              el.parameterName = el.parameterName.replace(/\n/g, "<br/>");
            if (el.afterVal) el.afterVal = el.afterVal.replace(/\n/g, "<br/>");
          });
        }
        table_data.value = data1;
        total.value = data.total;
      } else {
        nodata.value = true;
      }
    } else {
      nodata.value = true;
    }
  };
};

/**
 * @description: 请求table数据
 */
export const get_log_data = async (payload) => {
  const {table_data, isPost, loading} = payload;
  table_data.value = [];

  const { params, api } = compute_api_and_params(payload);

  try {
    let res = await api_events_analyse[api](params);
    get_log_data_ok(payload, res);
  } catch (err) {
    Message.error(err);
    console.error(err);
  } finally {
    isPost.value = false;
    loading.value = false;
  }

  // 日志列表接口
};
