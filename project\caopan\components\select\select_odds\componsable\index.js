import { onMounted } from "vue";

import { watcher_register } from "project/caopan/components/select/select_odds/watch/index.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";

import {
  init_data,
  modify_odds_fields,
} from "project/caopan/components/select/select_odds/module/index.js";
export const project_caopan_components_select_select_odds_componsable_index_componsable_fn =
  (raw_payload) => {
    const payload = {
      ...raw_payload,
    };

    // 使用全局watch componsable处理watch相关事务
    src_componsable_util_watcher_register_componsable_fn(
      watcher_register(payload)
    );

    onMounted(() => {
      init_data(payload);
    });

    return {
      modify_odds_fields: (val) => modify_odds_fields(payload, val),
    };
  };
