.bg-panda-bg-2 {
    background: #00a997 !important;
  }
  .bg-panda-bg-8 {
    background: #9e9e9e !important;
  }
  .bg-panda-query-date-active:hover {
    background: #00a99766 !important;
    color: #fff !important;
    border: 0 none;
  }
  .bg-panda-bg-2 {
    background: #00a997 !important;
  }
  .panda-query-date-tab-label:hover {
    //  color: var(--q-color-panda-primary) !important;
    //  border: solid 1px var(--q-color-panda-primary);
  }
  #paswodrd {
    :deep(.q-field--dense .q-field__control) {
      height: 40px;
      min-height: 40px;
    }
    :deep(.q-field__marginal) {
      margin-top: 9px;
    }
  }
  
  .login {
    width: 100vw;
    height: 100vh;
    min-width: 1200px;
    min-height: 600px;
    opacity: 0.96;
    overflow: hidden;
  }
  .white-bg {
    background: url("/assets/grey.png") no-repeat center center;
    background-size: 161px 152px;
    width: 161px;
    height: 152px;
    padding: 6px;
  }
  .imgcode {
    width: 140px;
  }
  .panda-icon-red {
    color: red;
    -webkit-text-fill-color: red;
  }
  
  .pwd-input-message {
    width: 406px;
    height: 34px;
    line-height: 34px;
  }
  
  .password-title {
    font-family: PingFangSC-Semibold;
    font-size: 35px;
    line-height: 40px;
    margin-bottom: 60px;
  }
  
  .password-input-shape {
    width: 406px;
  }
  
  .password-input-item {
    background: #fff;
    border-radius: 4px;
    border-radius: 4px;
    margin-top: 26px;
  }
  :deep(.q-field__native) {
    font-size: 16px;
    color: #333;
  }
  .text-tishi {
    color: #d06263;
  }
  
  // form表单input样式重写
  :deep(.q-field--with-bottom) {
    padding-bottom: 0;
  }
  
  :deep(.q-field__bottom--animated) {
    bottom: 4px;
  }
  
  :deep(.q-field__marginal) {
    font-size: 16px;
  }
  
  :deep(.q-field__native, .q-field__prefix, .q-field__suffix) {
    font-size: 16px;
  }
  .mt1x {
    margin-top: 1px;
  }
  .mt20x {
    margin-top: 20px;
  }
  
  body .q-field--outlined .q-field__control {
    height: 40px !important;
  }