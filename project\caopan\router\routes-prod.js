/*
 * @FilePath: /project/caopan/router/routes.js
 * @Description    :
 *  need_refresh:0,    0  不需要刷新     1  的  数值 需要刷新   （随机 5分钟内 刷新 ） ，  99 必须当时马上刷新
 */
export const default_route = ''

export const routes = [
  {
    path: '/',
    name: 'login',
    meta: {
      annotation: '登录页面',
      need_refresh: 0,
      developer: 'pasta',
    },
    component: () => import('project/caopan/pages/login/login_second/index.vue'),
  },
  {
    path: '/newpassword',
    name: 'newpassword',

    meta: {
      annotation: '登录页面-密码',
      need_refresh: 0,
      developer: 'pasta',
    },
    component: () => import('project/caopan/pages/login/newpassword/index.vue'),
  },
  {
    path: '/password',
    name: 'password',
    meta: {
      annotation: '登录页面-口令',
      need_refresh: 0,
      developer: 'pasta',
    },

    component: () => import('project/caopan/pages/login/password/index.vue'),
  },
  // // 设置集合
  // {
  //   path: '/sets',
  //   name: 'sets',
  //   meta: {
  //     annotation: '设置集合',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/sets/index.vue'),
  // },
  // //老设置-拒绝单-自动调价
  // {
  //   path: '/rests_set',
  //   name: 'rests_set',
  //   meta: {
  //     annotation: '设置-拒绝单-自动调价',
  //     developer: 'rank',
  //     need_refresh: 0,
  //   },

  //   component: () => import('app/project/caopan/pages/sets/set/rests_set/rests_set.vue'),
  // },
  // {
  //   path: '/twoitems',
  //   name: 'twoitems',
  //   meta: {
  //     annotation: '足球2项盘调价窗口',
  //     need_refresh: 0,
  //     developer: 'nice',
  //   },
  //   component: () => import('project/caopan/pages/play_pop/football/tow_items/two_items.vue'),
  // }, //新开弹窗玩法修改
  // {
  //   path: '/two_items_basketball',
  //   name: 'two_items_basketball',
  //   meta: {
  //     annotation: '新开弹窗玩法修改',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/basketball/two_items_basketball.vue'),
  // },
  // {
  //   path: '/special_two_items_basketball',
  //   name: 'special_two_items_basketball',
  //   meta: {
  //     annotation: '新开弹窗玩法修改',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/basketball/special_two_items.vue'),
  // },
  // {
  //   path: '/two_items_tennis',
  //   name: 'two_items_tennis',
  //   meta: {
  //     annotation: '网球新开弹窗玩法修改',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/tennis/two_items_tennis.vue'),
  // },
  // {
  //   path: '/two_items_pingpong',
  //   name: 'two_items_pingpong',
  //   meta: {
  //     annotation: '乒乓球调价窗口',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/pingpong/two_items_pingpong.vue'),
  // },
  // {
  //   path: '/two_items_icy_hocky',
  //   name: 'two_items_icy_hocky',
  //   meta: {
  //     annotation: '冰球调价窗口',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/icy_hocky/two_items_icy_hocky.vue'),
  // },
  // {
  //   path: '/two_items_volleyball',
  //   name: 'two_items_volleyball',
  //   meta: {
  //     annotation: '排球调价窗口',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/volleyball/two_items.vue'),
  // },
  // {
  //   path: '/two_items_snooker',
  //   name: 'two_items_snooker',
  //   meta: {
  //     annotation: '斯诺克调价窗口',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/snooker/two_items.vue'),
  // },
  // {
  //   path: '/two_items_baseball',
  //   name: 'two_items_baseball',
  //   meta: {
  //     annotation: '棒球调价窗口',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/baseball/two_items.vue'),
  // },
  // //新开弹窗三项盘调价窗口修改
  // {
  //   path: '/three_items_football',
  //   name: 'three_items_football',
  //   meta: {
  //     annotation: '足球三项盘调价窗口新版',
  //     need_refresh: 0,
  //     developer: 'nice,pasta',
  //     menuId: '100',
  //   },

  //   component: () => import('project/caopan/pages/play_pop/football/three_items_football.vue'),
  // },
  // // 棒球三项盘调价修改
  // {
  //   path: '/three_items_baseball',
  //   name: 'three_items_baseball',
  //   meta: {
  //     annotation: '足球三项盘调价窗口新版',
  //     need_refresh: 0,
  //     developer: 'nice,pasta',
  //     menuId: '100',
  //   },

  //   component: () => import('project/caopan/pages/play_pop/baseball/three_items_baseball.vue'),
  // },
  // //注单详情/赛事货量统计
  // {
  //   path: '/details',
  //   name: 'details',
  //   meta: {
  //     annotation: '注单详情/赛事货量统计',
  //     need_refresh: 0,
  //     developer: 'nice,pasta',
  //   },
  //   component: () => import('project/caopan/pages/separate_page/deta_ils.vue'),
  // },
  // // 2项盘 调价窗口（足球次要玩法）
  // {
  //   path: '/secondary_play_two_items',
  //   name: 'secondary_play_two_items',
  //   meta: {
  //     annotation: '足球次要玩法两项盘调价窗口',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/play_pop/football/secondary_play_two_items.vue'),
  // },
  // // 2项盘 调价窗口（足球次要玩法-数据类玩法）
  // {
  //   path: '/special_two_items_football',
  //   name: 'special_two_items_football',
  //   meta: {
  //     annotation: '足球次要玩法数据类玩法两项盘调价窗口',
  //     need_refresh: 0,
  //     developer: 'brently',
  //   },

  //   component: () => import('project/caopan/pages/play_pop/football/special_two_items_football.vue'),
  // },
  // // 多项盘 调价窗口（足球次要玩法-数据类玩法）
  // {
  //   path: '/special_three_items_football',
  //   name: 'special_three_items_football',
  //   meta: {
  //     annotation: '足球次要玩法数据类玩法多项盘调价窗口',
  //     need_refresh: 0,
  //     developer: 'brently',
  //   },

  //   component: () => import('project/caopan/pages/play_pop/football/special_three_items_football.vue'),
  // },
  // // 美足两项盘 调价窗口
  // {
  //   path: '/two_items_american_football',
  //   name: 'two_items_american_football',
  //   meta: {
  //     annotation: '美足两项盘调价窗口',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/play_pop/american_football/two_items_american_football.vue'),
  // },
  // // 美足三项盘 调价窗口
  // // {
  // //   path: "/three_items_american_football",
  // //   name: "three_items_american_football",
  // //   meta: {
  // //     annotation: "美足三项盘调价窗口",
  // //     need_refresh: 0
  // //   },

  // //   component: () =>
  // //     import(
  // //       "project/caopan/pages/play_pop/american_football/three_items_american_football.vue"
  // //     )
  // // },
  // // （新窗口）注单中心、取消注单
  // {
  //   path: '/order_manage_order_no',
  //   name: 'order_manage_order_no_',
  //   meta: {
  //     need_refresh: 99,
  //     annotation: '（新窗口）（去菜单公用路由-- 注单等模块）',
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/order_manage_order_no/index.vue'),
  // },
  // // （新窗口）待确认取消赛事
  // {
  //   path: '/redcat_plays',
  //   name: 'redcat_plaYse',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '（新窗口）（去菜单公用路由-- 跳转RC玩法）',
  //     developer: 'moer',
  //   },

  //   component: () => import('project/caopan/pages/match/redcat_plays/index.vue'),
  // },
  // {
  //   path: '/rc_template',
  //   name: 'rc_template',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: 'C01弹窗设置',
  //     developer: 'alieen',
  //   },

  //   component: () => import('project/caopan/pages/match/redcat_plays/rc_template.vue'),
  // },
  // {
  //   path: '/football_secondary_play',
  //   name: 'football_secondary_play_',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '（足球次要玩法3.0）',
  //     developer: 'pasta',
  //   },
  //   component: () => import('project/caopan/pages/football_secondary_play3/index.vue'),
  // },
  // {
  //   path: '/basketball_secondary_play',
  //   name: 'basketball_secondary_play_',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '篮球次要玩法',
  //     developer: 'brently',
  //   },
  //   component: () => import('project/caopan/pages/basketball_secondary_play3/index.vue'),
  // },
  // {
  //   path: '/tennis_secondary_play',
  //   name: 'tennis_secondary_play_',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '网球次要玩法',
  //     developer: 'pasta',
  //   },
  //   component: () => import('project/caopan/pages/tennis_secondary_play/index.vue'),
  // },
  // {
  //   path: '/snapshot_secondary_play',
  //   name: 'snapshot_secondary_play',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '斯洛克次要玩法',
  //     developer: 'pasta',
  //   },
  //   component: () => import('project/caopan/pages/snapshot_secondary_play/index.vue'),
  // },
  // {
  //   path: '/baseball_secondary_play',
  //   name: 'baseball_secondary_play',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '棒球次要玩法',
  //     developer: 'martin',
  //   },
  //   component: () => import('project/caopan/pages/baseball_secondary_play/index.vue'),
  // },
  // {
  //   path: '/two_items_football',
  //   name: 'two_items_football_',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '足球两相盘优化',
  //     developer: 'nice',
  //   },
  //   component: () => import('project/caopan/pages/play_pop/football/two_items_football.vue'),
  // },
  // {
  //   path: '/main/liveOddSupportmorning', //早盘操盘跳转
  //   name: 'trader_manage_morning_o2',
  //   meta: {
  //     annotation: '及时注单跳转滚球、早盘操盘（去菜单、条件）',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },
  //   component: () => import('project/caopan/pages/trader/manage/index.vue'),
  // },
  // {
  //   path: '/main/liveOddSupport', //滚球操盘跳转
  //   name: 'trader_manage_liveOddSupport2',
  //   meta: {
  //     annotation: '及时注单跳转滚球、早盘操盘（去菜单、条件）',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },
  //   component: () => import('project/caopan/pages/trader/manage/index.vue'),
  // },
  // //联赛模板编辑-新
  // {
  //   path: '/new_league_set_templte_edit',
  //   name: 'new_league_set_templte_edit',
  //   meta: {
  //     annotation: '联赛模板编辑',
  //     developer: 'rank',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/operation_set/league_parameter_set/league_set_template_edit/index.vue'),
  // },
  // // 结算参数模板
  // {
  //   path: '/new_settlement_params_template',
  //   name: 'new_settlement_params_template',
  //   meta: {
  //     annotation: '结算参数模板',
  //     developer: 'rank',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/settlement_manage/settlement_params_setting/components/new_settlement_params_template.vue'),
  // },
  // // 及时注单（及时注单模式）
  // {
  //   path: '/current_time_order',
  //   name: 'current_time_order',
  //   meta: {
  //     annotation: '及时注单（及时注单模式）',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/current_time_order/timeliness/index.vue'),
  // },
  // // 及时注单（注单查询模式）
  // {
  //   path: '/current_time_order_order',
  //   name: 'current_time_order_order',
  //   meta: {
  //     annotation: '及时注单（注单查询模式）',
  //     need_refresh: 99,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/current_time_order/order/index.vue'),
  // },
  // // alog
  // {
  //   path: '/alog',
  //   name: 'alog',
  //   meta: {
  //     annotation: 'alog玩法盘口计算器',
  //     need_refresh: 99,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/alog/index.vue'),
  // },
  // // alog
  // {
  //   path: '/alog_basketball',
  //   name: 'alog_basketball',
  //   meta: {
  //     annotation: 'alog篮球玩法盘口计算器',
  //     need_refresh: 99,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/alog/basketball/index.vue'),
  // },
  // {
  //   path: '/alog_pingpang',
  //   name: 'alog_pingpang',
  //   meta: {
  //     annotation: 'alog乒乓球玩法盘口计算器',
  //     need_refresh: 99,
  //     developer: 'brently',
  //   },

  //   component: () => import('project/caopan/pages/alog/pingpang/index.vue'),
  // },
  // {
  //   path: '/current_time_order_info_http',
  //   name: 'current_time_order_info_http',
  //   meta: {
  //     annotation: '操盘玩法设置注单HTTP请求类页面',
  //     need_refresh: 99,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/current_time_order_info_http'),
  // },
  // {
  //   path: '/manual_order',
  //   name: 'manual_order',
  //   meta: {
  //     annotation: '手动注单',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/manual_order'),
  // },
  // // 实时事件
  // {
  //   path: '/game_event',
  //   name: 'game_event',
  //   meta: {
  //     annotation: '实时事件',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/game_event'),
  // },
  // // 实时事件新 2.0
  // {
  //   path: '/game_event_new',
  //   name: 'game_event_new',
  //   meta: {
  //     annotation: '实时事件新 2.0',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },

  //   component: () => import('project/caopan/pages/game_event_new'),
  // },
  // //比分矩阵 score matrix
  // {
  //   path: '/score_matrix',
  //   name: 'score_matrix',
  //   meta: {
  //     annotation: '比分矩阵 score matrix',
  //     need_refresh: 1,
  //     developer: '篮球比分矩阵：wushuang',
  //   },

  //   component: () => import('project/caopan/pages/trader/edit/matchs_core/index.vue'),
  // },
  // //结算审核
  // {
  //   path: '/settlements',
  //   name: 'settlements',
  //   meta: {
  //     annotation: '结算审核',
  //     need_refresh: 1,
  //     developer: 'wushuang',
  //   },

  //   component: () => import('project/caopan/pages/settlements/index.vue'),
  // },
  // // 用户画像
  // {
  //   path: '/user_center',
  //   name: 'user_center',
  //   meta: {
  //     annotation: '用户画像',
  //     need_refresh: 0,
  //     developer: 'rank',
  //   },

  //   component: () => import('project/caopan/pages/user/user_center/index.vue'),
  // },
  // // 玩家组预警处理及查看
  // {
  //   path: '/player_group_alert_action',
  //   name: 'player_group_alert_action',
  //   meta: {
  //     annotation: '玩家组预警处理及查看',
  //     need_refresh: 0,
  //     developer: '',
  //   },

  //   component: () => import('project/caopan/pages/player_group_alert_action/index.vue'),
  // },
  // // 操盘概览
  // {
  //   path: '/trader_overview',
  //   name: 'trader_overview',
  //   meta: {
  //     annotation: '操盘概览',
  //     need_refresh: 0,
  //     developer: 'nice',
  //   },

  //   component: () => import('project/caopan/pages/trader_overview/football/index.vue'),
  // },
  // // 操盘概览
  // {
  //   path: '/event_analyse',
  //   name: 'event_analyse',
  //   annotation: '赛事复盘',
  //   developer: 'rank',
  //   need_refresh: 0,
  //   component: () => import('project/caopan/pages/event_analyse/index.vue'),
  // },
  // // 赛事复盘
  // {
  //   path: '/forecast_overview',
  //   name: 'forecast_overview',
  //   annotation: 'forecast概览',
  //   need_refresh: 0,
  //   developer: 'nice',
  //   component: () => import('project/caopan/pages/forecast_overview/football/index.vue'),
  // },
  // {
  //   path: '/champion_memorandum_view',
  //   name: 'champion_memorandum_view',
  //   meta: {
  //     annotation: '冠军玩法-备忘录详情',
  //     need_refresh: 0,
  //     developer: 'wes',
  //   },
  //   component: () => import('src/components/memorandum_view/index.vue'),
  //   // component: () =>
  //   // import(
  //   //   "project/caopan/pages/match/champion_plays/component/memorandum_view.vue"
  //   // )
  // },
  // {
  //   path: '/ods_linkage',
  //   name: 'ods_linkage',
  //   meta: {
  //     annotation: '冠军玩法-赔率联动',
  //     need_refresh: 0,
  //     developer: 'wes',
  //   },
  //   component: () => import('project/caopan/pages/match/odds_linkage/index.vue'),
  // },

  // // 备忘录
  // {
  //   path: '/memorandum',
  //   name: 'memorandum',
  //   meta: {
  //     annotation: '备忘录',
  //     need_refresh: 0,
  //     developer: 'nice',
  //   },

  //   component: () => import('project/caopan/pages/memorandum/index.vue'),
  // },
  // //结算管理-结算中心（新）---备忘录
  // {
  //   path: '/memorandumv2',
  //   name: 'memorandumv2',
  //   meta: {
  //     annotation: '备忘录（新）',
  //     need_refresh: 0,
  //     developer: 'addis',
  //   },

  //   component: () => import('project/caopan/pages/settlement_manage/settlement_v2/memorandum.vue'),
  // },
  // //
  // // （新窗口）操盘日志
  // {
  //   path: '/traders_log',
  //   name: 'traders_loG',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '（新窗口）（去菜单公用路由-- 操盘日志）',
  //     developer: 'moer',
  //   },

  //   component: () => import('project/caopan/pages/operation_set/traders_log/index.vue'),
  // },
  // // （新窗口）新建、查看控制模板
  // {
  //   path: '/agent_control_template',
  //   name: 'agent_control_template',
  //   meta: {
  //     need_refresh: 0,
  //     annotation: '（新窗口）（显示控制模板-新建、查看）',
  //     developer: 'nelly',
  //   },

  //   component: () => import('project/caopan/pages/agent_configuration/display_control_template/display/index.vue'),
  // },
  // {
  //   path: '/bai_jia_pei', //百家赔
  //   name: 'bai_jia_pei',
  //   meta: {
  //     annotation: '主列表百家赔',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },
  //   component: () => import('project/caopan/pages/trader/manage/module/table_row/bai_jia_pei_all/index.vue'),
  // },
  // {
  //   path: '/bai_jia_pei1', //百家赔
  //   name: 'bai_jia_pei1',
  //   meta: {
  //     annotation: '主列表百家赔',
  //     need_refresh: 0,
  //     developer: 'pasta',
  //   },
  //   component: () => import('project/caopan/pages/trader/manage/module/table_row/bai_jia_pei_all/index1.vue'),
  // },
  {
    path: '/main',
    redirect: { name: 'trader_manage_morning' },
    component: () => import('project/caopan/layouts/MyLayout.vue'),
    children: [
    //   {
    //     path: 'no_sport', //无球种默认路由
    //     name: 'no_sport',
    //     annotation: 'no_sport',
    //     component: () => import('project/caopan/pages/home_page/no_sport.vue'),
    //   },
    //   // {
    //   //   path: "home_page", //首页
    //   //   name: "home_page",
    //   // annotation:"首页",
    //   //   component: () => import("project/caopan/pages/home_page/index.vue")
    //   // },
    //   // {
    //   //   path: "operatorManage", //首操盘手管理
    //   //   name: "operator_manage",
    //   // annotation:"首操盘手管理",
    //   //   component: () => import("project/caopan/pages/operator_manage/index.vue")
    //   // },
    //   {
    //     path: 'redcat_plays',
    //     name: 'redcat_plays',
    //     meta: {
    //       annotation: '红猫玩法',
    //       need_refresh: 0,
    //       developer: 'wes',
    //     },
    //     component: () => import('project/caopan/pages/match/redcat_plays/index.vue'),
    //   },
    //   // 冠军玩法
    {
      path: "champion_plays",
      name: "champion_plays",
      meta: {
        annotation: "冠军玩法",
        need_refresh: 0,
        developer: 'wushuang'
      },

      component: () => import("project/caopan/pages/match/champion_plays/index.vue")
    },
    //   // 冠军玩法
    //   {
    //     path: 'ods_linkage',
    //     name: 'ods_linkage',
    //     meta: {
    //       annotation: '冠军玩法-赔率联动',
    //       need_refresh: 0,
    //       developer: 'wes',
    //     },
    //     component: () => import('project/caopan/pages/match/odds_linkage/index.vue'),
    //   },
    //   // 自研赔率
    //   {
    //     path: 'self_odds', //自研赔率
    //     name: 'self_odds',
    //     meta: {
    //       annotation: '自研赔率',
    //       need_refresh: 0,
    //     },

    //     component: () => import('project/caopan/pages/self_odds/match.vue'),
    //     // 设置meta下的keep_alive字段为true并且组件的name选项与路由的name选项相等可以缓存组件，只对本children有效
    //     children: [
    //       {
    //         path: 'sale', //早盘赔率
    //         name: 'self_odds_sale',
    //         meta: {
    //           annotation: '早盘赔率',
    //           need_refresh: 0,
    //           developer: 'nice',
    //         },

    //         component: () => import('project/caopan/pages/self_odds/sale/index.vue'),
    //       },
    //       {
    //         path: 'live', //滚球赔率
    //         name: 'self_odds_live',
    //         meta: {
    //           annotation: '滚球赔率',
    //           need_refresh: 0,
    //           developer: 'nice',
    //         },

    //         component: () => import('project/caopan/pages/self_odds/live/index.vue'),
    //       },
    //     ],
    //   },
      {
        path: 'match', //早盘管理
        name: 'match',
        meta: {
          annotation: '早盘管理',
          need_refresh: 0,
          developer: 'nice',
          operatePageCode: '14',
        },
        component: () => import('project/caopan/pages/match/match.vue'),
    //     // 设置meta下的keep_alive字段为true并且组件的name选项与路由的name选项相等可以缓存组件，只对本children有效
        children: [
    //       // {
    //       //   path: "early_game", //早盘赛事
    //       //   name: "early_game",
    //       //   meta: {
    //       //     annotation: "早盘管理-早盘赛事",
    //       //     need_refresh: 99,
    //       //     developer: 'pasta',
    //       //   },

    //       //   component: () =>
    //       //     import(
    //       //       "project/caopan/pages/match/early_game/index.vue"
    //       //     )
    //       // },
          {
            path: 'sale', //早盘管理-早盘赛事
            name: 'match_sale',
            meta: {
              annotation: '早盘管理-早盘赛事',
              need_refresh: 99,
              developer: 'darwin',
            },
            component: () => import('project/caopan/pages/match/sale/index.vue'),
          },
    //       {
    //         path: 'play_tabs', //玩法页
    //         name: 'match_play_tabs',
    //         meta: {
    //           annotation: '早盘管理-玩法页',
    //           need_refresh: 99,
    //           developer: 'darwin',
    //         },

    //         component: () => import('src/components/play_tabs/index.vue'),
    //       },
    //       {
    //         path: 'liveOddSupportmorning', //早盘操盘
    //         name: 'trader_manage_morning_o',
    //         meta: {
    //           annotation: '早盘管理-早盘操盘',
    //           need_refresh: 0,
    //           developer: 'nice',
    //         },
    //         component: () => import('project/caopan/pages/trader/manage/index.vue'),
    //       },
    //       {
    //         path: 'early_report', //早盘报表
    //         name: 'early_report',
    //         meta: {
    //           annotation: '早盘管理-早盘报表',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/match/early_report/index.vue'),
    //       },
    //       {
    //         path: 'discount_odds', //优惠盘口
    //         name: 'discount_odds',
    //         meta: {
    //           annotation: '早盘管理-优惠盘口',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/match/discount_odds/index.vue'),
    //       },
        ],
      },
      {
        path: 'rolling_ball_manage', //滚球管理
        name: 'rolling_ball_manage',
        meta: {
          annotation: '滚球管理',
          need_refresh: 0,
          developer: 'nice',
          operatePageCode: 17,
        },

        component: () => import('project/caopan/pages/rolling_ball_manage/index.vue'),
        // 设置meta下的keep_alive字段为true并且组件的name选项与路由的name选项相等可以缓存组件，只对本children有效
        children: [
          {
            path: "bowling_events", //滚球赛事
            name: "bowling_events",
            meta: {
              annotation: "滚球管理-滚球赛事",
              need_refresh: 99,
              developer: 'nice'
            },

            component: () =>
              import(
                "project/caopan/pages/rolling_ball_manage/bowling_events/index.vue"
              )
          },
    //       {
    //         path: 'live', //滚球管理-滚球赛事
    //         name: 'match_live',
    //         meta: {
    //           annotation: '滚球管理-滚球赛事',
    //           need_refresh: 99,
    //           developer: 'darwin',
    //         },

    //         component: () => import('project/caopan/pages/match/live/index.vue'),
    //       },
    //       {
    //         path: 'liveOddSupport', //滚球操盘
    //         name: 'trader_manage_liveOddSupport',
    //         meta: {
    //           annotation: '滚球管理-滚球操盘',
    //           need_refresh: 0,
    //           developer: 'nice,pasta',
    //         },

    //         component: () => import('project/caopan/pages/trader/manage/index.vue'),
    //       },
    //       {
    //         path: 'rolling_ball_report', //滚球报表
    //         name: 'rolling_ball_report',
    //         meta: {
    //           annotation: '滚球管理-滚球报表',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/rolling_ball_manage/rolling_ball_report/index.vue'),
    //       },
        ],
      },
    //   {
    //     path: 'history_fixtures', // 历史赛程
    //     name: 'history_fixtures',
    //     meta: {
    //       annotation: '历史赛程',
    //     },
    //     component: () => import('project/caopan/pages/match/match.vue'),
    //     children: [
    //       // 历史赛程
    //       {
    //         path: 'history_match',
    //         name: 'history_match',
    //         meta: {
    //           annotation: '历史赛程',
    //           developer: 'darwin',
    //         },
    //         component: () => import('project/caopan/pages/match/history_match/index.vue'),
    //       },
    //     ],
    //   },
    //   {
    //     path: 'operation_set', // 操盘设置
    //     name: 'operation_set',
    //     meta: {
    //       annotation: '操盘设置',
    //     },

    //     component: () => import('project/caopan/pages/operation_set/index.vue'),
    //     children: [
    //       {
    //         path: 'pre_sale_set', //操盘设置-预开售列表
    //         name: 'pre_sale_set',
    //         meta: {
    //           annotation: '操盘设置-预开售列表',
    //           need_refresh: 99,
    //           developer: 'darwin',
    //         },

    //         component: () => import('project/caopan/pages/operation_set/pre_sale_set/index.vue'),
    //       },
    //       {
    //         path: 'league_parameter_set', //联赛参数设置-新
    //         name: 'league_parameter_set',
    //         meta: {
    //           annotation: '操盘设置-联赛参数设置',
    //           need_refresh: 0,
    //           developer: 'rank',
    //           operatePageCode: 21,
    //         },

    //         component: () => import('project/caopan/pages/operation_set/league_parameter_set/index.vue'),
    //       },

    //       {
    //         path: 'play_management', //玩法管理
    //         name: 'play_management',
    //         meta: {
    //           annotation: '操盘设置-玩法管理',
    //           need_refresh: 0,
    //           developer: 'darwin',
    //         },

    //         component: () => import('project/caopan/pages/operation_set/play_management/index.vue'),
    //       },
    //       {
    //         path: 'play_collection_manage', //玩法集管理
    //         name: 'play_collection_manage',
    //         meta: {
    //           annotation: '玩法集管理',
    //           developer: 'darwin',
    //         },

    //         component: () => import('project/caopan/pages/operation_set/play_collection_manage/index.vue'),
    //         redirect: { name: 'play_collection_manage_main' },
    //         children: [
    //           {
    //             path: 'main',
    //             name: 'play_collection_manage_main',
    //             meta: {
    //               annotation: '操盘设置-玩法集管理-列表',
    //               need_refresh: 0,
    //               developer: 'darwin',
    //             },

    //             component: () => import('project/caopan/pages/operation_set/play_collection_manage/main/index.vue'),
    //           },
    //           {
    //             path: 'edit',
    //             name: 'play_collection_manage_edit',
    //             meta: {
    //               annotation: '操盘设置-玩法集管理-编辑',
    //               need_refresh: 0,
    //               developer: 'darwin',
    //             },

    //             component: () => import('project/caopan/pages/operation_set/play_collection_manage/edit/index.vue'),
    //           },
    //         ],
    //       },
    //       {
    //         path: 'e_play_management', //电子竞技玩法管理
    //         name: 'e_play_management',
    //         meta: {
    //           annotation: '操盘设置-电子竞技玩法管理',
    //           need_refresh: 0,
    //           developer: 'darwin',
    //         },
    //         component: () => import('project/caopan/pages/operation_set/play_management/index.vue'),
    //       },

    //       {
    //         path: 'e_play_collection_manage', //电子竞技玩法集管理
    //         name: 'e_play_collection_manage',
    //         meta: {
    //           annotation: '操盘设置-电子竞技玩法集管理',
    //           developer: 'darwin',
    //         },
    //         component: () => import('project/caopan/pages/operation_set/play_collection_manage/index.vue'),
    //         redirect: { name: 'e_play_collection_manage_main' },
    //         children: [
    //           {
    //             path: 'main',
    //             name: 'e_play_collection_manage_main',
    //             meta: {
    //               annotation: '操盘设置- 电子竞技玩法集管理-列表',
    //               need_refresh: 0,
    //               developer: 'darwin',
    //             },
    //             component: () => import('project/caopan/pages/operation_set/play_collection_manage/main/index.vue'),
    //           },
    //           {
    //             path: 'edit',
    //             name: 'e_play_collection_manage_edit',
    //             meta: {
    //               annotation: '操盘设置- 电子竞技玩法集管理-编辑',
    //               need_refresh: 0,
    //               developer: 'darwin',
    //             },
    //             component: () => import('project/caopan/pages/operation_set/play_collection_manage/edit/index.vue'),
    //           },
    //         ],
    //       },

    //       {
    //         path: 'virtual_play_management', //虚拟玩法管理
    //         name: 'virtual_play_management',
    //         meta: {
    //           annotation: '操盘设置-虚拟玩法管理',
    //           need_refresh: 0,
    //           developer: 'darwin',
    //         },

    //         component: () => import('project/caopan/pages/operation_set/play_management/index.vue'),
    //       },

    //       {
    //         path: 'virtual_play_collection_manage', //虚拟玩法集管理
    //         name: 'virtual_play_collection_manage',
    //         meta: {
    //           annotation: '操盘设置-虚拟玩法集管理',
    //           developer: 'darwin',
    //         },

    //         component: () => import('project/caopan/pages/operation_set/play_collection_manage/index.vue'),
    //         redirect: { name: 'virtual_play_collection_manage_main' },
    //         children: [
    //           {
    //             path: 'main',
    //             name: 'virtual_play_collection_manage_main',
    //             meta: {
    //               annotation: '操盘设置- 虚拟玩法集管理-列表',
    //               need_refresh: 0,
    //               developer: 'darwin',
    //             },

    //             component: () => import('project/caopan/pages/operation_set/play_collection_manage/main/index.vue'),
    //           },
    //           {
    //             path: 'edit',
    //             name: 'virtual_play_collection_manage_edit',
    //             meta: {
    //               annotation: '操盘设置- 虚拟玩法集管理-编辑',
    //               need_refresh: 0,
    //               developer: 'darwin',
    //             },

    //             component: () => import('project/caopan/pages/operation_set/play_collection_manage/edit/index.vue'),
    //           },
    //         ],
    //       },
    //       {
    //         path: 'event_list', //事件列表
    //         name: 'event_list',
    //         meta: {
    //           annotation: '操盘设置-事件列表',
    //           need_refresh: 0,
    //           developer: 'rank',
    //         },

    //         component: () => import('project/caopan/pages/operation_set/event_list/index.vue'),
    //       },
    //       {
    //         path: 'expectations1', //联赛期望
    //         name: 'expectations1',
    //         meta: {
    //           annotation: '操盘设置-联赛期望1',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/league_expectations/expectations1/index.vue'),
    //       },
    //       {
    //         path: 'expectations2',
    //         name: 'expectations2',
    //         meta: {
    //           annotation: '操盘设置-联赛期望2',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/league_expectations/expectations2/index.vue'),
    //       },
    //       {
    //         path: 'sportsRules',
    //         name: 'sportsRules',
    //         meta: {
    //           annotation: '体育规则',
    //           need_refresh: 0,
    //         },
    //       },
    //       {
    //         path: 'traders_log',
    //         name: 'traders_log',
    //         meta: {
    //           annotation: '操盘设置-操盘日志',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/operation_set/traders_log/index.vue'),
    //       },
    //       {
    //         path: 'traders_forecast_snapshot', // forcast快照
    //         name: 'traders_forecast_snapshot',
    //         meta: {
    //           annotation: '操盘设置-Forecast快照',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/operation_set/traders_forecast_snapshot/index.vue'),
    //       },
    //     ],
    //   },
    //   {
    //     path: 'settlement_manage', //结算管理
    //     name: 'settlement_manage',
    //     meta: {
    //       annotation: '结算管理',
    //       developer: 'downey,wushuang',
    //     },

    //     component: () => import('project/caopan/pages/settlement_manage/index.vue'),
    //     children: [
    //       {
    //         // 结算管理-PA 报球板
    //         path: 'PA_to_the_bat',
    //         name: 'PA_to_the_bat',
    //         meta: {
    //           annotation: '结算管理-PA 报球板',
    //           need_refresh: 99,
    //           developer: 'downey,wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/PA_to_the_bat/index.vue'),
    //       },
    //       {
    //         // 结算管理-PA 报球板
    //         path: 'PA_to_the_bat_new',
    //         name: 'PA_to_the_bat_new',
    //         meta: {
    //           annotation: '结算管理-PA 报球板',
    //           need_refresh: 99,
    //           developer: 'suho',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/PA_to_the_bat_third_edition/index.vue'),
    //       },
    //       {
    //         // 结算管理-PA 报球板
    //         path: 'PA_to_the_bat_ipad',
    //         name: 'PA_to_the_bat_ipad',
    //         meta: {
    //           annotation: '结算管理-PA 报球板',
    //           need_refresh: 99,
    //           developer: 'suho',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/PA_to_the_bat_third_edition/index.vue'),
    //       },
    //       {
    //         // 结算管理-PA 报球板
    //         path: 'PA_to_the_bat_ipad_sim',
    //         name: 'PA_to_the_bat_ipad_sim',
    //         meta: {
    //           annotation: '结算管理-PA 报球板',
    //           need_refresh: 99,
    //           developer: 'suho',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/PA_to_the_bat_third_edition/index.vue'),
    //       },
    //       {
    //         path: 'setLog',
    //         name: 'setLog',
    //         meta: {
    //           annotation: '结算管理-PA 报球板-报球版',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/operation_set/traders_log/index.vue'),
    //       },
    //       {
    //         // 结算管理-事件审核
    //         path: 'event_review',
    //         name: 'event_review',
    //         meta: {
    //           annotation: '结算管理-事件审核',
    //           need_refresh: 99,
    //           developer: 'downey,wushuang',
    //         },
    //         component: () => import('project/caopan/pages/settlement_manage/event_review/index.vue'),
    //       },
    //       {
    //         // 结算管理-比分中心
    //         path: 'score_center',
    //         name: 'score_center',
    //         meta: {
    //           annotation: '结算管理-比分中心',
    //           need_refresh: 99,
    //           developer: 'downey',
    //         },
    //         component: () => import('project/caopan/pages/settlement_manage/score_center/index.vue'),
    //       },
    //       {
    //         // 结算管理-比分中心
    //         path: 'score_center_settings',
    //         name: 'score_center_settings',
    //         meta: {
    //           annotation: '结算管理-比分中心设置',
    //           need_refresh: 99,
    //           developer: 'downey',
    //         },
    //         component: () => import('project/caopan/pages/settlement_manage/score_center_settings/index.vue'),
    //       },
    //       {
    //         // 结算管理-结算日志
    //         path: 'settlement_log',
    //         name: 'settlement_log',
    //         meta: {
    //           annotation: '结算管理-结算日志',
    //           need_refresh: 99,
    //           developer: 'downey',
    //         },
    //         component: () => import('project/caopan/pages/settlement_manage/settlement_log/index.vue'),
    //       },
    //       {
    //         // 结算管理-结算监控效率
    //         path: 'settlement_efficiency_monitoring',
    //         name: 'settlement_efficiency_monitoring',
    //         meta: {
    //           annotation: '结算管理-结算监控效率',
    //           need_refresh: 99,
    //           developer: 'downey',
    //         },
    //         component: () => import('project/caopan/pages/settlement_manage/settlement_efficiency_monitoring/index.vue'),
    //       },
    //       {
    //         //结算管理-结算参数设置
    //         path: 'settlement_params_setting',
    //         name: 'settlement_params_setting',
    //         meta: {
    //           annotation: '结算管理-结算参数设置',
    //           need_refresh: 0,
    //           developer: 'downey,wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/settlement_params_setting/index.vue'),
    //       },
    //       {
    //         //结算管理-结算中心
    //         path: 'settlement_center',
    //         name: 'settlement_center',
    //         meta: {
    //           annotation: '结算管理-结算中心',
    //           need_refresh: 0,
    //           developer: 'downey,wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/settlement_center/index.vue'),
    //       },
    //       {
    //         //结算管理-结算中心（新）
    //         path: 'settlement_v2',
    //         name: 'settlement_v2',
    //         meta: {
    //           annotation: '结算管理-结算中心（新）',
    //           need_refresh: 0,
    //           developer: 'wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/settlement_v2/index.vue'),
    //       },
    //       {
    //         //结算管理-结算中心（新）---操作日志
    //         path: 'settlement_v2_log',
    //         name: 'settlement_v2_log',
    //         meta: {
    //           annotation: '结算管理-结算中心（新）-操作日志',
    //           need_refresh: 0,
    //           developer: 'wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/settlement_v2/settlement_v2_log.vue'),
    //       },
    //       {
    //         // 结算中心-比赛页
    //         path: 'settlement_match',
    //         name: 'settlement_match',
    //         meta: {
    //           annotation: '结算中心-比赛页',
    //           need_refresh: 0,
    //           developer: 'downey,wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/settlement_match/index.vue'),
    //         meta: {
    //           keepAlive: true,
    //         },
    //       },
    //       {
    //         // 结算中心-盘口页
    //         path: 'settlement_handicap',
    //         name: 'settlement_handicap',
    //         meta: {
    //           annotation: '结算中心-盘口页',
    //           need_refresh: 0,
    //           developer: 'downey,wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/settlement_handicap/index.vue'),
    //       },
    //       {
    //         // 结算管理-待处理异常赛事
    //         path: 'abnormal_event_to_be_handled',
    //         name: 'abnormal_event_to_be_handled',
    //         meta: {
    //           annotation: '结算管理-待处理异常赛事',
    //           need_refresh: 99,
    //           developer: 'downey,wushuang',
    //           keepAlive: false,
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/abnormal_event_to_be_handled/index.vue'),
    //       },
    //       {
    //         // 结算管理-派彩异常监控
    //         path: 'send_color_abnormal_query',
    //         name: 'send_color_abnormal_query',
    //         meta: {
    //           annotation: '结算管理-派彩异常监控',
    //           need_refresh: 99,
    //           developer: 'wushuang',
    //           keepAlive: false,
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/send_color_abnormal_query/index.vue'),
    //       },
    //       {
    //         // 结算管理-待结算赛事页面
    //         path: 'event_to_be_settled',
    //         name: 'event_to_be_settled',
    //         meta: {
    //           annotation: '结算管理-待结算赛事页面',
    //           need_refresh: 0,
    //           developer: 'wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/event_to_be_settled/index.vue'),
    //       },
    //       {
    //         //结算管理-待结算赛事---操作日志
    //         path: 'outstanding_log',
    //         name: 'outstanding_log',
    //         meta: {
    //           annotation: '结算管理-待结算赛事-操作日志',
    //           need_refresh: 0,
    //           developer: 'downey',
    //         },
    //         component: () => import('project/caopan/pages/settlement_manage/event_to_be_settled/outstanding_log.vue'),
    //       },
    //       {
    //         path: 'unsold_match', //结算管理-仅早盘开售
    //         name: 'unsold_match',
    //         meta: {
    //           annotation: '结算管理-仅早盘开售',
    //           need_refresh: 99,
    //           developer: 'darwin',
    //         },

    //         component: () => import('project/caopan/pages/match/unsold_match/index.vue'),
    //       },
    //       {
    //         // 结算管理-结算统计报表
    //         path: 'settlement_setting',
    //         name: 'settlement_setting',
    //         meta: {
    //           annotation: '结算设置',
    //           need_refresh: 0,
    //           developer: 'wushuang',
    //         },

    //         component: () => import('project/caopan/pages/settlement_manage/settlement_setting/index.vue'),
    //       },
    //     ],
    //   },
    //   // {
    //   //   path: "risk_control",
    //   //   name: "risk_control_",
    //   //   meta: {
    //   //     annotation: "报表中心"
    //   //   },

    //   //   component: () =>
    //   //     import(
    //   //       "project/caopan/pages/risk_control/index.vue"
    //   //     ),
    //   //   children: [
    //   //     // 投注概况分析
    //   //     {
    //   //       path: "bet_report",
    //   //       name: "bet_report_",
    //   //       meta: {
    //   //         annotation: "报表中心-投注概况分析",
    //   //         need_refresh: 99,
    //   //         developer: 'pasta',
    //   //       },

    //   //       component: () =>
    //   //         import(
    //   //           "project/caopan/pages/risk_control/bet_report/index.vue"
    //   //         )
    //   //     },
    //   //     // 赛事注单汇总
    //   //     {
    //   //       path: "game_bet_report",
    //   //       name: "game_bet_report_",
    //   //       meta: {
    //   //         annotation: "报表中心-赛事注单汇总统计报表",
    //   //         need_refresh: 99,
    //   //         developer: 'pasta',
    //   //       },

    //   //       component: () =>
    //   //         import(
    //   //           "project/caopan/pages/risk_control/game_bet_report/index.vue"
    //   //         )
    //   //     },
    //   //     // 赛事注单玩法
    //   //     {
    //   //       path: "game_play_report",
    //   //       name: "game_play_report_",
    //   //       meta: {
    //   //         annotation: "报表中心-赛事注单玩法统计报表",
    //   //         need_refresh: 99,
    //   //         developer: 'pasta',
    //   //       },

    //   //       component: () =>
    //   //         import(
    //   //           "project/caopan/pages/risk_control/game_play_report/index.vue"
    //   //         )
    //   //     },
    //   //     // 报表中心-赛事注单用户统计报表
    //   //     {
    //   //       path: "game_user_report",
    //   //       name: "game_user_report_",
    //   //       meta: {
    //   //         annotation: "报表中心-赛事注单用户统计报表",
    //   //         need_refresh: 99,
    //   //         developer: 'pasta',
    //   //       },

    //   //       component: () =>
    //   //         import(
    //   //           "project/caopan/pages/risk_control/game_user_report/index.vue"
    //   //         )
    //   //     },
    //   //     // // 预警
    //   //     // {
    //   //     //   path:"alert_statistics_report",
    //   //     //   name:'alert_statistics_report_',
    //   //     //annotation:"预警",
    //   //     //   component: () => import("project/caopan/pages/risk_control/alert_statistics_report/index.vue"),
    //   //     // },
    //   //     // 报表中心-商户报表
    //   //     {
    //   //       path: "bet_total_report",
    //   //       name: "bet_total_report_",
    //   //       meta: {
    //   //         annotation: "报表中心-商户报表",
    //   //         need_refresh: 99,
    //   //         developer: 'pasta',
    //   //       },

    //   //       component: () =>
    //   //         import(
    //   //           "project/caopan/pages/risk_control/bet_total_report/index.vue"
    //   //         )
    //   //     },
    //   //     // 报表中心-操盘经营报表
    //   //     {
    //   //       path:"trader_operate_report",
    //   //       name:'trader_operate_report_',
    //   //       annotation:"报表中心-操盘经营报表",
    //   //       meta: {
    //   //         annotation: "报表中心-商户报表",
    //   //         need_refresh: 99,
    //   //         developer: 'pasta',
    //   //       },
    //   //       component: () => import("project/caopan/pages/risk_control/trader_operate_report/index.vue"),
    //   //     },
    //   //     // 赛事数据统计报表
    //   //     {
    //   //       path: "race_data_statistical_report",
    //   //       name: "race_data_statistical_report_",
    //   //       meta: {
    //   //         annotation: "报表中心-赛事数据统计报表",
    //   //         need_refresh: 99,
    //   //         developer: 'pasta',
    //   //       },

    //   //       component: () =>
    //   //         import(
    //   //           "project/caopan/pages/risk_control/race_data_statistical_report/index.vue"
    //   //         )
    //   //     },
    //   //     // 报表中心-风控工作台
    //   //     // {
    //   //     //   path: "risk_control_workbench",
    //   //     //   name: "risk_control_workbench_",
    //   //     //   meta: {
    //   //     //     annotation: "报表中心-风控工作台",
    //   //     //     need_refresh: 0
    //   //     //   },
    //   //     //   component: () => import("project/caopan/pages/risk_control/risk_control_workbench/index.vue"),
    //   //     // },
    //   //     // 操盘业绩看板
    //   //     {
    //   //       path: "trader_performance",
    //   //       name: "trader_performance_",
    //   //       meta: {
    //   //         annotation: "报表中心-操盘业绩看板",
    //   //         need_refresh: 99,
    //   //         developer: 'pasta',
    //   //       },

    //   //       component: () =>
    //   //         import(
    //   //           "project/caopan/pages/risk_control/trader_performance/index.vue"
    //   //         )
    //   //     }
    //   //   ]
    //   // },
    //   //风控设置
    //   {
    //     path: 'risk_control_set',
    //     name: 'risk_management',
    //     meta: {
    //       annotation: '风控管理',
    //     },

    //     component: () => import('project/caopan/pages/risk_management/index.vue'),
    //     // 设置meta下的keep_alive字段为true并且组件的name选项与路由的name选项相等可以缓存组件，只对本children有效
    //     children: [
    //       {
    //         path: 'risk_control_measures', //风控措施
    //         name: 'risk_control_measures',
    //         meta: {
    //           annotation: '风控措施',
    //           need_refresh: 0,
    //           developer: 'nelly',
    //         },

    //         component: () => import('project/caopan/pages/risk_management/risk_control_measures/index.vue'),
    //       },
    //       {
    //         path: 'risk_monitoring', //风险监控
    //         name: 'risk_monitoring',
    //         meta: {
    //           annotation: '风险监控',
    //           need_refresh: 0,
    //           developer: 'nelly',
    //         },

    //         component: () => import('project/caopan/pages/risk_management/risk_monitoring/index.vue'),
    //       },
    //       {
    //         path: 'log', //日志记录
    //         name: 'log',
    //         meta: {
    //           annotation: '日志记录',
    //           need_refresh: 0,
    //           developer: 'nelly',
    //         },

    //         component: () => import('project/caopan/pages/risk_management/log/index.vue'),
    //       },
    //     ],
    //   },
    
      // 任务中心
      {
        path: 'risk_task_center', 
        name: 'risk_task_center',
        meta: {
          annotation: '风控设置-任务中心',
          need_refresh: 0,
          developer: 'lane',
        },
        component: () => import('project/caopan/pages/risk_control_set/risk_task_center/index.vue'),
      },
    //   // {
    //   //   path: "risk_control_early_warn", //风控预警
    //   //   name: "risk_control_early_warn",
    //   //   meta: {
    //   //     annotation: "风控预警",
    //   //     need_refresh: 0,
    //   //     developer: 'rank',
    //   //   },

    //   //   component: () =>
    //   //     import(
    //   //       "project/caopan/pages/risk_control_early_warn/index.vue"
    //   //     )
    //   // },
      {
        path: 'operations_management', //运营管理
        name: 'operations_management',
        meta: {
          annotation: '运营管理',
        },

        component: () => import('project/caopan/pages/operations_management/index.vue'),
        children: [
          // 热推赛事
          {
            path: 'operate_manage',
            name: 'operate_manage',
            meta: {
              annotation: '运营管理-热推赛事',
              need_refresh: 0,
              developer: 'pasta',
            },

            component: () => import('project/caopan/pages/operation_set/operate_manage/index.vue'),
            // component: () =>import( "project/caopan/pages/operation_set/dra/index.vue")
          },

          // 联赛排序
          {
            path: 'league_ranking',
            name: 'league_ranking',
            meta: {
              annotation: '运营管理-联赛排序',
              need_refresh: 0,
              developer: 'rank',
            },
            component: () => import('src/components/league_ranking/index.vue'),
          },

          // {
          //   path: "event_sequencing", //赛事排序
          //   name: "event_sequencing",
          //   meta: {
          //     annotation: "运营管理-赛事排序",
          //     need_refresh: 0,
          //     developer: 'rank'
          //   },

          //   component: () =>
          //     import(
          //       "project/caopan/pages/operations_management/event_sequencing/index.vue"
          //     )
          // },

          // 热词
          {
            path: 'key_word',
            name: 'key_word',
            meta: {
              annotation: '运营管理-热词',
              need_refresh: 0,
              developer: 'nice',
            },
            component: () => import('project/caopan/pages/operations_management/key_word/index.vue'),
          },
          
          // 热门置顶排序
          {
            path: 'popular_top_ranking',
            name: 'popular_top_ranking',
            meta: {
              annotation: '热门置顶排序',
              need_refresh: 0,
              developer: 'nice',
            },
            component: () => import('project/caopan/pages/operations_management/popular_top_ranking/index.vue'),
          },

          // 公告栏
          {
            path: 'bulletin_board',
            name: 'bulletin_board',
            meta: {
              annotation: '运营管理-公告栏',
              need_refresh: 0,
              developer: 'nice,rank',
            },
            component: () => import('project/caopan/pages/operations_management/bulletin_board/index/index.vue'),
          },

          // 公告栏 - 新建
          {
            path: 'bulletin_board_create',
            name: 'bulletin_board_create',
            meta: {
              annotation: '运营管理-公告栏 -- 新建',
              need_refresh: 0,
              developer: 'nice,rank',
            },
            component: () => import('project/caopan/pages/operations_management/bulletin_board/create/create.vue'),
          },

          // 公告栏 -- 编辑
          {
            path: 'bulletin_board_edit',
            name: 'bulletin_board_edit',
            meta: {
              annotation: '运营管理-公告栏 -- 编辑',
              need_refresh: 0,
              developer: 'nice,rank',
            },
            component: () => import('project/caopan/pages/operations_management/bulletin_board/edit/edit.vue'),
          },
          
          // 运营管理-联赛菜单
          {
            path: 'league_menu',
            name: 'league_menu',
            meta: {
              annotation: '运营管理-联赛菜单',
              need_refresh: 0,
              developer: 'nice',
            },
            component: () => import('project/caopan/pages/operations_management/league_menu/index.vue'),
          },

          // 运营管理-搜索设置
          {
            path: 'menu_settings',
            name: 'menu_settings',
            meta: {
              annotation: '运营管理-搜索设置',
              need_refresh: 0,
              developer: 'nice',
            },
            component: () => import('project/caopan/pages/operations_management/menu_settings/index.vue'),
          },

          // 运营管理-PC热门联赛快捷入口
          {
            path: 'pc_hot_league_enter',
            name: 'pc_hot_league_enter',
            meta: {
              annotation: '运营管理-PC热门联赛快捷入口',
              need_refresh: 0,
              developer: 'malick',
            },
            component: () => import('project/caopan/pages/operations_management/pc_hot_league_enter/index.vue'),
          },

          // 运营管理-PC/H5列表页管理
          {
            path: 'pc_list_page',
            name: 'pc_list_page',
            meta: {
              annotation: '运营管理-PC/H5列表页管理',
              need_refresh: 0,
              developer: 'moer',
            },
            component: () => import('project/caopan/pages/operations_management/pc_list_page/index.vue'),
          },
          
          // 运营管理-热门联赛配置
          {
            path: 'hot_league_config',
            name: 'hot_league_config',
            meta: {
              annotation: '运营管理-热门联赛配置',
              need_refresh: 0,
              developer: 'malick',
            },
            component: () => import('project/caopan/pages/operations_management/hot_league_config/index.vue'),
          },

          // 运营管理-联赛菜单配置
          {
            path: 'league_menu_config',
            name: 'league_menu_config',
            meta: {
              annotation: '运营管理-联赛菜单配置',
              need_refresh: 0,
              developer: 'malick',
            },
            component: () => import('project/caopan/pages/operations_management/league_menu_config/index.vue'),
          },
        ],
      },

    //   {
    //     path: 'user',
    //     name: 'user',
    //     meta: {
    //       annotation: '用户中心',
    //     },

    //     component: () => import('project/caopan/pages/user/index.vue'),
    //     children: [
    //       // 用户列表
    //       {
    //         path: 'user_manage',
    //         name: 'user_manage',
    //         meta: {
    //           annotation: '用户中心-用户列表',
    //           need_refresh: 1,
    //           developer: 'pasta,rank',
    //         },

    //         component: () => import('project/caopan/pages/user/user_mange/index.vue'),
    //       },
    //       {
    //         path: 'user_group_set',
    //         name: 'user_group_set',
    //         meta: {
    //           annotation: '用户中心-用户组设置',
    //           need_refresh: 1,
    //           developer: 'rank',
    //         },

    //         component: () => import('project/caopan/pages/user/user_group_set/index.vue'),
    //       },
    //       // 投注IP管理
    //       {
    //         path: 'bet_ip_management',
    //         name: 'bet_ip_management',
    //         meta: {
    //           need_refresh: 1,
    //           developer: 'rank',
    //         },

    //         component: () => import('project/caopan/pages/user/bet_ip_management/index.vue'),
    //       },
    //       // 投注限额查询
    //       {
    //         path: 'bet_limit_enquiry',
    //         name: 'bet_limit_enquiry',
    //         meta: {
    //           need_refresh: 1,
    //           developer: 'rank',
    //         },

    //         component: () => import('project/caopan/pages/user/bet_limit_enquiry/index.vue'),
    //       },
    //       // {//日常协查
    //       //   path: "daily_investigation",
    //       //   name: "daily_investigation",
    //       //   meta: {
    //       //     need_refresh: 1,
    //       //     developer: 'rank'
    //       //   },

    //       //   component: () =>
    //       //     import(
    //       //       "project/caopan/pages/user/daily_investigation/index.vue"
    //       //     )
    //       // },
    //       {
    //         //用户变更日志
    //         path: 'user_change_log',
    //         name: 'user_change_log',
    //         meta: {
    //           need_refresh: 1,
    //           developer: 'rank',
    //         },

    //         component: () => import('project/caopan/pages/user/user_change_log2/index.vue'),
    //       },
    //       {
    //         //用户管控审核
    //         path: 'merchant_control_list',
    //         name: 'merchant_control_list',
    //         meta: {
    //           need_refresh: 1,
    //           developer: 'nico',
    //         },

    //         component: () => import('project/caopan/pages/user/merchant_control_list/index.vue'),
    //       },
    //       {
    //         //用户指纹池
    //         path: 'user_fingerprint_pool',
    //         name: 'user_fingerprint_pool',
    //         meta: {
    //           need_refresh: 1,
    //           developer: 'tony',
    //         },

    //         component: () => import('project/caopan/pages/user/user_fingerprint_pool/index.vue'),
    //       },
    //       {
    //         //相似用户查询
    //         path: 'similar_user_query',
    //         name: 'similar_user_query',
    //         meta: {
    //           need_refresh: 1,
    //           developer: 'tony',
    //         },

    //         component: () => import('project/caopan/pages/user/similar_user_query/index.vue'),
    //       },
    //     ],
    //   },
    //   // {
    //   //   path: "task_center", //任务中心
    //   //   name: "task_center",
    //   //annotation:"任务中心",
    //   //   component: () => import("project/caopan/pages/task_center/index.vue")
    //   // },
    //   {
    //     path: 'order_manage',
    //     name: 'order_manage_',
    //     meta: {
    //       annotation: '注单管理',
    //       developer: 'pasta',
    //     },

    //     component: () => import('project/caopan/pages/order_mange/index.vue'),
    //     children: [
    //       // 注单中心
    //       {
    //         path: 'center',
    //         name: 'order_center_',
    //         meta: {
    //           annotation: '注单管理-注单中心',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center/index/index.vue'),
    //       },
    //       // 注单中心(新)
    //       {
    //         path: 'center_new',
    //         name: 'order_center_new',
    //         meta: {
    //           annotation: '注单管理-注单中心(新)',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center_new/index.vue'),
    //       },
    //       // 注单中心(电子)
    //       {
    //         path: 'center_electronic',
    //         name: 'order_center_electronic',
    //         meta: {
    //           annotation: '注单管理-注单中心(电子)',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center_electronic/index.vue'),
    //       },
    //       // 注单中心(操盘)
    //       {
    //         path: 'center_trading',
    //         name: 'center_trading',
    //         meta: {
    //           annotation: '注单管理-注单中心(操盘)',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center_trading/index.vue'),
    //       },
    //       // 合买管理
    //       {
    //         path: 'center_group_buying',
    //         name: 'center_group_buying',
    //         meta: {
    //           annotation: '注单管理-合买管理',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center_group_buying/index.vue'),
    //       },
    //       // 百家赔注单
    //       {
    //         path: 'bet_slip_order_no',
    //         name: 'order_center_bet_no',
    //         meta: {
    //           annotation: '注单管理-百家赔注单',
    //           need_refresh: 99,
    //           developer: 'numbers',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center_bet_no/index.vue'),
    //       },
    //       // 注单中心(ES)
    //       // {
    //       //   path: "center_es",
    //       //   name: "order_center_es",
    //       //   meta: {
    //       //     annotation: "注单管理-注单中心(ES)",
    //       //     need_refresh: 99,
    //       //     developer: 'pasta',
    //       //   },

    //       //   component: () =>
    //       //     import(
    //       //       "project/caopan/pages/order_mange/center_es/index.vue"
    //       //     )
    //       // },
    //       {
    //         path: 'center_es_timely',
    //         name: 'order_center_es_timely',
    //         meta: {
    //           annotation: '注单管理-注单中心(ES)(及时流)',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center_es_timely/index.vue'),
    //       },
    //       // 注单中心(真人)
    //       {
    //         path: 'center_zr',
    //         name: 'order_center_zr',
    //         meta: {
    //           annotation: '注单管理-注单中心(真人)',
    //           need_refresh: 99,
    //           developer: 'nelly',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center_zr/index.vue'),
    //       },
    //       // 注单中心(彩票)
    //       {
    //         path: 'center_cp',
    //         name: 'order_center_cp',
    //         meta: {
    //           annotation: '注单管理-注单中心(彩票)',
    //           need_refresh: 99,
    //           developer: 'nelly',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/center_cp/index.vue'),
    //       },
    //       // 取消注单
    //       {
    //         path: 'cancel',
    //         name: 'order_cancel_',
    //         meta: {
    //           annotation: '注单管理-取消注单',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/cancel/index.vue'),
    //       },
    //       // 取消注单ES
    //       // {
    //       //   path: "cancel_es",
    //       //   name: "order_cancel_es_",
    //       //   meta: {
    //       //     annotation: "注单管理-取消注单es",
    //       //     need_refresh: 99,
    //       //     developer: 'pasta',
    //       //   },

    //       //   component: () =>
    //       //     import(
    //       //       "project/caopan/pages/order_mange/cancel_es/index.vue"
    //       //     )
    //       // },
    //       {
    //         path: 'cancel_es_timely',
    //         name: 'order_cancel_es_timely',
    //         meta: {
    //           annotation: '注单管理-取消注单es(及时流)',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/cancel_es_timely/index.vue'),
    //       },
    //       // betcancel审核
    //       {
    //         path: 'betcancel_check',
    //         name: 'betcancel_check_',
    //         meta: {
    //           annotation: '注单管理-betcancel审核',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },
    //         auth_id: 'betcancel_checkxxx',
    //         component: () => import('project/caopan/pages/order_mange/betcancel_check/index.vue'),
    //       },
    //       // 投注失败
    //       {
    //         path: 'bet_failed',
    //         name: 'bet_failed_',
    //         meta: {
    //           annotation: '注单管理-重试记录',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/bet_failed/index.vue'),
    //       },
    //       //结算日志
    //       {
    //         path: 'log_settlement',
    //         name: 'log_settlement_',
    //         meta: {
    //           annotation: '注单管理-结算日志',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/log_settlement/index.vue'),
    //       },
    //       //进球点注单审核
    //       {
    //         path: 'note_review',
    //         name: 'note_review_',
    //         meta: {
    //           annotation: '注单管理-进球点注单审核',
    //           need_refresh: 99,
    //           developer: 'jamison',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/note_review/index.vue'),
    //       },
    //       //冻结日志
    //       {
    //         path: 'log_freeze',
    //         name: 'log_freeze',
    //         meta: {
    //           annotation: '注单管理-冻结日志',
    //           need_refresh: 99,
    //           developer: 'pasta',
    //         },

    //         component: () => import('project/caopan/pages/order_mange/log_freeze/index.vue'),
    //       },
    //     ],
    //   },

    //   {
    //     path: 'trader',
    //     name: 'trader_container',
    //     component: () => import('project/caopan/pages/trader/index.vue'),
    //     children: [
    //       // {
    //       //   path: "manage", //操盘首页
    //       //   name: "trader_manage",
    //       // annotation:"操盘首页",
    //       //   component: () => import("project/caopan/pages/trader/manage/index.vue")
    //       // },

    //       {
    //         path: 'urgent', //MTS操盘
    //         name: 'urgent',
    //         meta: {
    //           annotation: 'MTS操盘',
    //           need_refresh: 0,
    //         },

    //         component: () => import('project/caopan/pages/trader/mts_urgen_t'),
    //       },
    //       // {
    //       //   path: "trader_edit",
    //       //   name: "trader_edit",
    //       // annotation:"",
    //       //   component: () => import("project/caopan/pages/trader/edit/index.vue")
    //       // },
    //       // // 报表
    //       // {
    //       //   path: "eventreport",
    //       //   name: "trader_event_report",
    //       //annotation:"报表",
    //       //   component: () =>
    //       //     import("project/caopan/pages/trader/report/event_report/index.vue")
    //       // },
    //       // {
    //       //   path: "financereport",
    //       //   name: "trader_finance_report",
    //       //annotation:"",
    //       //   component: () =>
    //       //     import("project/caopan/pages/trader/report/finance_report/index.vue")
    //       // }
    //       // 报表结束
    //     ],
    //   },
      // 排班管理
      {
        path: 'workforce',
        name: 'workforce_management',
        meta: {
          annotation: '排班管理',
        },

        component: () => import('project/caopan/pages/workforce/index.vue'),
        children: [
          // 班次管理
          {
            path: 'working_shift', //MTS操盘
            name: 'working_shift',
            meta: {
              annotation: '班次管理',
              need_refresh: 0,
              developer: 'rank',
            },

            component: () => import('project/caopan/pages/workforce/working_shift/index.vue'),
          },
        ],
      },
      {
        path: 'monitoring', //监控告警
        name: 'monitoring',
        meta: {
          annotation: '监控告警',
        },
        component: () => import('project/caopan/pages/monitoring/index.vue'),
        children: [
          // 数据商断链警告
          {
            path: 'monitoring_alarms',
            name: 'monitoring_alarms',
            meta: {
              annotation: '数据商断链警告',
              need_refresh: 0,
              developer: 'moer',
            },
            component: () => import('project/caopan/pages/monitoring/monitoring_alarms/index.vue'),
          },

          // 自动关盘监控
          {
            path: 'monitoring_close',
            name: 'monitoring_close',
            meta: {
              annotation: '自动关盘监控',
              need_refresh: 0,
              developer: 'moer',
            },
            component: () => import('project/caopan/pages/monitoring/monitoring_close/index.vue'),
          },

          // 早盘自动Rev
          {
            path: 'early_trading',
            name: 'early_trading',
            meta: {
              annotation: '自动关盘监控',
              need_refresh: 0,
              developer: 'moer',
            },
            component: () => import('project/caopan/pages/monitoring/early_trading/index.vue'),
          },
        ],
      },
    //   // 代理配置
    //   {
    //     path: 'agent_configuration',
    //     name: 'agent_configuration',
    //     meta: {
    //       annotation: '代理配置',
    //     },

    //     component: () => import('project/caopan/pages/agent_configuration/index.vue'),
    //     children: [
    //       {
    //         path: 'display_control_template',
    //         name: 'display_control_template',
    //         meta: {
    //           annotation: '显示控制模板',
    //           need_refresh: 0,
    //           developer: 'nelly',
    //         },

    //         component: () => import('project/caopan/pages/agent_configuration/display_control_template/main/index.vue'),
    //       },
    //       {
    //         path: 'user_configuration_list',
    //         name: 'user_configuration_list',
    //         meta: {
    //           annotation: '用户配置列表',
    //           need_refresh: 0,
    //           developer: 'nelly',
    //         },

    //         component: () => import('project/caopan/pages/agent_configuration/user_configuration_list/index.vue'),
    //       },
    //     ],
    //   },
    //   {
    //     path: 'basic', //系统设置
    //     name: 'basic_setting',
    //     meta: {
    //       annotation: '系统设置',
    //       developer: 'rank',
    //     },

    //     component: () => import('project/caopan/pages/basic_setting/basicSetting.vue'),
    //   },
    ],
  },
  // {
  //   path: '/forecast_snapshot_detail', // forcast快照详情界面
  //   name: 'forecast_snapshot_detail',
  //   meta: {
  //     annotation: 'Forecast快照详情列表',
  //     need_refresh: 0,
  //   },

  //   component: () => import('project/caopan/pages/operation_set/forcast_snapshot_detail/index.vue'),
  // },
  // {
  //   path: "test_page", //测试页面
  //   name: "test_page",
  //   meta: {
  //     annotation: "测试页面",
  //     developer: 'jinnian',
  //   },

  //   component: () =>
  //     import(
  //       "project/caopan/pages/test_page/index.vue"
  //     )
  // },
  //风控管理/风控措施/限额管理/商户模板配置子模版
  // {
  //   path: '/subtemplate',
  //   name: 'subtemplate',
  //   meta: {
  //     annotation: '商户模板配置子模版',
  //     need_refresh: 1,
  //     developer: 'malick',
  //   },
  //   component: () => import('project/caopan/pages/trader/resource/component/single_stencil/component/merchant_template.vue'),
  // },
  // {
  //   path: '/dictionary',
  //   name: 'dictionary',
  //   meta: {
  //     annotation: '指标字典',
  //     need_refresh: 1,
  //     developer: 'moer',
  //   },
  //   component: () =>
  //     import('project/caopan/pages/trader/resource/component/single_stencil/component/module/pointer_dictionary/pointer_dictionary.vue'),
  // },
];

// Always leave this as last one
// if (process.env.MODE !== "ssr") {
//   routes.push({
//     path: "*",
//     component: () => import("pages/error/Error404.vue")
//   });
// }

export default routes;
