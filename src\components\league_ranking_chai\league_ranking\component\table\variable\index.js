import { ref } from "vue";
import { table_columns_config } from "src/components/league_ranking/config/columns.js";
export const create_base_state_instance_fn = (payload) => {
    //加载状态
    const tabledata_loading = ref(false);
    //列表数据
    const data_list = ref([]);
    //params参数
    const params = ref({
        sportId: 1,
        tournamentLevel: 1,
        start: 1,
        size: 200,
        id: null,
        versionNewStatus: "",
        tournamentName: "",
    });
    const columns = ref(table_columns_config);
    //table_hover提示
    const table_hover = ref(false);
    //修改排序值
    const is_modifying_playrules_order = ref(false);
    // 当前点击row的id
    const row_click_id = ref(0);
    return {
        tabledata_loading,
        data_list,
        params,
        columns,
        table_hover,
        is_modifying_playrules_order
    }
}