import { api_operate } from "src/api/index.js";
// 确定
export const Sure_textanda = (payload) => {
  const { sta_switch, visible, ts } = payload;
  if (sta_switch.value == sta_switch.value) {
    visible.value = false;
    console.log(sta_switch.value, "sta_switch.value");
    ts.value = sta_switch.value;
    let param = {
      ts: ts.value,
    };
    api_operate.getSetTourSort(param).then((res) => {
      console.log(res, "00000000000000000");
    });
  }
  console.log(sta_switch.value);
  console.log(visible.value, "确定");
};
