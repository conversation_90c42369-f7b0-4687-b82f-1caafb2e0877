<template>
    <div class="table-f p-pr" ref="table">
        <table-no-data ref="table-no-data" :width="`100%`" :tabledata_loading="tabledata_loading"
            :table_no_data_visable="data_list.length == 0"
            :style="{ height: '100%', width: '100%', left: 0, top: '36px' }"
            :loading_style="{ height: '100%', width: '100%' }" style="width: 100%"></table-no-data>
        <!-- 列表展示 -->
        <!-- 高尔夫，赛车运动，板球，飞镖，趣味，娱乐，其他，球种 去掉拖拽排序 -->
        <i-table :columns="[28, 33, 37, 38, 50, 18, 40].includes(params.sportId)
            ? columnsChampion
            : columns
            " :data="data_list" :height="window_size_info.height - 117" row-key="id" :row-class-name="row_class_name"
            @on-row-click="row_click" :disabled-hover="table_hover" class="ivew-table-custom">
            <!-- <template slot-scope="{ row, index }" #sort> -->
            <template #sort="{ row, index }">
                <div class="p-pr">
                    <span v-show="!is_modifying_playrules_order && !row.show_input"
                        class="lh24x d-inline-block RobotoBold1">
                        {{ row.sort }}</span>
                    <!-- 序号 -->
                    <i-input v-model="row.orderNo_10" placeholder="" style="width: 88px" class="index-set"
                        v-show="is_modifying_playrules_order" type="number" @on-enter="set_sort_beg(row, index)"
                        @on-blur="set_sort_beg(row, index)" />
                    <i-input v-model="row.orderNo_10" placeholder="" style="width: 88px" class="index-set"
                        v-if="row.show_input" type="number" @on-enter="set_sort_beg(row, index)"
                        @on-blur="set_sort_beg(row, index)" />
                </div>
            </template>
            <!--  联赛名称-->
            <template #tournamentName="{ row }">
                <table-cell-ellipsis :str_all="row.tournamentName"
                    :tooltip_position="{ anchor: 'bottom left', self: 'top left' }" :offset="[-10, -10]"
                    class="RobotoBold1" :cursor="false"></table-cell-ellipsis>
            </template>
            <!-- 联赛英文名称 -->
            <template #tournamentEname="{ row }">
                <table-cell-ellipsis :str_all="row.tournamentEname" :offset="[-10, -10]"
                    :tooltip_position="{ anchor: 'bottom left', self: 'top left' }" class="RobotoBold1"
                    :cursor="false"></table-cell-ellipsis>
            </template>
            <template #web_show="{ row, index }">
                <div class="row panda-text-blue justify-center">
                    <!-- 新手版展示 -->
                    <i-switch v-if="btn_permissions('league_ranking_newbie_switch:view')"
                        v-model="data_list[index].versionNewStatus" :loading="row.swith_loading" size="small"
                        @on-change="swith_change(row, index)" :disabled="!btn_permissions('league_ranking_newbie_switch:edit')
                            " />
                </div>
            </template>
            <!-- <template slot-scope="{ row, index }" #data5> -->
            <template #data5="{ row, index }">
                <div class="row panda-text-blue justify-center">
                    <template v-if="![28, 33, 37, 38, 50, 18, 40].includes(params.sportId)">
                        <!-- 通用参数设置 -->
                        <span class="operation-icon" v-if="btn_permissions('league_ranking_newbie_sort:edit')">
                            <span class="panda_manage1 vamd" @click="table_sort(row, index, 1)"
                                v-show="row.sort != 1"></span>
                            <span class="panda_manage3 vamd" @click="table_sort(row, index, 2)"
                                v-show="row.sort != total"></span>
                            <span class="panda_manage4 vamd" @click="table_sort(row, index, 3)"
                                v-show="row.sort != 1"></span>
                        </span>
                        <!-- 滚球置顶 -->
                        <!-- {{ params.sportId }} -->
                        <span v-if="
                            params.sportId === 1 &&
                            btn_permissions('league_ranking_newbie_pinTop:edit')
                        " class="drag-srot cp mr15x" @click="top_morning(row, 1)">{{ i18n_t("menus.lm_10.children8_2")
                            }}</span>
                        <!-- 早盘置顶 -->
                        <span v-if="
                            params.sportId === 1 &&
                            btn_permissions('league_newbie_pinTop:edit')
                        " class="drag-srot cp mr15x" @click="top_morning(row, 2)">{{ i18n_t("menus.lm_10.children8_1")
                            }}</span>
                        <!-- 拖拽排序 -->
                        <span class="drag-srot cp mr15x" @mousedown="click_back(row)"
                            v-if="btn_permissions('league_ranking_newbie_sort:edit')">{{
                                i18n_t("operations.league_ranking.c_5") }}
                        </span>
                    </template>
                    <!-- 跨页排序 -->
                    <template v-if="!row.show_input">
                        <span class="drag-srot cp" @click="cross_page_sort(row, index)"
                            v-if="btn_permissions('league_ranking_newbie_sort:edit')">{{
                                i18n_t("operations.league_ranking.c_6") }}
                        </span>
                    </template>
                    <template v-if="row.show_input">
                        <!-- 保存排序值 -->
                        <span class="panda-btn-light-dense q-mr-sm text-panda-text-white mr15x"
                            @click.stop="submit_set_table_sort(row, index)" style="height: 22px">
                            {{ i18n_t("common.save") }}
                        </span>
                        <!-- 取消 -->
                        <span class="panda-btn-dark-dense q-mr-sm" @click="abort_modify_playrules_order()"
                            style="height: 22px; padding: 0 8px">{{ i18n_t("playManagement.cancel") }}</span>
                    </template>
                </div>
            </template>
        </i-table>
        <!-- 分页 -->
        <i-page :total="total" show-elevator show-sizer show-total :page-size-opts="[10, 20, 50, 100, 200, 500]"
            :page-size="params.size" :current="params.start" @on-change="change_page_num"
            @on-page-size-change="change_page_size" class="row items-center justify-center bottom-page" />
    </div>

</template>
<script setup>
import { columnsChampion } from "src/components/league_ranking/config/columns.js"; //列表展示columns
import { window_size_info } from "src/output/common/store-common.js";
import tableCellEllipsis from "project/caopan/components/table/table_cell_ellipsis/index.vue"; //提示框
import { src_components_league_ranking_component_table_componsable_fn } from "src/components/league_ranking/component/table/componsable/index.js";
import { src_components_league_ranking_component_table_variable_componsable_fn } from "src/components/league_ranking/component/table/componsable/variable.js";
import { src_components_league_ranking_variable_componsable_fn } from "src/components/league_ranking/componsable/variable.js";
const league_ranking_index_payload = src_components_league_ranking_variable_componsable_fn();

const base_payload = src_components_league_ranking_component_table_variable_componsable_fn();

const props = defineProps({
  data_list: {
    type: Array,
    default: [],
  },
});
const {
    //修改排序值
    is_modifying_playrules_order,
    columns,
    //加载状态
    tabledata_loading,
    //params参数
    params,
    //列表数量
    total,
    //table_hover提示
    table_hover,
    row_click_id
} = base_payload;

const {
    click_back,
    row_click,
    table_sort,
    submit_set_table_sort,
    cross_page_sort,
    set_sort_beg,
    abort_modify_playrules_order,
    row_class_name,
    change_page_num,
    change_page_size,
    swith_change,
    top_morning,
} = src_components_league_ranking_component_table_componsable_fn({ ...base_payload });

</script>
<style lang="scss" scoped>
@import url(src/components/league_ranking/css/index.scss);
</style>