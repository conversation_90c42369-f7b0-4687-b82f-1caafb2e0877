export const emit_generator = payload => {
  const { emit, is_edit_sort } = payload;
  const emit_edit_sort_status = () => {
    emit('edit_sort_status', { status: is_edit_sort.value });
  };

  const emit_sort_save = () => {
    emit('sort_save');
  };
  const emit_query_period_change = val => {
    emit('query_period_change', val);
  };
  const emit_icon_click = () => {
    emit('icon_click');
  };

  const emit_show_more_query_change = value => {
    emit('show_more_query_change', value);
  };
  return {
    emit_edit_sort_status,
    emit_sort_save,
    emit_query_period_change,
    emit_icon_click,
    emit_show_more_query_change,
  };
};
