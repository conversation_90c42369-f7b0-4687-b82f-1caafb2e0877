<!--
 * @FilePath: /src/components/query/select_query_period4.vue
 * @Description: 预开售-头部日期选择器
-->
<template>
  <div
    :class="
      `row bg-panda-base-title-light line-height-32px items-center full-width p-pl--sm ${
        has_border ? 'border-bottom-light' : ''
      }`
    "
    style="height:33px;"
  >
    <div v-if="left_btn">
      <span class="dib p1-df p1-aic p1-jcc mr10x">
        <span
          class="panda-left-info-toogle-button"
          :class="show_more_query ? 'active' : ''"
          @click="show_more_query = !show_more_query"
        >
        <q-tooltip >
          <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
          {{show_more_query?i18n_t('common.hideLeft'):i18n_t('common.displayLeft')}}
        </q-tooltip>
          <span class="panda-left-info-toogle-button-inner"></span>
        </span>
      </span>
    </div>
    <div
      v-for="(items, indexs) in query_form_date_arr"
      :key="`query_form_date_${indexs}`"
      @click="query_form_date_tab_click(items.value)"
      :class="
        selected_date_tab == items.value ? 'bg-panda-query-date-active' : ''
      "
      class="float-left text-panda-text-dark text-center cursor-pointer"
    >
      <span
        class="border-radius-2px panda-query-date-tab-label mr-10 panda-border-grey"
        :class="
          selected_date_tab == items.value
            ? 'text-panda-date-base'
            : 'text-panda-date-light'
        "
      >{{ items.label }}</span>
    </div>
    <div v-if="other_time_model" class="p1-mr--1">
      <iDatePicker
        type="date"
        @on-change="query_form_date_tab_click(7)"
        v-model="other_date"
        :options="otherDisabledDate"
        format="yyyy-MM-dd"
        placement="bottom-end"
        :placeholder="i18n_t('selectPeriodTab.Tab_6')"
        style="width: 110px;"
      ></iDatePicker>
      <!-- 其他早盘 -->
    </div>
    <div
      v-if="show_history&&!is_editing"
      :class="selected_date_tab == 8 ? 'bg-panda-query-date-active' : ''"
      class="float-left text-panda-text-dark text-center cursor-pointer line-height-24px panda-query-date-tab relative-position"
    >
      <template v-if="false">
        <span
          @click="query_form_date_tab_click(8)"
          class="panda-py-2px panda-px-10px border-radius-2px panda-query-date-tab-label"
          :class="
            selected_date_tab == 8
              ? 'text-panda-date-light'
              : 'text-panda-date-base'
          "
        >{{i18n_t('selectPeriodTab.Tab_11')}}</span>
        <!-- 历史赛程 -->
        <!-- z-index需要覆盖无数据z-index -->
        <div
          v-show="selected_date_tab == 8 && show_q_date_component"
          class="absolute bg-panda-base-dark border-radius-4px q-pa-md panda-border"
          style="top:32px; height:140px;z-index:11;box-shadow: 0 2px 14px 0 rgba(0,0,0,0.50);border-radius: 4px; padding:20px;"
        >
          <!-- 时间日期组件 -->
          <q-date
            v-model="history_date"
            @input="handle_history_date_confirm"
            mask="YYYY-MM-DD HH:mm:ss"
            color="panda-primary"
            text-color="panda-text-light"
            :options="startTime_limit"
            dark
          />
        </div>
      </template>
      <template v-else>
        <iDatePicker
          type="date"
          @on-change="query_form_date_tab_click(8)"
          v-model="history_date"
          :options="disabledDate"
          format="yyyy-MM-dd"
          placement="bottom-end"
          :placeholder="i18n_t('selectPeriodTab.Tab_11')"
          style="width: 110px;"
        ></iDatePicker>
      </template>
      
    </div>
    <!-- 动画下架红点数 -->
    <template v-if="animationoOfflineIds.length>0&&isHistoryFlag==0">
      <div style="display: flex;position: relative;margin-left:5px;" >
        <img style="width:24px;height:24px;cursor:pointer" @click="animationoOfflineClick(true)" src="/assets/icon/warning.svg" >
        <div class="red-back position-absolute  text-center text-panda-text-light" >
          {{ animationoOfflineIds.length<=99? animationoOfflineIds.length:"99+"}}
        </div>
        <div class="position-absolute text-center text-panda-text-light" style="cursor:pointer;width:110px;" v-if="isFilter" @click="animationoOfflineClick(false)">
          {{i18n_t('saleTable.sa_249')}}
        </div>
      </div>
  </template>
    <div v-if="icon_visible" class="p-ml--md p-df p-aic p-icon-bg" @click="icon_click">
      <span
        class="panda_icon_alert p-icon-position"
        style="font-size:14px"
        :class="icon_count ? 'p-icon-tc--orange' : ''"
      ></span>
      <span v-if="icon_count" class="period__icon-badge">{{ icon_count }}</span>
    </div>
     <span v-if="is_export" class="panda-btn-light-dense "
                @click="$emit('export_data')"
                style="width: 64px;margin: 0 10px;"
                >{{i18n_t('saicheng.common.export_1')}}</span
              >
    <q-space />
    <div
      class="float-right text-panda-text-dark self-end text-right position-relative"
      style="width:121px;height: px;"
    >
      <!--v-if  有改动-->
      <!-- <span
        @click="show_more_query = !show_more_query"
        v-if="show_show_more_query_btn"
        class="panda_show_more p-mr--xs"
        :class="show_more_query ? 'panda-text-primary' : ''"
        style="    font-size:12px;       height: 13px;    vertical-align: middle   "
      >
        <span>{{ show_more_query ? "收起" : "更多" }}筛选</span>
        <span
          class="panda_icon_gdsx p-ml--xxxxs"
          style="font-size: 12px"
          :class="show_more_query ? 'panda-icon-active' : ''"
        ></span>
      </span>-->
      <span class="dib" v-if="!right_bnt">
        <span
          :class="showrightdetail == 1 ? 'active' : ''"
          style=" display: inline-block;     margin-right: 8px; vertical-align: middle;       line-height: 13px; "
          class="panda-right-info-toogle-button"
          @click="toggle_showrightdetail"
        >
          <q-tooltip>{{ rightdetail_tooltip }}</q-tooltip>
          <span class="panda-right-info-toogle-button-inner"></span>
        </span>
      </span>
    </div>
  </div>
</template>

<script setup> 

import { project_caopan_components_query_select_query_period4_componsable_fn } from "project/caopan/components/query/select_query_period4/componsable/index.js";
 import * as  base_data from "project/saicheng/pages/animation_controller/components/animation_config/variable/index.js"
 import { toggle_showrightdetail } from "project/caopan/componsable/layout/rightinfo/module/index.js";
 const {
  otherDisabledDate,
startTime_limit,
disabledDate,

 } = base_data
// mixins: [
//     alertdialogmixin,
//     rightinfomixin,
//     formartmixin,
//     routerCache_handler
//     // showrightdetail_getter
//   ],
import {
  get_serverTime,
  get_history_startTimeFrom,
  set_breadcrumbsPart3,
  set_history_startTimeFrom
} from "src/output/common/store-common.js"

const props = defineProps({
    right_bnt: {
      type: Boolean,
      default: false
    },
    default_history_data: [Number, String, Object],
    prop_history_date: "",
    default_select: {
      type: Number,
      default: 0
    },
    has_border: {
      type: Boolean,
      default: false
    },
    more_query: {
      type: Boolean,
      default: false
    },
    icon_visible: {
      type: Boolean,
      default: false
    },
    icon_count: {
      type: Number,
      default: 0
    },
    show_show_more_query_btn: true,
    show_live_odd: {
      type: Boolean,
      default: false
    },
    show_history: {
      type: Boolean,
      default: true
    }, // 显示历史赛程
    show_yesterday: {
      type: Boolean,
      default: false
    },
    left_btn: {
      type: Boolean,
      default: true
    },
    other_time_model: {
      type: Boolean,
      default: true
    },
    is_editing: {
      type: Boolean,
      default: false
    },
    is_export:{
        type: Boolean,
        default:false
    },
    isHistoryFlag:{
        type: Number,
        default:0
    },
    animationoOfflineIds:{
      type:Array,
      default:()=>[]
    },
  })

  const {
    handle_history_date_confirm,
icon_click,
toggle_showrightdetail,

} = project_caopan_components_query_select_query_period4_componsable_fn({
  ...base_data,
  });

</script>
<style lang="scss" scoped>
 @import url(project/caopan/components/query/select_query_period4/css/index-scoped.scss);
</style>