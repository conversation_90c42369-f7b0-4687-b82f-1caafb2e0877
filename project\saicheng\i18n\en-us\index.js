/*
 * <AUTHOR> da<PERSON><PERSON>
 * @Description    : 国际化  英文
 * @FilePath: /src/i18n-saicheng/en-us/index.js
 */
export default {
  "common": {
    "all": "All",
    "league_link": "Official Site",
    "export_1": "Export",
    "return": "Go Back",
    "cancel": "Cancel",
    "confirm": "Confirm",
    "close": "Close",
    "clear": "Clear",
    "add": "Add",
    "change": "Change",
    "select": "Select",
    "displayLeft": "Display left side information",
    "displayRight": "Display right side information",
    "hideLeft": "Hide information on the left",
    "hideRight": "Hide information on the right",
    "league": "League",
    "cup": "Cup",
    "date": "Date",
    "save": "Save",
    "edit": "Edit",
    "yes": "Yes",
    "no": "No",
    "none": "Unrated",
    "none1": "none",
    "level": "Lv",
    "mon": "Mon",
    "tue": "Tue",
    "wed": "Wed",
    "thu": "Thu",
    "fri": "Fri",
    "sat": "Sat",
    "sun": "Sun",
    "year": "-",
    "month": "-",
    "day": "",
    "white": "White Background",
    "black": "Black Background",
    "loginOut": "Login Out",
    "noDataText": "Sorry! do not found the result you want",
    "dataSourceCode_QT": "QT",
    "dataSourceCode_SB": "SB",
    "dataSourceCode_188": "188",
    "dataSourceCode_SBO": "SBO",
    "dataSourceCode_TS": "TS",
    "leagueArea": "Please select a region",
    "Keyword_search": "Keyword Search",
    "qsr_playField": "Input Play Field",
    "home": "Home",
    "away": "Away",
    "other": "Other",
    "remark": "Remark",
    "total": "Total",
    "refresh": "Refresh",
    "noMatchingData": "No Matching Data",
    "exportData": "Export",
    "search": "Search",
    "loading": "Loading",
    "waiting": "Waiting",
    "copy": "Copy",
    "score": "Score",
    "booked": "booked,data correct",
    "booked_no": "booked,no data",
    "more": "More",
    "summary": "Summary",
    "current_page": "Current Page",
    "settled": "Settled",
    "unsettled": "Unsettled",
    "full_time": "Full Time",
    "league_relationship": "league relationship",
    "over_time": "OT",
    "penalty": "Pen",
    "succeed": "succeed",
    "save_succeed": "Save Succeed",
    "fail": "fail",
    "save_succeed": "Save Succeed",
    "input_file": "incorrect file format",
    "Tokyo_2020_Olympic_Games": "Tokyo 2020 Olympic Games",
    "Tokyo_2024_Olympic_Games": "Paris 2024 Olympic Games",
    "fan": "R",
    "to_caopan": "Please check in Trading & Risk Management System",
    "new_add": "新增区域",
    "wait_match": "Pending",
    "matched": "Completed",
    "enable": "Enable",
    "disable": "Disable",
    "unrelated": "Unlinked",
    "related": "Linked",
    "third_info": "Data Info",
    "related_info": "Linked Info",
    "eliminate": "Knockout",
    "club_team_flag": "Is Club",
    "team_settings": "Team Setup",
    "season_id": "Season Id",
    "team_news": "Team Info",
    "league_news": "League Info",
    "currently_total": "Currently",
    "teams": "teams",
    "team_info": "Team Info",
  },
  "menus": {
    "lm_0": "Fixture",
    "lm_1": {
      "title": "Fixture MGT",
      "title_bfw": "League MGT"
    },
    "lm_2": {
      "title": "PreSale"
    },
    "lm_3": {
      "title": "Event Ver"
    },
    "lm_4": {
      "title": "Metadata",
      "children1": "League Lib",
      "children2": "Team Lib",
      "children3": "Player Lib",
      "children4": "Olympic Special Topics",
    },
    "lm_5": {
      "title": "Reporting"
    },
    "lm_6": {
      "title": "Ver MGT"
    },
    "lm_7": {
      "title": "Access Control"
    },
    "lm_8": {
      "title": "OpLog"
    },
    "lm_9": {
      "title": "Config",
      "children1": "Hot Word"
    },
    "lm_10": {
      "title": "Vsports",
      "children1": "League Lib",
      "children2": "Team Lib"
    },
    "lm_11": {
      "title": "Esports",
      "children1": "League Lib",
      "children2": "Team Lib"
    },
    "lm_12": {
      "title": "Sale",
      "children1": "Pre-Match",
      "children2": "In-play Matches",
      "children3": "Match Settings",
    },
    "lm_13": {
      "title": "History schedule",
      "children1": "History schedule",
    },
    "lm_14": {
      "title": "Champion",
    },
    "lm_15": {
      "title": "Operation",
      "children1": "Hot Matches",
      "children2": "League Sort",
      "children3": "Match Sort",
      "olympic_games": "Olympic Special Topics",
    },
  },
  "sports": {
    "sports_1": "Soccer",
    "sports_2": "Basketball",
    "sports_3": "Baseball",
    "sports_4": "Ice hockey",
    "sports_5": "Tennis",
    "sports_6": "American Football",
    "sports_7": "Snooker",
    "sports_8": "Table tennis",
    "sports_9": "Volleyball",
    "sports_10": "Badminton",
    "sports_11": "Handball",
    "sports_12": "Boxing/Fighting",
    "sports_13": "Beach Volleyball",
    "sports_14": "Rugby Union",
    "sports_15": "Hockey",
    "sports_16": "Water Polo",
    "sports_17": "Athletics",
    "sports_18": "ENT",
    "sports_19": "Swimming",
    "sports_20": "Gymnastics",
    "sports_21": "Diving",
    "sports_22": "Shooting",
    "sports_23": "Weightlifting",
    "sports_24": "Archery",
    "sports_25": "Fencing",
    "sports_26": "Curling",
    "sports_27": "Taekwondo",
    "sports_28": "Golf",
    "sports_29": "Cycling",
    "sports_30": "Horse Racing",
    "sports_31": "Sailing",
    "sports_32": "Rowing",
    "sports_33": "Motorsport",
    "sports_34": "Judo",
    "sports_35": "karate",
    "sports_36": "Wrestling",
    "sports_37": "Cricket",
    "sports_38": "Darts",
    "sports_39": "Beach Football",
    "sports_40": "Others",
    "sports_50": "Fun",
    "sports_1001": "Virtual soccer",
    "sports_1002": "Dog race",
    "sports_1004": "Virtual basketball",
    "sports_1007": "Mud racing",
    "sports_1008": "Kart",
    "sports_1009": "Dirt motorcycle",
    "sports_1010": "motorcycle",
    "sports_1011": "Horse race",
    "sports_1012": "Virtual chariot",
    "sports_100": "LOL",
    "sports_101": "Dota2",
  },
  "match_status": {
    "status_1": "All",
    "status_2": "Not Started",
    "status_3": "InPlay",
    "status_4": "Suspension",
    "status_5": "Ended",
    "status_6": "Closed",
    "status_7": "Canceled",
    "status_8": "No data",
    "status_9": "Delay",
    "status_10": "Unknown",
    "status_11": "Postponed",
    "status_12": "Interrupted",
    "status_13": "Abandoned",
    "status_14": "online"
  },
  "manage_level": {
    "level_0": "League level show",
    "level_1": "Event level show",
  },
  "match_status1": {
    "status_0": "Not Started",
    "status_1": "InPlay",
    "status_2": "Suspension",
    "status_3": "Ended",
    "status_4": "Closed",
    "status_5": "Canceled",
    "status_6": "No data",
    "status_7": "Delay",
    "status_8": "Unknown",
    "status_9": "Postponed",
    "status_10": "Interrupted",
    "status_11": "Abandoned"
  },
  "manage_operation": {
    "operation1": "Match Creation",
    "operation2": "Match Link",
    "operation3": "Match Delink",
    "operation4": "Directly Match Creation",
    "operation5": "Selected Match Creation",
    "operation6": "Close",
    "operation7": "Reset",
    "operation8": "Match Management",
    "operation9": "Move Into PreSale",
    "operation10": "Need Close Hadicap",
    "operation11": "Match Details",
    "operation12": "Trading Management",
    "operation13": "Mark Opp",
    "operation14": "Cancel Opp",
    "operation15": "Result",
    "operation16": "Match Delink",
    "operation17": "Match & Team Delink",
    "operation18": "Mismatched League",
    "operation19": "Mismatched Home Team",
    "operation20": "Mismatched Away Team",
    "operation21": "Match Creation for Selected",
    "operation22": "Match Creation for Selected",
    "operation23": "Close",
    "operation24": "Reset",
    "operation25": "Match Management",
    "operation26": "Move Into PreSale",
    "operation27": "Need Close Hadicap",
    "operation28": "1 Selected",
    "operation29": "Link",
    "operation30": "A standard match has been generated, please select the team to be unlinked",
    "operation31": "This match that you want to create, is a duplicate of a match that's already been created. Are you sure you want to continue.",
    "operation40": "Mismatched Team",
    "operation41": "Data Provider Home and Away Team Opposite!",
    "operation42": "Event distribution",
    "operation43": "Cancel distribution",
    "operation44": "Cancel batch distribution",
    "operation45": "Batch distribution",
    "operation46": "League management",
    "operation47": "Cancel mapping",
  },
  "manage_alert": {
    "alert0": "Success",
    "alert1": "Failed",
    "alert2": "Deal",
    "alert3": "Please set the league area",
    "alert4": "Matchs",
    "alert5": "Please tick the team that needs to be unlinked",
    "alert6": "Match Stage",
    "alert7": "Generate standard matches will associate leagues and teams, and determine to continue to generate standard matches",
    "alert8": "The team has related affected events and cannot be disassociated temporarily. Check whether to check the affected events.",
    "alert9": "The league association of this schedule is inconsistent with the existing association relationship of the league library.  Whether to modify the league",
    "alert10": "Linked successfully",
    "alert11": "Linked failed",
    "alert12": "Merged successfully",
    "alert13": "Merge failed",
    "alert14": "Successfully unlinked",
    "alert15": "Failed to unlink",
    "alert16": "Successfully Sold",
    "alert17": "Failed Sold",
    "alert18": "The match linked needs to be accurate. If the link is incorrect, the match will not sold normally.",
    "alert19": "Are you sure to continue",
    "alert20": "League merging needs to be accurate. The wrong merger will affect the normal saling of the league.",
    "alert21": "The third-party leagues (or teams) linked/merged in this match belong to different standard leagues (or teams).",
    "alert22": "Whether to do",
    "alert23": "Make sure to unlink",
    "alert24": "You have chosen to cancel the operation",
    "alert25": "Affected matchs",
    "alert27": "It is not possible to check two matchs from the same data source",
    "alertNotBE": "BE source not check。",
    "alert28": "The team association of this schedule is inconsistent with the existing association relationship of the team library.  Whether to modify the team",
    "alert29": "Recommend Link is empty",
    "alert30": "Linked Successfully",
    "alert31": "League area cannot be null",
    "alert32": "The Chinese name of the league cannot be null",
    "alert33": "The English name of the league cannot be null",
    "alert34": "Compressed image exceeds 10KB cannot be uploaded, please re-upload again",
    "alert35": "Image size cannot exceeds 150x150",
    "alert36": "Image exceeds 1M cannot be uploaded, please re-upload again",
    "alert37": "Home away swapping / cancel swapping will affecting the change of match / odds / event / result, please confirm whether want to continue!",
    "alert38": "Swapping will affecting the change of 3rd party provider of odds / event / result, please confirm whether want to continue!",
    "alert39": "Cancel swapping will affecting the change of 3rd party provider of odds / event / result, please confirm whether want to continue!",
    "alert40": "Team Name in Chinese cannot be blank!",
    "alert41": "Team Name in English cannot be blank!",
    "alert42": "Two standard match are marked against each other, association merging is not allowed",
    "alert43": "The competition format cannot be empty!",
    "alert44": "The event association must be accurate. Incorrect associations may prevent the event from starting properly.",
    "alert45": "Please confirm if you wish to continue.",
    "alert46": "This event generates a standard event in the schedule background. Please confirm whether to issue it! After issuing it, you need to manually associate the mapping relationship in the schedule background.",
    "alert47": "Confirm whether to issue the event!",
    "alert48": "Please confirm that no schedule has been generated before deleting the season. Deleting it by mistake may prevent events from starting properly.",
    "alert49": "Please confirm whether to unlink this team!",
    "alert50": "Cancel distribution? Once canceled, it cannot be distributed again!",
    "alert51": "Reason for cancellation",
    "alert52": "Please confirm whether to cancel the mapping",
    "alert53": "Please confirm if you want to unlink this team",
    "alert55": "League {language} name cannot be empty",

  },
  "score_center": {
    "text_1": "SR",
    "text_2": "BC",
    "text_3": "BG",
    "text_4": "TX",
    "text_5": "RB",
    "text_6": "No Data",
    "text_7": "No Data",
    "text_8": "Cancled",
    "text_9": "OT",
    "text_10": "PT",
    "text_11": "Match",
    "text_12": "Game Score",
    "text_13": "Score",
    "text_14": "Games",
    "text_15": "Sets",
    "text_16": "Best of",
    "sportId_1_1": "Corner",
    "sportId_1_2": "Red card",
    "sportId_1_3": "Yellow card",
    "sportId_1_4": "Goal",
    "sportId_1_5": "Attack",
    "sportId_1_6": "Dangerous Attack",
    "sportId_1_7": "Possession",
    "sportId_1_8": "Offside",
    "sportId_1_9": "On Target",
    "sportId_1_10": "Off Target",
    "sportId_1_11": "Substitution",
    "sportId_1_12": "Penalty",
    "sportId_1_13": "Free Kick",
    "sportId_2_1": "Score",
    "sportId_2_2": "Rebound",
    "sportId_2_3": "2 Pts",
    "sportId_2_4": "3 Pts",
    "sportId_2_5": "Fouls",
    "sportId_2_6": "Stats",
    "sportId_3_1": "Pitches",
    "sportId_3_2": "Strikes",
    "sportId_3_3": "Walks",
    "sportId_3_4": "Balls",
    "sportId_3_5": "HBP",
    "sportId_3_6": "Hits",
    "sportId_3_7": "Singles",
    "sportId_3_8": "Doubles",
    "sportId_3_9": "Triples",
    "sportId_3_10": "Homers",
    "sportId_3_11": "Runs",
    "sportId_3_12": "Batters",
    "sportId_3_13": "Left On Base",
    "sportId_3_14": "On Base",
    "sportId_3_15": "Runs",
    "sportId_3_16": "Hits",
    "sportId_3_17": "Errors",
    "sportId_3_18": "Strike",
    "sportId_3_19": "Ball",
    "sportId_3_20": "Out",
    "sportId_3_21": "Substitution",
    "sportId_4_1": "Score",
    "sportId_4_2": "Shot Score",
    "sportId_4_3": "Major Score",
    "sportId_4_4": "Minor Score",
    "sportId_4_5": "Power play",
    "sportId_4_6": "Short-Handed",
    "sportId_4_7": "Goal",
    "sportId_4_8": "Canceled goal ",
    "sportId_4_9": "Penalty Stop",
    "sportId_5_1": "Sets Score",
    "sportId_5_2": "Games Score",
    "sportId_5_3": "Game Score",
    "sportId_5_4": "Serves scored",
    "sportId_5_5": "Failed serve",
    "sportId_5_6": "Breaks",
    "sportId_5_7": "Break Point",
    "sportId_5_8": "Break Rate",
    "sportId_5_9": "Guarantee",
    "sportId_5_10": "Score",
    "sportId_5_11": "Double fault",
    "sportId_5_14": "ACE",
    "sportId_5_15": "break",
    "sportId_6_1": "Score",
    "sportId_6_2": "Rushs",
    "sportId_6_3": "Field goals",
    "sportId_6_4": "Attacks",
    "sportId_6_5": "Touchdowns",
    "sportId_6_6": "Touchdown",
    "sportId_6_7": "Extra point",
    "sportId_6_8": "Safety",
    "sportId_6_9": "2 point conversion",
    "sportId_6_10": "Field goal",
    "sportId_6_11": "Penalty",
    "sportId_6_12": "Challenge",
    "sportId_7_1": "Sets Score",
    "sportId_7_2": "Games Score",
    "sportId_7_3": "Fouls",
    "sportId_7_4": "Highest break",
    "sportId_7_5": "Current break",
    "sportId_7_6": "Score",
    "sportId_7_7": "Foul",
    "sportId_8_1": "Sets Score",
    "sportId_8_2": "Games Score",
    "sportId_8_3": "Red Card",
    "sportId_8_4": "Yellow Card",
    "sportId_9_1": "Score",
    "sportId_9_2": "Games Score",
    "sportId_9_3": "Serve Score",
    "sportId_9_4": "Serving errors",
    "sportId_10_1": "Sets Score",
    "sportId_10_2": "Games Score",
    "sportId_10_3": "Numbers of Score",
    "sportId_10_4": "Receiving points times",
    "sportId_10_5": "Serves scored",
    "sportId_10_6": "Black Card"
  },
  "meatdata_operation": {
    "operation1": "Reset",
    "operation2": "League Linking",
    "operation3": "Remove Linking",
    "operation4": "League ID Creation",
    "operation5": "Generate Team ID",
    "operation6": "Player ID Creation",
    "operation7": "Edit",
    "operation8": "Link Team",
    "operation9": "Player Linking",
    "operation10": "Setted",
    "operation11": "Set as main",
    "operation12": "The main of the league is set up",
    "operation13": "Please set the main league first",
    "operation14": "League affiliation",
    "operation15": "Select a team",
  },
  "matchPeriodId": {
    "id_1_OT": "Overtime 1H",
    "id_1_OT1": "Overtime 2H",
    "id_1_": "All",
    "id_1_0": "Not Started",
    "id_1_6": "1H",
    "id_1_7": "2H",
    "id_1_31": "HT",
    "id_1_32": "Awaiting OT",
    "id_1_100": "Full Time End",
    "id_1_41": "Overtime 1H",
    "id_1_33": "OT-1H End",
    "id_1_42": "Overtime 2H",
    "id_1_110": "Overtime End",
    "id_1_34": "Awaiting PT",
    "id_1_50": "Penalty Shootout",
    "id_1_120": "Penalty Ended",
    "id_1_80": "Interrupted",
    "id_1_90": "Abandoned",
    "id_1_999": "Ended",
    "id_2_": "All",
    "id_2_0": "Not started",
    "id_2_13": "Q1",
    "id_2_301": "End Q1",
    "id_2_14": "Q2",
    "id_2_302": "End Q2",
    "id_2_15": "Q3",
    "id_2_303": "End Q3",
    "id_2_16": "Q4",
    "id_2_100": "End Q4",
    "id_2_32": "Awaiting OT",
    "id_2_40": "Overtime",
    "id_2_110": "Overtime Ended",
    "id_2_61": "Postponed",
    "id_2_80": "Interrupted",
    "id_2_90": "Abandoned",
    "id_2_1": "1H",
    "id_2_2": "2H",
    "id_2_31": "1H Ended",
    "id_2_999": "Ended",
    "id_5_": "All",
    "id_5_0": "Not Started",
    "id_5_8": "Set 1",
    "id_5_9": "Set 2",
    "id_5_10": "Set 3",
    "id_5_11": "Set 4",
    "id_5_12": "Set 5",
    "id_5_100": "Full Time End",
    "id_5_301": "Set 1 End",
    "id_5_302": "Set 2 End",
    "id_5_303": "Set 3 End",
    "id_5_304": "Set 4 End",
    "id_5_305": "Set 5 End",
    "id_5_93": "Default 2",
    "id_5_94": "Default 1",
    "id_5_95": "Retired 1",
    "id_5_96": "Retired 2",
    "id_5_97": "Absent 1",
    "id_5_98": "Abdent 2",
    "id_5_800": "Game 1 End",
    "id_5_900": "Game 2 End",
    "id_5_1000": "Game 3 End",
    "id_5_1100": "Game 4 End",
    "id_5_1200": "Game 5 End",
    "id_5_61": "Postponed",
    "id_5_80": "Interrupted",
    "id_5_90": "Canceled",
    "id_5_999": "Ended",
    "id_3_": "All",
    "id_3_0": "Not Started",
    "id_3_401": "T1",
    "id_3_421": "Break T1-B1",
    "id_3_402": "B1",
    "id_3_422": "Break T2-B1",
    "id_3_403": "T2",
    "id_3_423": "Break T2-B2",
    "id_3_404": "B2",
    "id_3_424": "Break T3-B2",
    "id_3_405": "T3",
    "id_3_425": "Break T3-B3",
    "id_3_406": "B3",
    "id_3_426": "Break T4-B3",
    "id_3_407": "T4",
    "id_3_427": "Break T4-B4",
    "id_3_408": "B4",
    "id_3_428": "Break T5-B4",
    "id_3_409": "T5",
    "id_3_429": "Break T5-B5",
    "id_3_410": "B5",
    "id_3_430": "Break T6-B5",
    "id_3_411": "T6",
    "id_3_431": "Break T6-B6",
    "id_3_412": "B6",
    "id_3_432": "Break T7-B6",
    "id_3_413": "T7",
    "id_3_433": "Break T7-B7",
    "id_3_414": "B7",
    "id_3_434": "Break T8-B7",
    "id_3_415": "T8",
    "id_3_435": "Break T8-B8",
    "id_3_416": "B8",
    "id_3_436": "Break T9-B8",
    "id_3_417": "T9",
    "id_3_437": "Break T9-B9",
    "id_3_418": "B9",
    "id_3_438": "Awaiting OT",
    "id_3_43820": "Awaiting OT",
    "id_3_419": "Overtime 1H",
    "id_3_439": "Break Overtime",
    "id_3_420": "Overtime 2H",
    "id_3_41910": "T10",
    "id_3_43910": "Break T10-B10",
    "id_3_42010": "B10",
    "id_3_43810": "Break T11-B10",
    "id_3_41911": "T11",
    "id_3_43911": "Break T11-B11",
    "id_3_42011": "B11",
    "id_3_43811": "Break T12-B11",
    "id_3_41912": "T12",
    "id_3_43912": "Break T12-B12",
    "id_3_42012": "B12",
    "id_3_43812": "Break T13-B12",
    "id_3_41913": "T13",
    "id_3_43913": "Break T13-B13",
    "id_3_42013": "B13",
    "id_3_43813": "Break T14-B13",
    "id_3_41914": "T14",
    "id_3_43914": "Break T14-B14",
    "id_3_42014": "B14",
    "id_3_43814": "Break T15-B14",
    "id_3_41915": "T15",
    "id_3_43915": "Break T15-B15",
    "id_3_42015": "B15",
    "id_3_43815": "Break T16-B15",
    "id_3_41916": "T16",
    "id_3_43916": "Break T16-B16",
    "id_3_42016": "B16",
    "id_3_43816": "Break T17-B16",
    "id_3_41917": "T17",
    "id_3_43917": "Break T17-B17",
    "id_3_42017": "B17",
    "id_3_43817": "Break T18-B17",
    "id_3_41918": "T18",
    "id_3_43918": "Break T18-B18",
    "id_3_42018": "B18",
    "id_3_43818": "Break T19-B18",
    "id_3_41919": "T19",
    "id_3_43919": "Break T19-B19",
    "id_3_42019": "B19",
    "id_3_43819": "Break T20-B19",
    "id_3_41920": "T20",
    "id_3_43920": "Break T20-B20",
    "id_3_42020": "B20",
    "id_3_100": "Match Ended",
    "id_3_80": "Interrupted",
    "id_3_90": "Abandoned",
    "id_3_999": "Ended",
    "id_9_": "All",
    "id_9_0": "Not started",
    "id_9_8": "Set 1",
    "id_9_301": "Pause 1",
    "id_9_9": "Set 2",
    "id_9_302": "Pause 2",
    "id_9_10": "Set 3",
    "id_9_303": "Pause 3",
    "id_9_11": "Set 4",
    "id_9_304": "Pause 4",
    "id_9_12": "Set 5",
    "id_9_305": "Pause 5",
    "id_9_441": "Set 6",
    "id_9_306": "Pause 6",
    "id_9_442": "Set 7",
    "id_9_100": "Full Time End",
    "id_9_17": "Golden Set",
    "id_9_37": "Awaiting Golden",
    "id_9_130": "Golden Ended",
    "id_9_93": "Default 1",
    "id_9_94": "Default 2",
    "id_9_95": "Retired 1",
    "id_9_96": "Retired 2",
    "id_9_61": "Postponed",
    "id_9_80": "Interrupted",
    "id_9_90": "Abandoned",
    "id_9_999": "Ended",
    "id_8_": "All",
    "id_8_0": "Not started",
    "id_8_8": "Set 1",
    "id_8_301": "Pause 1",
    "id_8_9": "Set 2",
    "id_8_302": "Pause 2",
    "id_8_10": "Set 3",
    "id_8_303": "Pause 3",
    "id_8_11": "Set 4",
    "id_8_304": "Pause 4",
    "id_8_12": "Set 5",
    "id_8_305": "Pause 5",
    "id_8_441": "Set 6",
    "id_8_306": "Pause 6",
    "id_8_442": "Set 7",
    "id_8_100": "Full Time End",
    "id_8_94": "Default 1",
    "id_8_93": "Default 2",
    "id_8_95": "Retired1",
    "id_8_96": "Retired2",
    "id_8_97": "Absent 1",
    "id_8_98": "Absent 2",
    "id_8_61": "Postponed",
    "id_8_80": "Interrupted",
    "id_8_90": "Canceled",
    "id_8_999": "Ended",
    "id_7_": "All",
    "id_7_0": "Not started",
    "id_7_21": "Live",
    "id_7_30": "Pause",
    "id_7_445": "Session Break",
    "id_7_100": "Full Time End",
    "id_7_94": "Default 1",
    "id_7_93": "Default 2",
    "id_7_95": "Retired 1",
    "id_7_96": "Retired 2",
    "id_7_97": "Abandon 1",
    "id_7_98": "Abandon 2",
    "id_7_61": "Postponed",
    "id_7_80": "Interrupted",
    "id_7_90": "Canceled",
    "id_7_999": "Ended",
    "id_4_": "All",
    "id_4_0": "Not started",
    "id_4_1": "1st Period",
    "id_4_301": "Pause1",
    "id_4_2": "2nd Period",
    "id_4_302": "Pause2",
    "id_4_3": "3rd Period",
    "id_4_100": "Full Time End",
    "id_4_32": "Awaiting OT",
    "id_4_40": "Overtime",
    "id_4_110": "Overtime End",
    "id_4_34": "Awaiting PT",
    "id_4_50": "Penalty Shootout",
    "id_4_120": "Penalty Ended",
    "id_4_80": "Interrupted",
    "id_4_90": "Abandoned",
    "id_4_999": "Ended",
    "id_10_": "All",
    "id_10_0": "Not started",
    "id_10_8": "Set 1",
    "id_10_301": "Set 1 End",
    "id_10_9": "Set 2",
    "id_10_302": "Set 2 End",
    "id_10_10": "Set 3",
    "id_10_303": "Set 3 End",
    "id_10_11": "Set 4",
    "id_10_304": "Set 4 End",
    "id_10_12": "Set 5",
    "id_10_100": "Full Time End",
    "id_10_94": "Default 1",
    "id_10_93": "Default 2",
    "id_10_95": "Retired 1",
    "id_10_96": "Retired 2",
    "id_10_97": "Absent 1",
    "id_10_98": "Absent 2",
    "id_10_61": "Postponed",
    "id_10_80": "Interrupted",
    "id_10_90": "Canceled",
    "id_10_999": "Ended",
    "id_6_": "All",
    "id_6_0": "Not started",
    "id_6_13": "Q1",
    "id_6_301": "Pause Q1",
    "id_6_14": "Q2",
    "id_6_302": "Pause Q2",
    "id_6_15": "Q3",
    "id_6_303": "Pause Q3",
    "id_6_16": "Q4",
    "id_6_100": "Full Time End",
    "id_6_32": "Awaiting OT",
    "id_6_40": "Overtime",
    "id_6_110": "Overtime Ended",
    "id_6_80": "Interrupted",
    "id_6_90": "Abandoned",
    "id_6_999": "Ended",
    "matchPeriodId14_60": "Suspension",
    "matchPeriodId14_443": "Awaiting OT",
    "matchPeriodId14_440": "Overtime",
    "matchPeriodId14_444": "Overtime Ended",
  },
  "ball3": {
    "w1": "Top 1",
    "w2": "Bottom 1",
    "w3": "Top 2",
    "w4": "Bottom 2",
    "w5": "Top 3",
    "w6": "Bottom 3",
    "w7": "Top 4",
    "w8": "Bottom 4",
    "w9": "Top 5",
    "w10": "Bottom 5",
    "w11": "Top 6",
    "w12": "Bottom 6",
    "w13": "Top 7",
    "w14": "Bottom 7",
    "w15": "Top 8",
    "w16": "Bottom 8",
    "w17": "Top 9",
    "w18": "Bottom 9",
    "w19": "Top 10",
    "w20": "Bottom 10",
    "w21": "Top 11",
    "w22": "Bottom 11",
    "w23": "Top 12",
    "w24": "Bottom 12",
    "w25": "Top 13",
    "w26": "Bottom 13",
    "w27": "Top 14",
    "w28": "Bottom 14",
    "w29": "Top 15",
    "w30": "Bottom 15",
    "w31": "Top 16",
    "w32": "Bottom 16",
    "w33": "Top 17",
    "w34": "Bottom 17",
    "w35": "Top 18",
    "w36": "Bottom 18",
    "w37": "Top 19",
    "w38": "Bottom 19",
    "w39": "Top 20",
    "w40": "Bottom 20"
  },
  "league_levels": {
    "all_levels": "Levels",
    "level_": "All",
    "level_0": "Unrated",
    "level_1": "Lv 1",
    "level_2": "Lv 2",
    "level_3": "Lv 3",
    "level_4": "Lv 4",
    "level_5": "Lv 5",
    "level_6": "Lv 6",
    "level_7": "Lv 7",
    "level_8": "Lv 8",
    "level_9": "Lv 9",
    "level_10": "Lv 10",
    "level_11": "Lv 11",
    "level_12": "Lv 12",
    "level_13": "Lv 13",
    "level_14": "Lv 14",
    "level_15": "Lv 15",
    "level_16": "Lv 16",
    "level_17": "Lv 17",
    "level_18": "Lv 18",
    "level_19": "Lv 19",
    "level_20": "Lv 20"
  },
  "sport_team_type": {
    "type_": "All",
    "type_1": "Men Team",
    "type_2": "Men Singles",
    "type_3": "Women Singles",
    "type_4": "Men Doubles",
    "type_5": "Women Doubles",
    "type_6": "Mixed Doubles",
    "type_7": "Other",
    "type_8": "Women Team",
    "type_9": "Esports"
  },
  "manage": {
    "all_league": "Leagues",
    "search_1": "More Filter Options",
    "search_2": "League / Team / Match ID",
    "search_21": "League",
    "search_22": "Team",
    "search_23": "External ID",
    "search_3": "Data Source",
    "search_4": "Matched",
    "search_5": "No Match",
    "search_6": "Match Stage",
    "search_7": "Match Status",
    "search_8": "Start Time",
    "search_9": "End Time",
    "search_10": "Reset",
    "search_11": "Confirm",
    "search_12": "Clear",
    "search_13": "All",
    "search_14": "Part",
    "search_15": "Show Lock",
    "search_19": "Electronic Games",
    "search_20": "Non Electronic Games",
    "search_16": "round",
    "search_17": "All Time",
    "search_18": "Enter round",
    "RobotRecommendation": "Recommend",
    "to_day": "Today",
    "tor_day": "Tmr",
    "league": "League",
    "today": "Today",
    "homeTeamNames": "Home",
    "homeTeamPlayer": "Home Player",
    "score": "Score",
    "awayTeamNames": "Away",
    "awayTeamPlayer": "Away Player",
    "neutralGround": "Neutral",
    "dataSourceCode": "Data Provider",
    "relatedMatch": "Competitor",
    "matchManageId": "Match ID",
    "inPlay": "In-Play",
    "action": "Operate",
    "leagueNameEn": "English LeagueName",
    "homeTeamNamesEn": "Home (English)",
    "awayTeamNamesEn": "Away (English)",
    "text_1": "Move to Match Settings",
    "text_2": "New Match",
    "text_3": "All",
    "text_4": "Inverse",
    "text_5": "Create Match",
    "text_6": "Verified",
    "text_7": "Failed",
    "text_8": "Back To Fixture",
    "text_9": "Verification Status",
    "text_10": "Verified",
    "text_11": "Pending",
    "text_12": "eSports subscribed",
    "text_13": "eSports unsubscribed",
    "modal_tips_1": "The same team names for home and away teams are already in pre-sale.",
    "modal_tips_2": "Do you confirm to move it to pre-sale?",
  },
  "round_type": {
    "type_1": "Best 3 of 2",
    "type_2": "Best 5 of 3",
    "type_3": "Best 3 of 2",
    "type_4": "Best 5 of 3",
    "type_5": "Best 7 of 4",
    "type_6": "Best 4 of 3",
  },
  "robot": {
    "text_1": "Total Verified：",
    "text_2": "Verified：",
    "text_3": "All Passed Rate：",
    "text_4": "Portion Passed Rate：",
    "text_5": "Total Matching：",
    "text_6": "Matched：",
    "text_7": "100% Matching Rate (MR)：",
    "text_8": "SR MR：",
    "text_9": "BC MR：",
    "text_10": "BG MR：",
    "text_11": "188 MR：",
    "text_12": "SBA MR：",
    "text_13": "QT MR：",
    "text_14": "SBO MR：",
    "text_15": "Odds Feed MR：",
    "text_16": "Please ensure verification is correct before proceed",
  },
  "selectPeriodTab": {
    "Tab_1": "-",
    "Tab_2": "",
    "Tab_3": "In-Play",
    "Tab_4": "Today",
    "Tab_5": "Tomorrow",
    "Tab_6": "Early Markets",
    "Tab_7": "Historical Fixtures"
  },
  "pagination": {
    "total": "Total",
    "article": "",
    "page": "page",
    "Jump": "Redirect-to",
    "itemPage": "per page",
    "common": "common",
    "record": "record"
  },
  "right_info": {
    "right_tab1": "Match Info",
    "right_tab2": "Real-Time Event",
    "language_1": "CN",
    "language_2": "Abbrev",
    "language_3": "TC",
    "language_4": "English",
    "language_5": "Spanish",
    "language_6": "Italian",
    "language_7": "German",
    "language_8": "French",
    "language_9": "Portuguese",
    "language_10": "Russian",
    "language_11": "Japanese",
    "language_12": "Korean",
    "language_13": "Thai",
    "language_14": "Vietnamese",
    "language_15": "Burmese",
    "language_3_1": "TC-Abbrev",
    "language_yc": "Full Name",
    "language_id": "Language ID",
    "type_1": "League",
    "type_2": "Home",
    "type_3": "Away",
    "type_4": "Team",
    "type_5": "Player",
    "type_6": "Trilateral Team",
    "edit_1": "Linked",
    "edit_2": "Season",
  },
  "match_edit": {
    "edit_title1": "Match MGT",
    "edit_title2": "OpLog",
    "edit_title3": "Reset",
    "edit_title4": "Save",
    "edit_title5": "Required fields。",
    "edit_1": "Billing Date",
    "edit_2": "Match Time",
    "edit_3": "League Name",
    "edit_4": "League Logo",
    "edit_5": "Swap",
    "edit_6": "Upload",
    "edit_7": "League Level",
    "edit_8": "League Area",
    "edit_9": "Format",
    "edit_10": "Type",
    "edit_11": "Neutral Court",
    "edit_12": "Official Site",
    "edit_13": "League Name",
    "edit_14": "Home",
    "edit_15": "Away",
    "edit_16": "Start Time",
    "edit_17": "Language",
    "edit_18": "Edit History",
    "edit_19": "More",
    "edit_20": "Play Field",
    "edit_21": "Field Type",
    "log_1": "OpLog",
    "log_2": "Module",
    "log_3": "Content",
    "log_4": "Account",
    "log_5": "OpTime",
    "log_6": "Remark",
    "log_7": "Add",
    "log_8": "Please enter remarks",
    "edit_22": "Edit MGT",
    "edit_23": "League ID",
    "edit_24": "Logo",
    "edit_25": "League",
    "edit_26": "Team ID",
    "edit_27": "Logo",
    "edit_28": "Team",
    "edit_29": "Name",
    "edit_30": "Player ID",
    "edit_31": "Logo",
    "edit_32": "Player ID",
    "edit_33": "Name",
    "edit_34": "Player Name",
    "edit_35": "Information",
    "edit_36": "Basic Info",
    "edit_37": "Nationality",
    "edit_38": "Birthdate",
    "edit_39": "Gender",
    "edit_40": "Height",
    "edit_41": "Team Against",
    "edit_42": "Alias of League",
    "edit_43": "Weight",
    "edit_44": "Select a league",
    "edit_45": "Change League",
    "edit_46": "Season",
    "edit_47": "Rounds",
    "edit_48": "Start time",
    "edit_49": "End time",
    "edit_51": "Cancel Swap",
    "edit_52": "man",
    "edit_53": "woman",
    "edit_54": "Lottery ID",
    "edit_55": "Please enter Lottery ID",
    "edit_56": "Please select Country",
    "edit_57": "Please enter League URL",
    "edit_58": "Note : Click this button to create new team",
    "edit_59": "Please enter League Name or ID",
    "edit_60": "Please select Home Team",
    "edit_61": "Please select Away Team",
    "edit_62": "Please select format",
    "edit_63": "Please select Neutral",
    "edit_64": "BlockList",
    "edit_65": "BlockList Team",
    "edit_66": "Continue",
    "edit_67": "Later",
    "edit_68": "Edit",
    "edit_69": "The page has been overwritten. Your changes will be lost",
    "edit_70": "Clear All",
    "edit_71": "Please Enter Round",
    "edit_72": "Schedule Type",
    "edit_73": "Promotion spots",
    "edit_74": "Relegation spots",
    "edit_75": "Season",
    "edit_76": "Historical",
    "edit_77": "Start date",
    "edit_78": "End date",
    "edit_79": "Cup",
    "edit_80": "Daylight Saving Time",
    "edit_81": "Winter time",
    "edit_82": "Summer/Winter Time",
    "edit_83": "No season data yet",
    "edit_84": "Relegation spots",
    "edit_85": "League Info",
    "edit_86": "Self league",
    "edit_87": "Trilateral Team Name",
    "edit_88": "Size: 72 x 72 px",
    "edit_89": "File Size: Max 10 Mb",
    "check_1": "Billing Date cannot be blank!",
    "check_2": "Match Time cannot be blank!",
    "check_3": "League Name cannot be blank!",
    "check_4": "League Level cannot be blank!",
    "check_5": "Country cannot be blank!",
    "check_6": "Format cannot be blank!",
    "check_7": "Neutral cannot be blank!",
    "check_8": "Team type cannot be blank!",
    "check_9": "Home Team cannot be blank!",
    "check_10": "Away Team cannot be blank!",
  },
  "match_create_league": {
    "create_title1": "Regular Season",
    "create_title2": "Esports",
    "create_title3": "Self league",
    "create_title4": "早盘规则参数模版",
    "create_title5": "滚球规则参数模版",
    "create_title6": "Promotion spots",
    "create_title7": "Relegation spots",
    "create_title8": "League Name	",
    "create_title9": "Language ID",
    "create_title10": "Language",
    "create_title11": "Full Name",
    "create_title12": "Abbreviation",

    "sj_placeholder": "Input the top teams to advance",
    "jj_placeholder": "Input the bottom teams to be relegated",
    "sc_type_placeholder": "Total Rounds",

  },

  "match_match_cycle": {
    "match_cycle_title1": "Match Cycle",
    "match_cycle_title2": "Other Cycles",
    "other_type_year": "Year",
    "other_type_month": "Month",
    "match_cycle_type1": "No Season Schedule",
    "match_cycle_type2": "Annual",
    "match_cycle_type3": "Cross-Year",
    "match_cycle_type4": "Quadrennial",
    "match_cycle_type5": "Other",
    "match_cycle_message1": "Match cycle cannot be empty.",
    "match_cycle_message2": "If the match cycle is set to 'Other', the 'Other cycle' field must not be empty.",
    "match_cycle_message3": "Please select another cycle type",
  },
  "match_create_season": {
    "season_title1": "Season Info",
    "season_title2": "Season",
    "season_title3": "Historical",
    "season_title4": "Season name",
    "season_title5": "Start Date",
    "season_title6": "End Date",
    "season_title7": "Number",
    "season_title8": "Support the Outrights",
    "season_title9": "Actions",
    "season_title10": "Edit",
    "season_title11": "Linked Teams",
    "season_title12": "Delete",
    "season_title13": "League ID",
    "season_title14": "League Name",
    "season_title15": "Season Year",
    "season_title16": "Auto Import the Latest Team Data",
    "season_title17": "Match Cycle",
    "season_title18": "Date Selection",
    "season_title19": "Start Time",
    "season_title20": "End Time",
    "season_title21": "Add season",
    "season_title22": "Edit season",
    "season_title23": "Delete Season",
    "season_title24": "League Info",
    "season_title25": "Season Info",
    "season_message1": "Season year cannot be empty",
    "season_message2": "Please select date",
  },
  "match_season_date_selection": {
    "type1": "None",
    "type2": "Year-Month-Day-Hour-Minute (UTC+8)",
    "type3": "Year-Month-Day (UTC+8)",
    "type4": "Year-Month",
    "type5": "Year",

  },

  "match_team_jersey": {
    "jersey": 'Jersey',
    "add_jersey": 'Add Jersey',
    "edit_jersey": 'Edit Jersey',
    "jersey_name": 'Jersey Name',
    "season_schedule": "Season Schedule",
    "jersey_type": "Jersey type",
    "jersey_img": "Jersey Illustration",
    "jersey_img_size": "Size Limit",
    "jersey_img_max": "Maximum Size",
    "jersey_style": "Jersey style",
    "color_style": "Color Settings",
    "color_0": "Base Color",
    "color_1": "Collar Color",
    "color_2": "Cuff Color",
    "color_3": "Stitching Color",
    "color_4": "Stripe Color",
    "style": "Style",
    "jersey_name_not_null": 'The jersey name cannot be empty',
    "jersey_default_not_null": 'Please select the default use occasion for jersey',
    "jersey_type_not_null": 'Please select a jersey type',
    "jersey_img_not_null": 'Please upload the jersey picture',
    "jersey_style_not_null": 'Please set a custom jersey style',
    "season_schedule_not_null": 'Please select the season schedule',
    "jersey_module": "Jersey Info",
    "Custom_style": 'Custom style',
    "Upload_img": "Upload Image"
  },
  "match_team": {
    "link_league_details": "Linked league info",
    "team_name": 'Team Name',
    'unlinking': "Unlink",
    "full_name": "Full name",
    "regions": "Region",
    "number": "Number",
    "log": "Log",
    "is_it_a_club": "Is Club",
    "deactivation": "Is Disabled",
    "match_status": "Matching Status",
    "match": "Match",
    "no_match": "Awaiting a match",
    "language_jc": "Abbreviation"

  },

  "match_team_league_info": {
    "league_id": "League ID",
    "simplified": "Simplified",
    "english": "English",
    "season_schedule": "Season Schedule",
    "actions": "Actions"
  },
  "meta_data": {
    "left_pla_1": "ID / League Name",
    "left_pla_2": "Enter Team ID or Team Name",
    "left_pla_3": "ID / Player Name",
    "left_pla_4": "Enter Link Team ID",
    "left_pla_5": "Enter Trilateral Team ID or Trilateral Team Name",
    "left_1": "Level",
    "left_2": "Region",
    "left_3": "Id Status",
    "left_4": "With ID",
    "left_5": "Without ID",
    "left_6": "Reset",
    "left_7": "Submit",
    "left_8": "Id Status",
    "left_9": "Id Status",
    "left_10": "Search",
    "left_id1": "With ID",
    "left_id2": "Without ID",
    "left_id3": "With ID",
    "left_id4": "Without ID",
    "left_id5": "With ID",
    "left_id6": "Without ID",
    "head_1": "League ID",
    "head_2": "Level",
    "head_3": "Region",
    "head_4": "CN",
    "head_5": "Abbrev",
    "head_6": "English",
    "head_7": "Data Provider",
    "head_8": "Actions",
    "head_9": "Team ID",
    "head_10": "Player ID",
    "head_11": "Type",
    "head_12": "Parent league",
    "head_13": "Create league",
    "head_14": "league create league",
    "head_15": "Create league",
    "head_16": "Create Team",
    "head_17": "Disable",
    "head_18": "Recover",
    "head_19": "Three party player ID",
    "head_20": "Belonging team",
    "head_21": "Please enter the third-party player ID",
    "head_22": "Regular team",
    "head_23": "Association List",
    "head_24": "Club",
    "head_25": "Team total",
    "head_26": "Trilateral Team ID",
    "head_27": "Link Team Lib",
    "head_28": "Link Team ID",
    "head_29": "Matching Status",
    "head_30": "Is Disabled",
    "head_31": "Link Status",
    "head_32": "Number",
    "head_35": "Team Name(cn)",
    "head_36": "Team Name(en)",
  },
  "stats_rep": {
    "tab_1": "Quantity",
    "tab_2": "Fusion",
    "tab_3": "Coverage",
    "head_1": "Total",
    "head_2": "Ytd",
    "head_3": "Last 7 Days",
    "head_4": "Last 30 Days",
    "head_5": "Date",
    "head_6": "Matches",
    "head_7": "In-Play",
    "head_8": "Fusion Statistics",
    "head_9": "DateTime",
    "head_10": "League",
    "head_11": "Home",
    "head_12": "Away",
    "left_1": "Search date",
    "left_2": "Race"
  },
  "op_log": {
    "tab_1": "Today",
    "tab_2": "Ytd",
    "head_1": "SerialNum",
    "head_2": "Account",
    "head_3": "Res-Name",
    "head_4": "OpTime",
    "head_5": "Suc or not",
    "head_6": "OpType",
    "head_7": "ID",
    "head_8": "Before OP",
    "head_9": "After OP",
    "head_10": "Remark",
    "left_1": "Account / Login ID",
    "left_2": "Res-Name",
    "left_3": "OpType",
    "left_4": "Start time",
    "left_5": "End time",
    "right_1": "Details",
    "right_2": "OP ID",
    "right_3": "Login ID",
    "right_4": "Account",
    "key_1": "sysModelEn",
    "key_2": "eventEn",
    "alert_1": "The operation log data is empty. Export it after confirmation",
    "alert_2": "Start Time cannot be null!",
    "alert_3": "End Time cannot be null!",
    "alert_4": "The start time cannot be later than the end time",
    "alert_5": "The time frame cannot exceed one month",
  },
  "v_sports": {
    "head_1": "English(provider)",
    "head_2": "Sort"
  },
  "olympic": {
    "year": "Year",
    "sort": "Sorting",
    "olympic_project": "Olympic Event",
    "history": "History",
    "olympic_project_name": "Olympic Event Name",
    "logo": "LOGO",
    "project_introduce": "Project Introduction",
    "rules_summary": "Rule Summary",
    "olympic_history": "Olympic History",
    "olympic_project_association_home": "奥运项目关联到首页",
    "state": "Status",
    "ranking": "Ranking",
    "team": "Team",
    "detailed": "Details",
    "gold_medal": "Gold Medal",
    "silver_medal": "Silver Medal",
    "bronz_medal": "Bronze Medal",
    "total": "Total",
    "single_operation": "Single Operation",
    "operation": "Operation",
    "upload_image": "Upload Image",
    "add_one_piece": "(Can Add 1)",
    "max_img_volume": "Image Cannot Exceed 10KB",
    "support": "Support",
    "add_year": "Add New Year",
    "select_year": "Please Select a Year",
    "olympic_name": "Olympic Name",
    "olympic_name_zh_no_empty": "The Chinese Name Of The Olympic Event Cannot Be Empty",
    "olympic_name_en_no_empty": "The English Name Of The Olympic Event Cannot Be Empty",
    "select_enter_olympic_zh_name": "Please Select Or Enter The Chinese Olympic Name",
    "select_enter_olympic_en_name": "Please Select Or Enter The English Olympic Name",
    "select_enter": "Please Select Or Enter",
    "select_enter_en_name": "Please Select Or Enter English",
    "bing_init_data": "Bring In Initial Data",
    "logo_no_empty": "Logo Cannot Be Empty",
    "year_no_empty": "Year Cannot Be Empty",
    "olympic_name_no_empty": "Olympic Name Cannot Be Empty",
    "data_no_empty": "Whether To Bring Data Cannot Be Empty",
    "bind_champion_config": "Bind Champion Gameplay Configuration",
    "games_name": "Gameplay Name",
    "games_name_zh_no_empty": "The Chinese Gameplay Name Cannot Be Empty",
    "games_name_en_no_empty": "The English Gameplay Name Cannot Be Empty",
    "please_enter": "Please Enter",
    "enter_games_name": "Please Enter Chinese",
    "zh_name_no_empty": "The Chinese Name Cannot Be Empty",
    "enter_en_games_name": "Please Enter English",
    "enter_zh_games_name": "Please Enter Chinese",
    "en_name_no_empty": "The English Name Cannot Be Empty",
    "bind_leagues_id": "Bind Corresponding League ID",
    "bind_leagues_id_no_empty": "League ID Cannot Be Empty",
    "enter_leagues_id": " Please Enter The Event ID",
    "handicap_id_no_empty": "The Betting ID Cannot Be Empty",
    "handicap_id": "Unified Betting ID",
    "enter_handicap_id": "Please Enter The Betting ID",
    "delete_confirm": "Delete Confirmation",
    "sure_delete_information": "Confirm Deletion Of This Configuration Information",
    "add_team": "Add New Team",
    "details": "Details",
    "please_select": "Please Select",
    "enter_amount": "Please Enter Quantity",
    "add": "Add New",
    "confirm": "Confirm",
    "delet": "Delete",
    "enable": "Enable",
    "disable": "Disable",
    "is_change_enable": "Update The Status To [Enable]?",
    "is_change_disable": "Update The Status To [Disable]?",
    "add_olympic_project": "Add New Olympic Project",
    "olympic_project_name": "Olympic Event Name",
    "project_introduce": "Project Introduction",
    "enter_project_introduce": "Please Enter The Chinese Project Introduction",
    "enter_project_introduce_en": "Please Enter The English Project Introduction",
    "project_introduce_zh_no_empty": "The Chinese Name Of The Project Introduction Cannot Be Empty",
    "project_introduce_en_no_empty": " The English Name Of The Pproject Introduction Cannot Be Empty",
    "rules_summary": "Rule Summary",
    "enter_rules_summary": "Please Enter The Chinese Summary Of The Rules",
    "enter_rules_summary_en": "Please Enter The English Summary Of The Rules",
    "rules_summary_zh_no_empty": "The Chinese Name Of The Rule Summary Cannot Be Empty",
    "rules_summary_en_no_empty": "The English Name Of The Rule Summary Cannot Be Empty",
    "olympic_history": "Olympic History",
    "enter_olympic_history": "Please Enter The Chinese Olympic History",
    "enter_olympic_history_en": "Please Enter The English Olympic History",
    "olympic_history_zh_no_empty": "The Chinese Name For Olympic History Cannot Be Empty",
    "olympic_history_en_no_empty": "The English Name For Olympic History Cannot Be Empty",
    "game_name": "Gameplay Name",
    "enter_game_name": "Please Enter The Chinese Gameplay Name",
    "enter_game_name_en": "Please Enter The English Gameplay Name",
    "change_state": "Change Status",
    "search": "Search",
    "add_medal_table": "Add New Medal Table",
    "medal_table": "Medal Table"
  },
  "remediation_tools": {
    "tab": {
      "closing_process": "Close market handling",
      "modify_event_status": "Modify event status",
      "football_match_status": "Handle football event status",
      "clear_cache_gameplay": "Clear automatic close market cache",
      "modify_event_logo": "Modify event live betting status",
      "change_league": "Change standard league",
      "restart_event": "Reopen event",
      "change_championship_league": "Change championship event league",
      "edit_information": "Edit team information",
      "change_team": "Change event teams",
      "l01_start_correction": "L01 start time correction",
      "l01_league_update": "L01 league update",
      "odds_source_adjustment": "Pre-sale early market and live betting historical odds source adjustment",
      "event_state_clear": "Event status reset",
    },
    "chang_format_option": {
      "0": "0-2Hx45m(football 90m) 10mx4Q(basketball 40m)",
      "1": "1-2Hx20m(football 40m)",
      "10": "10-2Hx15m(football 30m)",
      "55": "55-2Hx3m(eFootball 6m)",
      "57": "57-2Hx2m(eFootball 4m)",
      "62": "62-2Hx6m(eFootball 12m)",
      "63": "63-2Hx5m(eFootball 10m)",
      "69": "69-2Hx10m(eFootball 20m)",
      "71": "71-2Hx4m(eFootball 8m)",
      "72": "72-2Hx15m(eFootball 30m)",
      "7": "7-12m x 4Q(basketball 48m)",
      "17": "17-20m x 2H(basketball 40m)",
      "68": "68-5m x 4Q(eBasketball 20m)",
    },
    "operator": 'Operator',
    "operator_id": 'Operator ID',
    "operator_id_not_empty": 'Operator ID cannot be empty',
    "enter_operator_id": 'Operator ID, editable, for production use: 110',
    "code": "Status code",
    "return_msg": "Return information",
    "handicap_source_id": "Third-party market source ID",
    "enter_handicap_source_id": "Please enter the third-party market source ID",
    "handicap_source_id_not_empty": "Third-party market source ID cannot be empty",
    "standard_event_id": "Standard event ID",
    "enter_standard_event_id": "Please enter the standard event ID",
    "standard_event_id_not_empty": " Standard event ID cannot be empty",
    "event_manage_id": "Event management ID",
    "enter_event_manage_id": "Please enter the event management ID",
    "event_manage_id_not_empty": "Event management ID cannot be empty",
    "standard_event_manage_id": "Standard event management ID",
    "enter_standard_event_manage_id": "Please enter the standard event management ID",
    "standard_event_manage_id_not_empty": "Standard event management ID cannot be empty",
    "event_id": "Event ID",
    "enter_event_id": "Please enter the event ID",
    "event_id_not_empty": "Event ID cannot be empty",
    "team_manage_id": "Team management ID",
    "team_id": "Team ID",
    "enter_team_manage_id": "Please enter the team management ID",
    "team_manage_id_not_empty": "Team management ID cannot be empty",
    "shampionship_event_manage_id": "Championship event management ID",
    "shampionship_event_manage_id_not_empty": "Championship event management ID cannot be empty",
    "enter_shampionship_event_manage_id": "Please enter the championship event management ID",
    "league_manage_id": "League management ID",
    "league_manage_id_not_empty": " League management ID cannot be empty",
    "enter_league_manage_id": "Please enter the league management ID",
    "event_status": "Event status",
    "standard_gameplay_id": "Standard play ID",
    "gameplay_id": "Play ID",
    "gameplay_name": "Play name",
    "enter_standard_gameplay_id": " Please enter the standard play ID",
    "standard_gameplay_id_not_empty": "Standard play ID cannot be empty",
    "standard_manage_id": "Event management ID/standard event ID",
    "enter_standard_manage_id": "Please enter the event management ID/standard event ID",
    "standard_manage_id_not_empty": "Event management ID/standard event ID cannot be empty",
    "reopening_time": "Reopen time",
    "original_time": "Original time",
    "update_time": "Corrected time",
    "team_logos": "Home/away team identifier",
    "team_home": "Home team",
    "team_away": "Away team",
    "data_sources_num": "Number of associated data sources",
    "enter_data_sources_num": "Please enter the number of associated data sources",
    "data_sources_num_not_empty": "Number of associated data sources cannot be empty",
    "data_sources_code_list": "List of associated data source codes",
    "enter_data_sources_code_list": "Please enter the list of associated data source codes",
    "data_sources_code_list_not_empty": "List of associated data source codes cannot be empty",
    "data_sources_code": "Data source code",
    "enter_data_sources_code": "Please enter the data source code",
    "data_sources_code_not_empty": "Data source code cannot be empty",
    "batch_processing": "Batch processing, separated by commas",
    "footerball_batch_processing": "For football, batch processing, separated by commas",
    "source": "Data source",
    "enter_odds_source": "Please enter the odds source",
    "odds_source_not_empty": "Odds source cannot be empty",
    "early": "Early market",
    "enter_event_original_id": "Please enter the third-party original event ID",
    "event_original_id_not_empty": "Third-party original event ID cannot be empty",
    "early_or_rolling": "Early market or live betting",
    "request_results": "Request result",
    "single_time_limit": "Single period time limit",
    "enter_single_time_limit": "Please enter the limit time",
    "single_time_limit_not_empty": "Limit time cannot be empty",
    "end_time_limit": "Current basketball immediate settlement end time limit",
    "order_No": "Order number",
    "enter_order_No": "Order number, multiple please use commas (maximum 200 order numbers at a time)",
    "enter_order_No2": "Please enter the order number",
    "enter_order_No3": "Order number (only 200 orders per single request, separate multiple requests with commas)",
    "order_No_not_empty": "Order number cannot be empty",
    "confirm_close": "Are you sure you want to handle emergency close market?",
    "confirm_modify_league_id": "Are you sure you want to modify the league management ID?",
    "confirm_modify_event_id": "Are you sure you want to modify the event team ID?",
    "confirm_clear_cache": "Are you sure you want to clear the cache?",
    "confirm_close_plate": "Are you sure you want to handle emergency close market?",
    "confirm_open_or_close_plate": "Are you sure you want to urgently open/close the market?",
    "sure_send": "Are you sure you want to issue?",
    "confirm_modify_team": " Are you sure you want to modify the team?",
    "confirm_need_clear": " Are you sure you want to reset the event status to pre-match?",
    "confirm_modify_event_state": "Are you sure you want to modify the event status?",
    "confirm_submit": "Are you sure you want to submit?",
    "confirm_change_event_logo": "Are you sure you want to modify the live betting identifier to pre-match?",
    "confirm_adjust_odds_source": "Are you sure you want to adjust the odds source?",
    "confirm_modify_event_info": "Are you sure you want to modify the completed event information?",
    "confirm_correct_source": "Are you sure you want to correct the data source?",
    "confirm_restart_game": "Are you sure you want to restart the match?",
    "confirm_use": "Are you sure you want to use it?",
    "process_info": "Handling information",
    "confirm": "Confirm",
    "confirm_modify": "Confirm correction",
    "clear": "Clear",
    "confirm_clear": "Cleanup",
    "open_plate": "Open market",
    "close_plate": "Close market",
    "change_state": "Status change",
    "enter_request_result": "Please enter the request result",
    "request_result_not_empty": "Request result cannot be empty",
    "set_start_time": "Please set the start time",
    "start_time": "Match start time",
    "start_time_not_empty": "Match start time cannot be empty",
    "standard_event_info": "Standard event information",
    "restart": "Restart match",
    "modify_time": "Modify time",
    "modify_time_after": "Time After Modification",
    "accounts_time": "Financial time",
    "event_status_source": "Event status source",
    "rolling_sales_status": "Live betting sale status",
    "rolling_sales_time": "Live betting sale time",
    "preMatch_sales_status": "Pre-match sale status",
    "preMatch_sales_time": "Pre-match sale time",
    "business_event_source_coding": "Commercial event source code",
    "is_support_rolling": "Supports live betting",
    "rolling_data_service_provider": "Live betting data provider",
    "preMatch_data_service_provider": "Pre-match data provider",
    "rolling_trading_platform": "Live betting platform",
    "preMatch_trading_platform": "Pre-match platform",
    "sale_info": "Sales information",
    "rolling_odds_logos": "Live betting odds identifier",
    "is_game_over": "Is the match finished",
    "match_against_info": "Match opponent information",
    "competition_format1": "(Badminton, Tennis, Baseball, etc.) Tournament format",
    "competition_format2": "(Football, Basketball, Ice Hockey, American Football) Tournament format",
    "game_stage": "Match phase",
    "event_original_id": "Third-party original event ID",
    "data_encoding": "Data code",
    "match_open_logos": "Match open market identifier",
    "is_open_rolling": "Is live betting open",
    "is_open_preMatch": "Is pre-match market open",
    "game_time": "Match progress time",
    "third_party_contest_id": "Third-party match ID",
    "standard_league_id": "Standard league ID",
    "sports_types": "Sport type",
    "submit": "Submit",
    "user": "Username",
    "user_acount": "User",
    "user_id": "User id",
    "merchant": "Merchant",
    "odds": "Odds",
    "bet_amount": "Bet amount (in yuan)",
    "bet_amount1": "Betting slip amount",
    "event_name": "Event name",
    "home_away_name": "Home/away team name",
    "trader": "Trader",
    "device_infor": "Device information",
    "rejection_detail": "Rejection details",
    "rejection_time": "Rejection time",
    "rejection_reason": "Rejection reason",
    "release_results": "One-click order settlement - issue results",
    "release_results_tips": "[Only supports: normal, single parlay, all sports, one-click issue results]",
    "event_number": "Event number",
    "beting_time": "Bet time",
    "sports_type_name": "Sport type name",
    "event_type": "Event type",
    "bet_odds": "Betting slip odds",
    "final_handicap_type": "Final market type",
    "handicap": "Market value",
    "final_odds": "Final odds",
    "is_auto_highest_odds": "Automatically accept highest odds",
    "is_del": "Delete",
    "json_format": "All possible betting slip results in JSON format",
    "settlement_event_stage_id": "Settlement event phase id",
    "primary_secondary_logos": "Main/sub market identifier",
    "preMatch_trading_source": "Pre-match trading source",
    "rolling_trading_source": "Pre-match trading source",
    "odds_source": "Odds data source",
    "settlement_amount": "Settlement amount",
    "payout_status": "Payout status",
    "settlement_type": "Settlement type",
    "settlement_time": "Settlement time",
    "creat_time": "Creation time",
    "manual_settlement": "Manual settlement",
    "auto_settlement": "Automatic settlement",
    "settlement_rollback": "Settlement rollback",
    "settlement_rollback_again": "Settlement rollback and then re-settle (format: 3X, e.g., 31, re-settle for the first time)",
    "bet_principal": "Betting slip principal",
    "bet_principal1": "Betting slip principal (European odds)",
    "net_profit": "Net profit",
    "original_net_profit": "Net profit in original currency",
    "billing_ip": "Settlement IP address",
    "is_final_settlement_result": "Is this the final settlement result",
    "original_settlement_amount": "Settlement amount in original currency",
    "original_bet_amount": "Betting slip principal in original currency",
    "current_account_balance": "Current account balance",
    "change_amount": "Change amount",
    "change_type": "Change type",
    "increase": "Increase",
    "reduce": "Decrease",
    "account_change_type": "Account change business type",
    "amount_before_transfer": "Amount before transfer",
    "amount_after_transfer": "Amount after transfer",
    "l01_roll_ball": "L01 live betting (Use this function when there is a delay or disconnection in live betting odds!)",
    "l01_before_event": "L01 pre-match (Use this function when there is a delay or disconnection in early market odds!)",
    "enter_leagues_name": "Please enter league name",
    "sports": "Sport type",
    "format_id": 'Tournament format ID',
    "change_event_status": "Change to status",
    "ordinary_event": "Regular event",
    "change_time": "Change time",
    "rolling_state_modify_stage": "Live betting status selection for modification",
    "open_0": "Not open",
    "open_1": "Open",
    "match_market_status": {
      "-1": "Not started",
      "0": "Open market",
      "1": "Close market",
      "2": "Close market",
      "11": "Lock market",
      "12": "Decommission",
      "13": "Close market"
    },
    "is_end": {
      "0": "Yes",
      "1": "No",
      "2": "Temporary status",
    },
    "support_0": "Not supported",
    "support_1": "Supported",
    "Unsold": "Not sold",
    "Overdue_Unsold": "Overdue not sold",
    "Apply_Delay": "Apply for extension",
    "Sold": "Open for sale",
    "Apply_Stop_Sold": "Apply for stop sale",
    "Stop_Sold": "Stop sale",
    "Expected_End_Sold": "Unexpected stop sale",
    "Cancel_Sold": "Cancel open for sale",
    "third_event_id": "Third-party market source ID/event ID",
    "open_close_process": "Emergency opening/closing disk processing"
  },
  "against": {
    "modify_reason": "Reason for modification",
    "stage": "Stage",
    "group": "Group",
    "game_rounds": "Match round",
    "group_stage": "Group Stage",
    "select_begin_time": "Please Select A Game Time",
    "select_change_reason": "Please Select The Reason For Modification",
    "tip1": "The delay time must be after the original time!",
    "tip2": "The delayed time must be on the same day as the original time!",
    "tip3": "The extension time must be after the original time!",
    "tip4": "The postponed time cannot be the same day as the original time!",
    "tip5": "The advance time must be before the original time!",
  },
  
  "match_resource_right_details": {
    
  
    "league_link": "League Website",
  
    "season_info": "Season Info",
  },
  "match_resource": {

    "tab_normal": "Regular Season",
    "tab_related": "Linked list",
    "gen_league_id": "Generate League ID",
    "tr_config_seasonNameJson": "Season",


    "tr_config_tournamentManageId": "League ID",
    "tr_config_tournamentLevel": "League Level",

    "tr_config_thirdTournamentSourceId": "Trilateral League ID",

    "tr_config_thirdTournamentSource": "Trilateral League",

    "tr_config_match_type": "Trilateral Type",
    "tr_config_match_type_relative": "Link League Lib",
    "tr_config_referenceId": "Link League ID",


    "tr_config_region_id": "League Region",
    "tr_config_en_name": "English Name",
    "tr_config_zs_name": "Simplified Name",

    "action_league_unlink": "Unlink",
    "action_league_edit": "Edit",

    "status_relation_is_linked": "Link Status",
    "status_relation_linked": "Linked",
    "status_relation_unlinked": "Unlinked",

    "status_relation_match_statue": "Matching Status",
    "status_relation_is_match": "is Match",
    "status_relation_match": "Match",
    "status_relation_unmatch": "Unmatch",
    "status_relation_pending": "Pending",
    "status_relation_completed": "Completed",


    "status_operation_is_disable": "Is Disable",
    "status_operation_status_enable": "Enable",
    "status_operation_status_disable": "Disable",

    "placeholder_enter_league_id": "Enter League ID or League Name",
    "placeholder_enter_link_league_id": "Enter Link League ID",



    "title_schedule_type": "Schedule Type",
    "title_league_name": "League Name",
    "title_league_region": "League Region",
    "title_competition_format": "Competition Format",
    
    "title_game_round": "Total Round",
    "input_trilateral_type_regular_season": "Regular Season",
    "input_trilateral_type_esports": "ESports",

    "input_title_data_source": "Data Sources",

    "error_league_en_name_empty": "League English Name cannot be empty",

    "error_league_zs_name_empty": "League Simplified Name cannot be empty",

    "error_need_unlink_id": "Please select unlink league data id ",

    "left_window_more_filter": "More Filter Options",


    ///关联联赛弹框
    "dialog_associate_teams_dialog_title": "Link League",

    "dialog_associate_teams_dialog_league_id": "League ID",
    "dialog_associate_teams_dialog_league_type": "League Type",
    "dialog_associate_teams_dialog_league_name": "League Name",
    "dialog_associate_teams_dialog_league_region": "League Region",
    "dialog_associate_teams_dialog_league_error_multi_standard_id": "三方联赛只能关联在同一个标准，请先个别移除或重新选择",



    "dialog_associate_teams_dialog_league_type_regular": "Regular Season",
    "dialog_associate_teams_dialog_league_type_season": "ESports",

    "dialog_associate_teams_dialog_enter_league_manage_id": "Please enter the League management ID",

    "dialog_associate_teams_dialog_league_not_found": "Linked Failed, can't find league info",

    ///生成联赛ID弹框
    "dialog_gen_teams_dialog_league_type_missing": "Please Select Schedule Type",
    "dialog_gen_teams_dialog_league_length_missing": "Please Select Competition Format ",
    "dialog_gen_teams_dialog_league_round_missing": "Please Enter round Type",


    
    "dialog_gen_teams_dialog_league_round_missing": "Please Enter round Type",



  }
}
