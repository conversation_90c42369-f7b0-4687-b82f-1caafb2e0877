<template>
  <q-select
    filled
    rounded
    class="w150x q-mr-lg"
    color="panda-text-light"
    bg-color="panda-base-dark"
    v-model="model"
    hide-bottom-space
    :options="oddsOptions"
    emit-value
    map-options
    option-value="value"
    option-label="label"
    dense
    options-dense
    @input="modify_odds_fields"
    popup-content-class="bg-panda-base-dark text-panda-text-dark"
    :disable="isShow"
  >
    <template #prepend>
      <span class="icon m5x">
        <img v-if="model !== 'DE'" :src="oddsIconPath(model)" style="width: 12px; margin-top: 5px" alt="" />
      </span>
    </template>
    <template #option="scope">
      <q-item v-bind="scope.itemProps" v-on="scope.itemEvents" class="row">
        <q-item-section avatar class="col-2 rml5x">
          <span class="icon ml5x">
            <img v-if="scope.opt.value !== 'DE'" :src="oddsIconPath(scope.opt.value)" style="width: 12px; margin-top: 2px" alt="" />
          </span>
        </q-item-section>
        <q-item-section class="ml5x">
          <q-item-label v-html="scope.opt.label" />
          <q-item-label caption>{{ scope.opt.description }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { useRoute } from 'vue-router';
  import { i18n_t } from 'src/output/common/project-common.js';
  import { get_order_odds_kind, get_order_show_page, set_order_odds_kind } from 'src/output/common/store-common.js';

  const route = useRoute();

  const oddsOptions = [
    { label: i18n_t('common.default'), value: 'DE' },
    { label: i18n_t('handicap.EU'), value: 'EU' }, // 欧洲盘
    { label: i18n_t('handicap.HK'), value: 'HK' }, // 亚洲盘
    { label: i18n_t('handicap.MY'), value: 'MY' }, // 马来盘
    { label: i18n_t('handicap.ID'), value: 'INDO' }, // 印尼盘
    { label: i18n_t('handicap.US'), value: 'US' }, // 美国盘
    { label: i18n_t('handicap.UK'), value: 'UK' }, // 英国盘
  ];

  const oddsIconMap = {
    EU: 'eu',
    HK: 'hk',
    INDO: 'id',
    MY: 'my',
    UK: 'uk',
    US: 'us',
  };

  const model = ref(get_order_odds_kind().value);
  const orderShowPage = get_order_show_page();

  watch(
    () => get_order_odds_kind().value,
    value => {
      model.value = value;
    },
  );

  const pathName = computed(() => route.path.includes('order_match_list'));

  const isShow = computed(() => {
    if (route.name === 'order_center_electronic') {
      return false;
    }
    // 非全部都不可切换赔率
    return orderShowPage.value !== 1;
  });

  const oddsIconPath = value => `/img/${oddsIconMap[value]?.toLowerCase()}.png`;

  const modify_odds_fields = value => set_order_odds_kind(value);
</script>

<style lang="scss" scoped>
  .icon {
    line-height: 20px;
    font-size: 8px;
  }
</style>
