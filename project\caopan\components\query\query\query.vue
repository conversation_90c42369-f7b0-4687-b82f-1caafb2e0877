<!--
 * @FilePath: /project/caopan/components/query/query.vue
 * @Description:
-->
<template>
  <!-- 查询数据区域开始 -->
  <div class="column mt20x">
    <div class="mb20x">
      <slot name="Inquire"></slot>
    </div>
    <div class="pl10x">
      <slot name="checkbox_suffix_1"></slot>
    </div>
    <slot name="allselect"></slot>
    <slot name="BSselect"></slot>
    <div
      v-for="(item, index) in queryformItem"
      :key="index"
      class="row mb20x fpptall_selection"
    >
      <span class="test-panda-sidebar-lable mr4x inputlabel">{{
        item.title
      }}</span>
      <iSelect
        class="col mr20x bg-panda-field-grey"
        size="small"
        :placeholder="`${i18n_t('common.pleaseChoose')}`"
        v-model="query_form.queryform_form_data[item.name]"
      >
        <iOption
          v-for="item in item.options"
          :value="item.value"
          :key="item.value"
          >{{ item.description }}</iOption
        >
      </iSelect>
    </div>
    <div>
      <data-source-code
        v-if="showDataSourceCode"
        :title="i18n_t('playManagement.play_data_source')"
        :show_all="false"
        :not_pa="2"
        @change="queryform_form_data_change"
      />
      <!-- 玩法数据源 -->
    </div>
    <div>
      <panda-btn v-bind="$attrs.btn" v-if="showBtnArea" />
    </div>
  </div>
  <!-- 查询数据区域结束 -->
</template>

<script setup>
import { i18n_t } from "src/output/common/project-common.js";
import dataSourceCode from "project/caopan/components/query/data_source_code/data_source_code.vue";
import pandaBtn from "project/caopan/components/btn/btn.vue";
import { project_caopan_components_query_query_componsable_fn } from "project/caopan/components/query/query/componsable/index.js";
import { project_caopan_components_query_query_componsable_variable_fn } from "project/caopan/components/query/query/componsable/variable.js";
const emit = defineEmits(["query_sx_change"]);

const props = defineProps({
  query_form: {
    type: Object,
    default: () => ({}),
  },
  other: {
    // 传入非datasource的数据源信息集合
    default() {
      return {
        title: i18n_t("report_common.search30"), //"赛事",
        data: [
          { fullName: "1", code: 1 },
          { fullName: "2", code: 2 },
        ],
      };
    },
  },
  select_class: {
    type: String,
    default: "w150x",
  },
  showDataSourceCode: {
    default: false,
  },
  queryformItem: {
    default() {
      return [
        { title: i18n_t("gameplaySetManagement.gameplay_set_type") },
        { title: i18n_t("gameplaySetManagement.gameplay_set_state") },
      ];
      // "玩法集类型"       玩法集状态
    },
  },
  showBtnArea: {
    default: false,
  },
});

const base_payload =
  project_caopan_components_query_query_componsable_variable_fn();
const { queryform_form, old_value } = base_payload;

const { queryform_form_data_change } =
  project_caopan_components_query_query_componsable_fn({
    ...base_payload,
    props,
    emit,
  });
</script>

<style lang="scss" scoped>
@import url("project/caopan/components/query/query/css/index.scss");
</style>
