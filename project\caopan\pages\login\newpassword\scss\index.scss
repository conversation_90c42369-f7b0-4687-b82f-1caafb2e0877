.bg-panda-query-date-active {
    background-color: var(--q-color-panda-primary);
    border: none;
    color: #fff !important;
  }
  .bg-panda-query-date-active:hover {
    background: #00a99766 !important;
    color: #fff !important;
    border: 0 none;
  }
  .bg-panda-bg-2 {
    background: #00a997 !important;
  }
  .bg-panda-bg-8 {
    background: #9e9e9e !important;
  }
  
  #newpaswodrd {
    :deep(.q-field--dense .q-field__control) {
      height: 40px;
      min-height: 40px;
    }
    :deep(.q-field__marginal) {
      margin-top: 9px;
    }
  }
  
  .login {
    width: 100vw;
    height: 100vh;
    min-width: 1200px;
    min-height: 600px;
    opacity: 0.96;
    overflow: hidden;
  }
  .password-title {
    font-family: PingFangSC-Semibold;
    font-size: 35px;
    line-height: 40px;
    margin-bottom: 60px;
  }
  
  .codemsg-bg {
    background: url("/assets/codemsg.png") no-repeat center center;
    background-size: 280px 54px;
    width: 280px;
    height: 54px;
    font-size: 15px;
    line-height: 54px;
    padding-left: 10px;
    text-align: center;
  }
  .white-bg {
    background: url("/assets/grey.png") no-repeat center center;
    background-size: 161px 152px;
    width: 161px;
    height: 152px;
    padding: 6px;
  }
  .imgcode {
    width: 140px;
  }
  .password-input-shape {
    width: 406px;
  }
  
  .password-input-item {
    background: #fff;
    border-radius: 4px;
    border-radius: 4px;
    margin-top: 30px;
  }
  :deep(.q-field__native) {
    font-size: 16px;
    color: #333;
  }
  
  // form表单input样式重写
  :deep(.q-field--with-bottom) {
    padding-bottom: 0;
  }
  
  :deep(.q-field__bottom--animated) {
    bottom: 4px;
  }
  
  :deep(.q-field__marginal) {
    font-size: 16px;
  }
  
  :deep(.q-field__native, .q-field__prefix, .q-field__suffix) {
    font-size: 16px;
  }
  
  .text-tishi {
    color: #d06263;
  }
  .mt1x {
    margin-top: 1px;
  }
  .mt20x {
    margin-top: 20px;
  }