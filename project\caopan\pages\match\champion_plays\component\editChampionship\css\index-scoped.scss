
:deep( .numberInput input::-webkit-outer-spin-button),
:deep( .numberInput  input::-webkit-inner-spin-button ){
-webkit-appearance: none;
}
:deep( .ivu-modal-footer ){
  display: none;
}
:deep( .ivu-modal ){
  top: 30vh;
}
.dialog_box {
  width: auto;
  height: auto;
  .t_1_c_e {
    border: 1px solid var(--q-color-panda-them--404758);
    padding: 0 10px;
    margin: 15px 20px;
    line-height: 30px;
  }
}
.title  {
    line-height: 32px;
    text-align: left;
    color: red;
  }
  :deep( .ivu-input::-webkit-inner-spin-button ){
    -webkit-appearance: none !important;
  }
  :deep( .ivu-input::-webkit-outer-spin-button ){
    -webkit-appearance: none !important;
  }
    .edit_championship {
	position: absolute;
	top: 0px;
	right: -800px;
	width: 800px;
	height: 100%;
	transition: all 0.5s;
	background: var(--q-color-panda-base-light4);
	z-index: 60;
	.set_yuyan {
		width: 100%;
		padding: 10px 20px;
		.title {
			margin-bottom: 20px;
			font-size: 12px;
			color: var(--q-color-panda-table-text-header);
		}
		.wanfa_box {
			margin-top: 10px;
			.wanfa_box_left {
				color: var(--q-color-panda-table-text-header);
			}
		}
	}
	.play-list {
		.full-width {
			height: 30px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 15px;
			div {
				display: inline-flex;
        &:nth-child(2) {
					width: 32px;
					justify-content: left;
					align-items: center;
					& > span {
						display: inline-flex;
						justify-content: center;
						align-items: center;
						width: 15px;
						height: 15px;
						line-height: 15px;
						margin-right: 10px;
						font-size: 16px;
						border-radius: 50%;
						cursor: pointer;
						background: var(--q-color-primary);
					}
				}
				&:nth-child(4) {
					width: 150px;
				}
				&:nth-child(5) {
					width: 50px;
					justify-content: left;
					align-items: center;
					& > span {
						display: inline-flex;
						justify-content: center;
						align-items: center;
						width: 15px;
						height: 15px;
						line-height: 15px;
						margin-right: 10px;
						font-size: 16px;
						border-radius: 50%;
						cursor: pointer;
						background: var(--q-color-primary);
						&:nth-child(1) {
							background: var(--q-color-panda-text-dark);
						}
					}
				}
			}
		}
	}
	.title_box {
		width: 100%;
		height: 36px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-right: 15px;
		box-sizing: border-box;
		background: var(--q-color-panda-base-light);
		.title_box_left {
			display: inline-flex;
			align-items: center;
			height: 36px;
			padding-left: 5px;
			color: var(--q-color-panda-table-text-header);
			i {
				&:hover {
					opacity: 0.8;
				}
			}
		}
		.title_box_right {
			display: inline-flex;
			justify-content: space-between;
			align-items: center;
			width: 150px;
			height: 36px;
		}
	}
	.box_content {
		padding: 10px 0 0 15px;
		.top_box {
			display: flex;
			justify-content: left;
			.top_box_left {
				width: 45%;
				margin-right: 30px;
				.top_box_left_child {
					display: flex;
					justify-content: left;
					align-items: center;
					margin-bottom: 20px;
					span {
						display: inline-flex;
						&:nth-child(1) {
							width: 110px;
							justify-content: flex-end;
							align-items: center;
							color: var(--q-color-panda-table-text-header);
							margin-right: 10px;
						}
						&:nth-child(3) {
							color: var(--q-color-primary);
							text-decoration: underline;
							cursor: pointer;
						}
					}
					.input_box {
						width: 150px;
						height: 24px;
						line-height: 24px;
						padding: 0 10px;
						border-radius: 2px;
						border: 1px solid var(--q-color-panda-them--404758);
						background-color: var(--q-color-panda-field-grey);
						margin-right: 10px;
					}
				}
			}
			.top_box_right {
				width: 45%;
				.top_box_right_child {
					display: flex;
					justify-content: left;
					align-items: center;
					margin-bottom: 20px;
					span {
						display: inline-flex;
						&:nth-child(1) {
							width: 110px;
							justify-content: flex-end;
							align-items: center;
							color: var(--q-color-panda-table-text-header);
							margin-right: 10px;
						}
					}
					.input_box {
						width: 150px;
						height: 24px;
						line-height: 24px;
						padding: 0 10px;
						border-radius: 2px;
						border: 1px solid var(--q-color-panda-them--404758);
						background-color: var(--q-color-panda-field-grey);
						margin-right: 10px;
					}
					.box_xuan {
						width: 200px;
						display: inline-flex;
						justify-content: left;
						align-items: center;
					}
				}
			}
		}
	}
}
.edit_championship_show {
	right: 0;
}

:deep( .ivu-tooltip ){
	width: initial;
}
.odds-name {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: normal;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}
.play-list {
	width: 640px;
	border: 1px solid var(--q-color-panda-table-border);
	overflow-y: auto;
}

.showLanguageDialog_icon {
    span:nth-child(1){
       display: inline-block;
       height: 20px;
       line-height: 20px;
       margin-right: 5px;
    }
    span:nth-child(2){
        display: inline-block;
        width: 15px;
        height: 15px;
        cursor: pointer;
        vertical-align: middle;
        background: url('/assets/settlement_v2_icon/edit.svg') no-repeat 100%/100% !important;
    }
    span:nth-child(2):hover{
        background: url('/assets/settlement_v2_icon/edit_a.svg') no-repeat 100%/100% !important;
    }
  }