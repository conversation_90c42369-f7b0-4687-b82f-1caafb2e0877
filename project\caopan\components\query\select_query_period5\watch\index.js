export const watcher_register = (payload) => {
  const {
    get_optional_events_num,
    show_more_query,
    current_h,
    get_serverTime,route
  } = payload;
  return [
    watch(
      () => get_optional_events_num,
      (newVal) => {
        optional_events_num.value =
          sessionStorage.getItem("optional_events_num") || 0;
      }
    ),

    watch(
      () => show_more_query,
      () => {
        emit("show_more_query_change", show_more_query.value);
      }
    ),
    watch(
      () => current_h,
      () => {
        init_query_form_date_arr();
      }
    ),
    watch(
      () => get_serverTime(),
      () => {
        let [y, m, d, h, mm, s] = format_date_base_time2(val);
        if (h == 12 && mm == "00" && s > 0 && s < 31) {
          init_query_form_date_arr();
        }
      }
    ),
    watch(
      () => route.name,
      () => {
        init_query_form_date_arr();

        if (val == "trader_manage_liveOddSupport") {
          query_form_date_tab_click(9);
        } else if (val == "trader_manage_morning_o") {
          query_form_date_tab_click(0);
        } else {
          default_data();
        }
      },
      {
        deep: true,
        immediate: true,
      }
    ),
  ];
};
