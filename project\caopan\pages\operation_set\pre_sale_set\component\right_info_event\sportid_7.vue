<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date           : 2021-01-29 20:55:56
 * @LastEditTime : 2021-06-23 23:49:55
 * @LastEditors  : Please set LastEditors
 * @Description    : 斯洛克
 * @FilePath     : /trader-tool-pro/src/pages/operation_set/pre_sale_set/component/right_info_event/sportid_7.vue
-->
<template>
  <div>
    <!-- 主客队比分统计 -->
    <div>
      <score-statistics
        :time_options="time_options"
        :statistics_code_list="statistics_code_list"
        :right_detail_obj="right_detail_obj"
        @change_ws_data_source="change_ws_data_source"
      ></score-statistics>
    </div>
    <!-- 事件流 -->
    <div
      class="row mt10x p-jcsb stHider p-lh--sm p-mb--xxs pl15x pr15x border-bottom-light border-top-light"
    >
      <Select
        class="bg-panda-field-grey"
        size="small"
        style="width: 100px; height: 24px; margin: 7px 0"
        v-model="btn_model_1"
      >
        <template v-for="item in time_options">
          <Option
            :key="item.value"
            :value="item.value"
            v-if="
              compute_i18n_type &&
              item.value <=
                (right_detail_obj.matchPeriodId > 50
                  ? 10
                  : right_detail_obj.matchPeriodId)
            "
          >
            {{ item.value == "-1" ? item.label : "Frame " + item.value }}
            <!-- 赛事阶段下拉框 -->
          </Option>
          <Option
            :key="item.value"
            :value="item.value"
            v-if="
              !compute_i18n_type &&
              item.value <=
                (right_detail_obj.matchPeriodId > 50
                  ? 10
                  : right_detail_obj.matchPeriodId)
            "
          >
            {{ item.label }}
            <!-- 赛事阶段下拉框 -->
          </Option>
        </template>
      </Select>
      <div class="flex p1-aic p-lh--lx p-gutter-mr4x">
        <!-- 当前杆得分 -->
        <img
          class="cp h18x"
          :src="
            get_icon_url({
              val: 1,
              sport_id: sport_id,
              status: get_icon_status(icon_opts.includes(1)),
            })
          "
          alt=""
          :title="$t('score_center.sportId_7_5')"
          @click="light_icon({ icon_opts, val: 1 })"
        />
        <!-- 得分 -->
        <img
          class="cp h18x"
          :src="
            get_icon_url({
              val: 2,
              sport_id: sport_id,
              status: get_icon_status(icon_opts.includes(2)),
            })
          "
          alt=""
          :title="$t('score_center.sportId_7_6')"
          @click="light_icon({ icon_opts, val: 2 })"
        />
        <!-- 犯规 -->
        <img
          class="cp h18x"
          :src="
            get_icon_url({
              val: 3,
              sport_id: sport_id,
              status: get_icon_status(icon_opts.includes(3)),
            })
          "
          alt=""
          :title="$t('score_center.sportId_7_7')"
          @click="light_icon({ icon_opts, val: 3 })"
        />
      </div>
    </div>
    <div
      class="p1-oxa p-f1 p-px--dm pande-text-788299 ml15x mr15x"
      style="height: 280px"
      v-if="event_list && event_list.length"
    >
      <div v-for="(i, index) in event_list" :key="index">
        <div v-if="i.dataSourceCode == data_source">
          <!-- 判断是否选中数据源 -->
          <div v-if="btn_model_1 == '-1' ? true : i.firstNumber == btn_model_1">
            <!-- 判断是否选中赛事阶段  只有斯洛克通过firstNumber过滤 其他赛种都是根据 matchPeriodId 过滤 -->
            <div
              v-if="
                should_show.length == 0
                  ? true
                  : should_show.includes(i.eventCode)
              "
            >
              <!-- 判断是否选中图标显示 -->
              <!--主队 -->
              <div class="flex mt5x" v-if="i.homeAway && i.homeAway == 'home'">
                <div
                  class="flex p-jcsb p-r-bgc--dark p-lh--xxs p-h--xxs"
                  style="width: 204px"
                >
                  <!--进球事件 -->
                  <span
                    v-if="
                      i.eventCode === 'snooker_score_change' ||
                      i.eventCode === 'ball_pot'
                    "
                    class="p-f1 p-ellipsis p-px--xxs"
                  >
                    <!-- 第几个进球 -->
                    <span
                      v-if="!compute_i18n_type"
                      class="p-ellipsis dib"
                      style="width: 105px"
                      :title="`第${i.remark}个${computed_event_name(
                        i.eventName
                      )}`"
                    >
                      第{{ i.remark }}个进球-{{
                        computed_event_name(i.eventName)
                      }}
                    </span>
                    <span
                      v-else
                      class="p-ellipsis dib"
                      style="width: 105px"
                      :title="`${i.remark} ' goal-${computed_event_name(
                        i.eventName
                      )}`"
                    >
                      {{ i.remark }} ' goal-{{
                        computed_event_name(i.eventName)
                      }}
                    </span>
                  </span>
                  <span
                    v-if="
                      i.eventCode === 'snooker_score_change' ||
                      i.eventCode === 'ball_pot'
                    "
                    class="p-dif p-jcc p-fb--sm h20x"
                    style="width: 90px"
                  >
                    <!-- 当前杆得分-->
                    <span style="width: 25px" class="tac">{{
                      i.addition2
                    }}</span>
                    <img
                      src="/svg/7/7_1_l.svg"
                      alt=""
                      style="height: 13px; margin-top: 3px"
                    />
                    <span style="width: 25px" class="tac">0</span>
                    <!-- 局比分-->
                    <span style="width: 25px" class="tac">{{
                      i.homeFirstNumber
                    }}</span>
                    <img
                      src="/svg/7/7_2_l.svg"
                      alt=""
                      style="height: 13px; margin-top: 3px"
                    />
                    <span style="width: 25px" class="tac">{{
                      i.awayFirstNumber
                    }}</span>
                  </span>
                  <!-- 犯规事件 -->
                  <span
                    v-if="i.eventCode === 'snooker_foul'"
                    class="p-f1 p-ellipsis p-px--xxs"
                  >
                    <!-- 第几个犯规 -->
                    <span
                      v-if="!compute_i18n_type"
                      class="p-ellipsis dib"
                      style="width: 105px"
                      :title="`第${i.remark}个${computed_event_name(
                        i.eventName
                      )}`"
                    >
                      第{{ i.remark }}个{{ computed_event_name(i.eventName) }}
                    </span>
                    <span
                      v-else
                      class="p-ellipsis dib"
                      style="width: 105px"
                      :title="`${i.remark} ' ${computed_event_name(
                        i.eventName
                      )}`"
                    >
                      {{ i.remark }} ' {{ computed_event_name(i.eventName) }}
                    </span>
                  </span>
                  <span
                    v-if="i.eventCode === 'snooker_foul'"
                    class="p-dif p-jcc p-fb--sm h20x"
                    style="width: 90px"
                  >
                    <!-- 犯规-->
                    <span style="width: 25px" class="tac">{{
                      i.eventHomeNumber
                    }}</span>
                    <img
                      src="/svg/7/7_3_l.svg"
                      alt=""
                      style="height: 13px; margin-top: 3px"
                    />
                    <span style="width: 25px" class="tac">{{
                      i.eventAwayNumber
                    }}</span>
                    <!-- 局比分-->
                    <span style="width: 25px" class="tac">{{
                      i.homeFirstNumber
                    }}</span>
                    <img
                      src="/svg/7/7_2_l.svg"
                      alt=""
                      style="height: 13px; margin-top: 3px"
                    />
                    <span style="width: 25px" class="tac">{{
                      i.awayFirstNumber
                    }}</span>
                  </span>
                </div>
                <!-- 三角箭头 -->
                <q-icon
                  name="play_arrow"
                  style="
                    font-size: 35px;
                    height: 20px;
                    transform: translateX(-12px);
                  "
                  class="p-r-tc--dark"
                ></q-icon>
              </div>
              <!--客队 -->
              <div
                class="flex p-jcfe mt5x"
                v-if="i.homeAway && i.homeAway == 'away'"
              >
                <div
                  class="flex p-jcsb p-r-bgc--dark p-lh--xxs p-h--xxs"
                  style="width: 204px"
                >
                  <!--进球事件 -->
                  <span
                    v-if="
                      i.eventCode === 'snooker_score_change' ||
                      i.eventCode === 'ball_pot'
                    "
                    class="p-dif p-jcc p-fb--sm"
                    style="width: 90px"
                  >
                    <!-- 局比分-->
                    <span style="width: 25px" class="tac">{{
                      i.homeFirstNumber
                    }}</span>
                    <img
                      src="/svg/7/7_2_l.svg"
                      alt=""
                      style="height: 13px; margin-top: 3px"
                    />
                    <span style="width: 25px" class="tac">{{
                      i.awayFirstNumber
                    }}</span>
                    <!-- 当前杆得分-->
                    <span style="width: 25px" class="tac">0</span>
                    <img
                      src="/svg/7/7_1_l.svg"
                      alt=""
                      style="height: 13px; margin-top: 3px"
                    />
                    <span style="width: 25px" class="tac">{{
                      i.addition2
                    }}</span>
                  </span>
                  <span
                    v-if="
                      i.eventCode === 'snooker_score_change' ||
                      i.eventCode === 'ball_pot'
                    "
                    class="p-f1 p-ellipsis p-px--xxs"
                  >
                    <!-- 第几个进球 -->
                    <span
                      v-if="!compute_i18n_type"
                      class="fr p-ellipsis dib"
                      style="text-align: right; width: 105px"
                      :title="`第${i.remark}个${computed_event_name(
                        i.eventName
                      )}`"
                    >
                      第{{ i.remark }}个进球-{{
                        computed_event_name(i.eventName)
                      }}
                    </span>
                    <span
                      v-else
                      class="fr p-ellipsis dib"
                      style="text-align: right; width: 105px"
                      :title="`${i.remark} ' goal-${computed_event_name(
                        i.eventName
                      )}`"
                    >
                      {{ i.remark }} ' goal-{{
                        computed_event_name(i.eventName)
                      }}
                    </span>
                  </span>
                  <!-- 犯规事件 -->
                  <span
                    v-if="i.eventCode === 'snooker_foul'"
                    class="p-dif p-jcc p-fb--sm h20x"
                    style="width: 90px"
                  >
                    <!-- 局比分-->
                    <span style="width: 25px" class="tac">{{
                      i.homeFirstNumber
                    }}</span>
                    <img
                      src="/svg/7/7_2_l.svg"
                      alt=""
                      style="height: 13px; margin-top: 3px"
                    />
                    <span style="width: 25px" class="tac">{{
                      i.awayFirstNumber
                    }}</span>
                    <!-- 犯规-->
                    <span style="width: 25px" class="tac">{{
                      i.eventHomeNumber
                    }}</span>
                    <img
                      src="/svg/7/7_3_l.svg"
                      alt=""
                      style="height: 13px; margin-top: 3px"
                    />
                    <span style="width: 25px" class="tac">{{
                      i.eventAwayNumber
                    }}</span>
                  </span>
                  <span
                    v-if="i.eventCode === 'snooker_foul'"
                    class="p-f1 p-ellipsis p-px--xxs"
                  >
                    <!-- 第几个犯规 -->
                    <span
                      v-if="!compute_i18n_type"
                      class="fr p-ellipsis dib"
                      style="text-align: right; width: 105px"
                      :title="`第${i.remark}个${computed_event_name(
                        i.eventName
                      )}`"
                    >
                      第{{ i.remark }}个{{ computed_event_name(i.eventName) }}
                    </span>
                    <span
                      v-else
                      class="fr p-ellipsis dib"
                      style="text-align: right; width: 105px"
                      :title="`${i.remark} ' ${computed_event_name(
                        i.eventName
                      )}`"
                    >
                      {{ i.remark }} ' {{ computed_event_name(i.eventName) }}
                    </span>
                  </span>
                </div>
                <q-icon
                  name="play_arrow"
                  style="
                    font-size: 35px;
                    height: 20px;
                    transform: translateX(12px) rotate(0.5turn);
                  "
                  class="p-r-tc--dark p-ro1"
                ></q-icon>
              </div>
              <!-- 赛事阶段分割线 -->
              <div
                class="dashed"
                v-if="
                  computed_matchPeriodId_show(i.firstNumber) &&
                  period_id[i.firstNumber] == index
                "
              >
                <span class="after"></span>
                <span class="title"
                  >{{ computed_matchPeriodId(i.firstNumber) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row p-jcc pande-text-788299" v-else>
      <span>{{ $t("score_center.text_7") }}</span
      ><!-- 无实时事件流 -->
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { i18n_t } from "src/output/common/project-common.js";
import scoreStatistics from "src/pages/operation_set/pre_sale_set/component/right_info_event/module/score_statistics.vue"; // 统计比分组件
import { project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_componsable_fn } from "project/caopan/pages/operation_set/pre_sale_set/component/right_info_event/mixins/componsable/index.js";
import { project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_variable_componsable_fn } from "project/caopan/pages/operation_set/pre_sale_set/component/right_info_event/mixins/componsable/variable.js";

const props = defineProps({
  right_detail_obj: {
    type: Object, // 指定为对象类型
    required: true, // 如果是必传属性
  },
  sport_id: {
    type: [String, Number], // 可以是字符串或数字
    required: true,
  },
  event_list: {
    type: Array, // 指定为数组类型
    default: () => [], // 默认值为空数组
  },
  period_id: {
    type: [String, Number], // 可以是字符串或数字
    default: null, // 默认值为 null
  },
});

const time_options = ref([
  { label: `${i18n_t("common.full_time")}`, value: "-1" },
  { label: "第1局", value: "1" }, // 斯洛克  英文取的 'Frame' + value
  { label: "第2局", value: "2" },
  { label: "第3局", value: "3" },
  { label: "第4局", value: "4" },
  { label: "第5局", value: "5" },
  { label: "第6局", value: "6" },
  { label: "第7局", value: "7" },
  { label: "第8局", value: "8" },
  { label: "第9局", value: "9" },
  { label: "第10局", value: "10" },
  { label: "第11局", value: "11" },
  { label: "第12局", value: "12" },
  { label: "第13局", value: "13" },
  { label: "第14局", value: "14" },
  { label: "第15局", value: "15" },
  { label: "第16局", value: "16" },
  { label: "第17局", value: "17" },
  { label: "第18局", value: "18" },
  { label: "第19局", value: "19" },
  { label: "第20局", value: "20" },
  { label: "第21局", value: "21" },
  { label: "第22局", value: "22" },
  { label: "第23局", value: "23" },
  { label: "第24局", value: "24" },
  { label: "第25局", value: "25" },
  { label: "第26局", value: "26" },
  { label: "第27局", value: "27" },
  { label: "第28局", value: "28" },
  { label: "第29局", value: "29" },
  { label: "第30局", value: "30" },
  { label: "第31局", value: "31" },
  { label: "第32局", value: "32" },
  { label: "第33局", value: "33" },
  { label: "第34局", value: "34" },
  { label: "第35局", value: "35" },
]);
//所有的比分统计类型
const statistics_code_list = ref([
  { key: "matchScore", name: `${i18n_t("score_center.sportId_7_1")}` }, //盘比分
  { key: "setScore", name: `${i18n_t("score_center.sportId_7_2")}` }, //局比分
  { key: "snookerFoul", name: `${i18n_t("score_center.sportId_7_3")}` }, //犯规次数
  {
    key: "highestSingleShot",
    name: `${i18n_t("score_center.sportId_7_4")}`,
  }, //单杆最高
]);

const map_opts = ref({
  1: ["snooker_score_change"],
  2: ["ball_pot"],
  3: ["snooker_foul"],
});

const event_keys = ref([
  { key: ["snooker_score_change"] }, // 得分
  { key: ["ball_pot"] }, // 进球
  { key: ["snooker_foul"] }, // 犯规
]);

const payload =
  project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_variable_componsable_fn(
    {
      map_opts,
    }
  );
const { icon_opts, btn_model_1, compute_i18n_type, data_source, should_show } =
  payload;

const {
  change_ws_data_source,
  get_icon_url,
  get_icon_status,
  light_icon,
  computed_matchPeriodId_show,
  computed_matchPeriodId,
  computed_event_name,
} =
  project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_componsable_fn(
    {
      time_options,
      statistics_code_list,
      map_opts,
      event_keys,
      right_detail_obj: props.right_detail_obj,
      sport_id: props.sport_id,
      event_list: props.event_list,
      period_id: props.period_id,
    }
  );
</script>

<style lang="scss" scoped>
@import "src/pages/operation_set/pre_sale_set/component/right_info_event/common.scss";
</style>
