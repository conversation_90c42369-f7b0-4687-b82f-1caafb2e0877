import { onBeforeMount ,onMounted } from "vue"
import { change_pre_risk_manager_code_ok } from "project/caopan/pages/match/sale/api-request/change_pre_risk_manager_code_ok.js"
import { change_weight } from "project/caopan/pages/match/sale/api-request/change_weight.js"
import { handle_yjks_mock } from "project/caopan/pages/match/sale/api-request/handle_yjks_mock.js"
import { init_tabledata } from "project/caopan/pages/match/sale/api-request/init_tabledata.js"
import { mts_sold } from "project/caopan/pages/match/sale/api-request/mts_sold.js"
import { save_build_match } from "project/caopan/pages/match/sale/api-request/save_build_match.js"
import { sort_save } from "project/caopan/pages/match/sale/api-request/sort_save.js"

import { init_right_detail_status } from "src/output/common/project-common";
import {
  edit_sort_status,
  set_source_weight,
  edit_match_sale,
  get_tablecolumns,
  remove_team,
select_team,
build_match_click,
// get_tablecolumns,
change_source_weight,
// set_source_weight,
// change_preRiskManagerCode,
init_data,
submit_success,
close_right_info,
// edit_match_sale,
set_footer_fn,
// edit_sort_status,
compute_init_tabledata_params,
compute_scrollarea_style,

} from "project/caopan/pages/match/sale/module/index.js"
import {  log_click,compute_main_component_style,
  compute_middle_component_style,} from "project/caopan/pages/match/sale/module/others.js"
import { get_router_cache_value_by_path } from "project/saicheng/componsable/router_cache/handler/module/index.js";

import {
  set_sportRegion_array_force,
  set_dataSource_obj,
  set_optional_events_num,
} from "src/output/common/store-common.js";
import { watcher_register } from "project/caopan/pages/match/sale/watch/index.js"
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";

export const project_caopan_pages_match_sale_componsable_index_componsable_fn = (payload) => {


  // const payload = {
  //   ...raw_payload
  // }
  const { get_tab, select_sportId, tablecolumns, visibleColumns } = payload;
  src_componsable_util_watcher_register_componsable_fn(
    watcher_register({
      ...payload,
    })
  );
  onBeforeMount(() => {
    let sportid = get_router_cache_value_by_path(payload, 'sportId')  //获取sportId 球类id  早盘赛事、滚球赛事记住功能自动搜索
    select_sportId.value = sportid ? sportid : select_sportId.value
    set_sportRegion_array_force();
    set_dataSource_obj();
    get_tab();
    get_tablecolumns(payload)
    tablecolumns.value.forEach((item) => {
      if (item.name != 'sortValue')
        visibleColumns.value[visibleColumns.value.length] = item.name;
    })

  })
  onMounted(() => {
    init_right_detail_status(payload,2);

    compute_main_component_style(payload);
    compute_scrollarea_style(payload)
    // when_window_size_info_change
    // compute_middle_component_style(payload);
  })
  return {
    change_pre_risk_manager_code_ok: () => change_pre_risk_manager_code_ok(payload),
    change_weight: () => change_weight(payload),
    handle_yjks_mock: () => handle_yjks_mock(payload),
    init_tabledata: () => init_tabledata(payload),
    mts_sold: () => mts_sold(payload),
    save_build_match: () => save_build_match(payload),
    sort_save: () => sort_save(payload),

    edit_sort_status: (obj) => edit_sort_status(payload, obj),
    set_source_weight: () => set_source_weight(payload),
    edit_match_sale: (val, item) => edit_match_sale(payload, val, item),

    remove_team: (teamType) => remove_team(payload, teamType),
    select_team: (dataTeamType, buildTeamType) => select_team(payload, dataTeamType, buildTeamType),
    build_match_click:(row) => build_match_click(payload, row),
    get_tablecolumns,
    change_source_weight:( item) => change_source_weight(payload, item),
    // set_source_weight,
    change_preRiskManagerCode:(item) => change_preRiskManagerCode(payload, item),
    init_data:(arr) => init_data(arr),
    submit_success: (val) => submit_success(payload,val),
    close_right_info: () => close_right_info(payload),
    // edit_match_sale,
    set_footer_fn:( label) => set_footer_fn(payload, label),
    // edit_sort_status,
    compute_init_tabledata_params:(i) => compute_init_tabledata_params(payload, i),
    compute_scrollarea_style:() => compute_scrollarea_style(payload),
    log_click:(row) => log_click(payload, row),
    compute_main_component_style:() => compute_main_component_style(payload),
    compute_middle_component_style:() => compute_middle_component_style(payload),

  }
}