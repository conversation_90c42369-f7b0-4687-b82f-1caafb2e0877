import { api_operate } from "src/api/index.js";
import { Message, lodash, i18n_t } from "src/output/common/project-common.js";
/**
 * @description:  早盘滚球置顶
 * @param {row,val} 数据 1早盘2滚球
 */
export const top_morning = (row, val) => {
  console.log(row, val);
  let param = [
    {
      topRange: val,
      top_type: 1,
      tid: Number(row.id),
      enableFlag: "N",
      sortNo: 9999, // 用于排序值9999 在热门置顶排序最后一条
    },
  ];
  api_operate.addOrUpdate(param).then((res) => {
    const { code, msg, data } = lodash.get(res, "data", {});
    if (code == 200) {
      Message.info(`${msg}`);
    } else {
      Message.error(i18n_t("menus.lm_10.leagues.popular13"));
    }
    console.log(res);
  });
};
