<!--
 * @FilePath: /project/caopan/pages/operation_set/league_parameter_set/league_set_template_edit/components/football/ao_set/index.vue
 * @Description:AO弹窗
-->
<template>
  <div class="full-height use-ivew">
    <div class="row">
      <div class="col-12 border-top-light2">
        <div id="match-resource-query" class="full-width border-bottom-light2">
          <q-tabs
            v-if="tabs.length > 1"
            v-model="tab_change"
            dense
            indicator-color="transparent"
            active-color="white"
            align="justify"
            class="text-grey-6"
          >
            <q-tab
              v-for="tab in tabs"
              no-caps
              :key="tab.name"
              :name="tab.name"
              :label="tab.label"
              class="border-right-light2 text-panda-text-light"
            >
            </q-tab>
          </q-tabs>
        </div>
        <component
          :is="tab_change"
          :data_type="data_type"
          :ao_data="ao_data"
          :set="set"
          :random_number="random_number"
          @ao_show="ao_show"
          @ao_updata="updata_ao"
          :key="tab_change"
          ref="ao_data_margin"
          :rc="rc"
          :machine="machine"
        >
        </component>
      </div>
    </div>
  </div>
</template>

<script>
//import mixins from "project/caopan/mixins/index.js";
import Regular from "project/caopan/pages/operation_set/league_parameter_set/league_set_template_edit/components/football/ao_set/ao_data_set.vue";
import Extra from "project/caopan/pages/operation_set/league_parameter_set/league_set_template_edit/components/football/ao_set/ao_data_set.vue";
import spec_event from "project/caopan/pages/operation_set/league_parameter_set/league_set_template_edit/components/football/ao_set/set_aoswich.vue";
import liner_margrn from "project/caopan/pages/operation_set/league_parameter_set/league_set_template_edit/components/football/ao_set/liner_margin.vue";
import auto_open from "project/caopan/pages/operation_set/league_parameter_set/league_set_template_edit/components/football/ao_set/auto_open.vue";
  mixins: [...mixins],
  name: "index",
  props: {
    ao_data: {},
    set: {},
    random_number: {},
    rc: {}, //是否为rc玩法
    machine: false,
  },

  components: {
    Regular,
    Extra,
    spec_event, // Spec Event
    liner_margrn, // Liner Margin
    auto_open, // Auto Open
  },
  mounted() {},
 
  computed: {},


</script>

<style lang="scss" scoped>
#match-resource-query {
  :deep( .q-tabs ){
    height: 40px;
    flex: none;
    display: inline-block;
    .q-tab__label {
      font-size: 13px;
    }
  }
  :deep( .q-tabs--dense ){
    .q-tab {
      height: 40px;
      min-height: 40px;
      padding: 0 24px;
    }
  }
  .q-tab--active {
    background-color: var(--q-color-panda-bg-modal-mask) !important;
    color: var(--q-color-panda-primary) !important;
  }
}
</style>
