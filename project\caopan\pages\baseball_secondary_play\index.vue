<!--
 * <AUTHOR> martin
 * @Date         : 2021-03-19 15:01:10
 * @Description  : 棒球操盘次要玩法
 * @FilePath: /project/caopan/pages/baseball_secondary_play/index.vue
-->
<template>
  <div class="use-iview-ob play-content" style="min-width: 1700px">
    <!-- 比分扳 滚球 -->
    <div class="row q-pa-xs text-panda-text-light q-mb-xs bg-panda-dark-dark" v-if="route.query.liveType">
      <div class="score-item" style="width: 20px">
        <div style="height: 18px; width: 20px"></div>
        <div style="height: 18px; width: 20px">
          <img
            data-v-525142b5=""
            src="/image/sport/panda_img_sport_bangq.svg"
            alt=""
            style="width: 10px; height: 10px"
            v-if="mServesFirst == 'home'"
          />
          <!-- <span class="panda_icon_sport_snk q-mr-xs" style="font-size:12px;" ></span> -->
        </div>
        <div class="text-center">
          <img
            data-v-525142b5=""
            src="/image/sport/panda_img_sport_bangq.svg"
            alt=""
            style="width: 10px; height: 10px"
            v-if="mServesFirst == 'away'"
          />
          <!-- <span class="panda_icon_sport_snk q-mr-xs q-mt-xs" style="font-size:12px;" v-if="servesFirst=='away'"></span> -->
        </div>
      </div>
      <!-- 主客队 -->
      <div class="score-item" style="width: 100px">
        <div class="pande-text-788299">
          {{ i18n_t("expectations.text10") }}
        </div>
        <div class="row q-pr-md text-red ellipsis">
          {{ language_value(teamList["home"]) }}
        </div>
        <div class="row q-pr-md ellipsis" style="border-top: 1px solid #404040">
          {{ language_value(teamList["away"]) }}
        </div>
      </div>
      <!-- {{ scoreList }} -->
      <!-- 全场比分 -->
      <div class="score-item" style="width: 50px">
        <div class="text-red">{{ i18n_t("traderTable.qTable_40") }}</div>
        <div class="text-red text-center">
          {{ match_score ? match_score.split(":")[0] : 0 }}
        </div>
        <div class="text-red text-center" style="border-top: 1px solid #404040">
          {{ match_score ? match_score.split(":")[1] : 0 }}
        </div>
      </div>
      <div v-for="item in roundType" :key="item" class="score-item" :class="setNum == item ? 'text-panda-text-primary' : ''">
        <div class="pande-text-788299 text-center" :class="setNum == item ? 'text-panda-text-primary' : ''">
          {{ i18n_t("report_common.nth") }}{{ item }}{{ i18n_t("saleTable.la_9") }}
        </div>
        <div class="text-center">{{ get_home_score(1, item) }}</div>
        <div class="text-center" style="border-top: 1px solid #404040">
          {{ get_home_score(2, item) }}
        </div>
      </div>
      <div class="score-item" style="width: 70px">
        <div class="text-blue text-center">{{ i18n_t("event.add.w99") }}</div>
        <div class="text-blue text-center">
          {{ get_home_score(1, setNum) }}
        </div>
        <div class="text-blue text-center" style="border-top: 1px solid #404040">
          {{ get_home_score(2, setNum) }}
        </div>
      </div>
    </div>
    <!--场次 玩法集-->
    <div class="row item-center" style="background-color: #333333" v-if="!route.query.playSetId">
      <!--根据后端定义的玩法集id 从i18里面组装对应的玩法名称-->
      <div
        v-for="index in playSetList"
        :key="`playset_` + index"
        class="text-panda-text-light row playset-item cursor-pointer q-mr-sm"
        :class="checkedPlaysetId.includes(Number(index)) || checkedPlaysetId.length == roundType + 1 ? 'active' : ''"
        @click="action_play_set_id(index)"
      >
        <div class="text-center">
          {{ get_play_set_name(index) }}
        </div>
        <div class="text-center" v-if="checkedPlaysetId.includes(Number(index))">
          <img
            @click.stop="
              handle_click_log(null, {
                matchId: route.query.matchId,
                sportId: route.query.sportId,
                objectId: index,
              })
            "
            class="ml5x vatb cp"
            style="width: 14px; height: 14px"
            src="/assets/icon/log.svg"
            alt="log"
            srcset=""
          />
        </div>
        <!--当前选中玩法集下表才显示对应的开关锁，全部不显示-->
        <div v-if="checkedPlaysetId.includes(Number(index)) && checkedPlaysetId.length < 2" class="action-playset">
          <!-- 0开 2关 1封 11锁 -->
          <span
            @click.stop="
              change_play_status(
                0,
                get_play_childCategory(
                  dataList.find(items => items.playSetId == index),
                  index
                ),
                get_params_num(index)
              )
            "
            :title="i18n_t('operate_match_status.operate_match_status_0')"
            class="action-font q-ml-sm panda_icon_caopan_7"
          >
          </span>
          <span
            @click.stop="
              change_play_status(
                2,
                get_play_childCategory(
                  dataList.find(items => items.playSetId == index),
                  index
                ),
                get_params_num(index)
              )
            "
            :title="i18n_t('operate_match_status.operate_match_status_2')"
            class="action-font q-ml-sm panda_icon_caopan_8"
          >
          </span>
          <span
            @click.stop="
              change_play_status(
                1,
                get_play_childCategory(
                  dataList.find(items => items.playSetId == index),
                  index
                ),
                get_params_num(index)
              )
            "
            :title="i18n_t('operate_match_status.operate_match_status_1')"
            class="action-font q-ml-sm panda_icon_caopan_10"
          >
          </span>
          <span
            @click.stop="
              change_play_status(
                11,
                get_play_childCategory(
                  dataList.find(items => items.playSetId == index),
                  index
                ),
                get_params_num(index)
              )
            "
            :title="i18n_t('operate_match_status.operate_match_status_11')"
            class="action-font q-ml-sm panda_icon_caopan_3"
          >
          </span>
        </div>
      </div>
      <div style="margin-top: 3px">
        <iSelect class="bg-panda-field-grey" size="small" v-model="matchVolumeId" @on-change="update_bet_amount_type">
          <iOption v-for="item in matchVolumeList" :key="item.value" :value="item.value" :label="item.label"> </iOption>
        </iSelect>
      </div>
    </div>
    <iCollapse v-model="value2" simple>
      <iPanel v-for="item in computed_dataList" :key="item.playSetId" :name="`${item.playSetId}`">
        <span class="text-panda-text-light">
          <span>
            <!-- 场次 -->
            <span class="q-ml-sm">
              {{ get_play_set_name(item.playSetId) }}
            </span>
            <!-- <span class="q-ml-sm">{{ item.playSetId }}</span> -->
            <span class="q-ml-xs pande-text-gold">
              <!--开赛时间-->
              <span>{{ get_times(beginTime, "m", "/").slice(5) }}</span>
              <!-- 早盘 -->
              <span>
                <!--为滚球盘时显示-->
                <p v-if="route.query.liveType" class="fl">
                  <span v-if="matchStatus == 1 && !period">
                    <!-- 即将开赛 -->
                    {{ i18n_t("matchPeriodId.matchPeriodId_other1") }}
                  </span>
                  <span v-else-if="matchSnapshot == 1">
                    <!-- 赛前15分钟 -->
                    {{ i18n_t("traderTable.text99") }}

                    <div v-if="secondsMatchStart">
                      <span class="panda-text-orange">
                        {{ matchPstart() }}
                      </span>
                    </div>
                    <!-- <timer :secondsMatchStart="secondsMatchStart" :props="{ matchSnapshot, matchStatus, period }" /> -->
                  </span>
                  <span v-else>
                    <!-- 滚球 -->
                    <span>
                      <!-- 阶段、局数 -->
                      <!-- {{ i18n_t('report_common.nth') }}{{setNum}}{{ i18n_t('saleTable.la_9') }} / {{ roundType }} -->
                      {{ i18n_t(`matchPeriodId.matchPeriodId3_${period}`) }}
                    </span>
                    <span class="panda-text-orange2 q-ml-xs">
                      {{ period_map[period] }}
                    </span>
                  </span>
                </p>
                <!-- <p v-else class="fl">{{get_times(beginTime,'m','/').slice(5)}}</p> -->
              </span>
            </span>
          </span>
          <!-- 主队 pande-text-red1 -->
          <span class="q-ml-sm">
            <!-- 主 -->
            {{ language_value(teamList["home"]) }} ( {{ i18n_t("secondary_play_1.s1") }})
          </span>
          <!-- 比分 -->
          <span v-if="item.playSetId == '10018'" class="q-mx-xs">
            <span v-if="route.query.liveType">
              <span class="text-red">
                <!-- 红牌 -->
                {{ i18n_t("orderCenter.event6") }}：
                {{ item.score1 ? item.score1 : "0:0" }}
              </span>
              <span class="q-ml-sm" style="color: #ffb001">
                <!-- 黄牌： -->
                {{ i18n_t("orderCenter.event7") }}：
                {{ item.score2 || "0:0" }}
              </span>
              <span class="q-ml-sm">
                <!-- 罚牌： -->
                {{ i18n_t("orderCenter.event14") }}：
                {{ get_10018_score(item.score1, item.score2) }}
              </span>
            </span>
            <span v-else> VS </span>
          </span>
          <span v-else class="q-mx-xs">
            {{ item.score ? item.score : route.query.liveType ? "0:0" : "VS" }}
          </span>
          <!-- 客队 -->
          <span> {{ language_value(teamList["away"]) }} </span>
          <!--数据源为MTS时显示-->
          <span v-if="is_new_trader(riskManagerCode)" :class="trader_icon(riskManagerCode, 1)" class="p-fs--md vatb" style="margin: 3px 0 0 0">
            <!-- mts操盘图标 -->
          </span>
          <span v-else style="margin: -3px 15px 0 15px">
            <!--操盘模式为M+A时显示-->
            <!--tradeType 操盘模式 [0, 1]:M+A [0]:A [1]: M-->
            <span v-if="tradeType && tradeType.length >= 2" class="p-fs--md vatb panda_new_cp_icon_13">
              <!-- 自动+手动-->
            </span>
            <!-- 自动图标-->
            <span v-if="tradeType && tradeType.includes(0) && tradeType.length == 1" class="p-fs--md vatb panda_new_cp_icon_11"> </span>
            <!-- 手动-->
            <span v-if="tradeType && tradeType.includes(1) && tradeType.length == 1" class="p-fs--md vatb panda_new_cp_icon_10"> </span>
          </span>
          <span>
            <!-- 0开 2关 1封 11锁 -->
            <span
              @click.stop="change_play_status(0, get_play_childCategory(item, item.playSetId), get_params_num(item.playSetId))"
              :title="i18n_t('operate_match_status.operate_match_status_0')"
              class="action-font q-ml-sm panda_icon_caopan_7"
            >
            </span>
            <span
              @click.stop="change_play_status(2, get_play_childCategory(item, item.playSetId), get_params_num(item.playSetId))"
              :title="i18n_t('operate_match_status.operate_match_status_2')"
              class="action-font q-ml-sm panda_icon_caopan_8"
            >
            </span>
            <span
              @click.stop="change_play_status(1, get_play_childCategory(item, item.playSetId), get_params_num(item.playSetId))"
              :title="i18n_t('operate_match_status.operate_match_status_1')"
              class="action-font q-ml-sm panda_icon_caopan_10"
            >
            </span>
            <span
              @click.stop="change_play_status(11, get_play_childCategory(item, item.playSetId), get_params_num(item.playSetId))"
              :title="i18n_t('operate_match_status.operate_match_status_11')"
              class="action-font q-ml-sm panda_icon_caopan_3"
            >
            </span>
          </span>
          <!-- <span>基准分： {{match_score}}</span> -->
        </span>
        <!-- 独立窗口 -->
        <span
          title="独立窗口"
          @click.stop="new_window(item.playSetId)"
          class="panda_open_c_o q-mr-lg cursor-pointer position-relative"
          style="font-size: 16px; float: right; margin-top: 10px"
        ></span>
        <!-- 展开收起 -->
        <span
          class="position-relative font1"
          :class="[value2.includes(`${item.playSetId}`) ? 'panda_icon_add_light' : 'panda_icon_remove_light']"
          style="float: right; top: 10px; right: 10px"
        ></span>
        <div>
          <component
            class="component"
            :is="`playSet`"
            @emit_dataSource_model_show="dataSource_model_show"
            @emit_dialogConfirmOpen_show="dialogConfirmOpen_show"
            @handle_click_log="handle_click_log"
            :playSet="item"
            :playSetId="item.playSetId"
            :playSetScore="item.score"
            :playSetScore1="item.score1"
            :playSetScore2="item.score2"
            :secondsMatchStart="0"
            :operate_match_status="operate_match_status"
            :match_score="match_score"
            :matchStatus="matchStatus"
            :matchStartTime="get_times(beginTime)"
            :matchSnapshot="matchSnapshot"
            :period="period"
            :riskManagerCode="riskManagerCode"
            :matchVolumeId="matchVolumeId"
            v-bind="$attrs"
          />
        </div>
      </iPanel>
    </iCollapse>
    <!-- 数据源弹窗 -->
    <q-dialog v-model="dataSource_model" persistent transition-show="scale" transition-hide="scale">
      <dialog-toggle-data-source
        v-drag
        @change_dataSource="change_dataSource"
        :data_loading="data_loading"
        :matchId="Number(route.query.matchId)"
        :dataSource_old="dataSourceCode"
      />
    </q-dialog>
    <!-- 确认基准分弹窗 -->
    <q-dialog v-model="open_show" persistent transition-show="scale" transition-hide="scale">
      <dialog-confirm-open :score="playSetScore" :header="true" @handle_cancle="handle_cancle" @handle_confirm="handle_confirm"></dialog-confirm-open>
    </q-dialog>
    <!-- 日志记录弹窗 -->
    <q-dialog v-model="logPopup" transition-show="scale" transition-hide="scale">
      <LogPopup :params="log_params" :columnsType="1" v-drag></LogPopup>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { i18n_t, language_value, is_new_trader, trader_icon } from "src/output/common/project-common.js";
import { src_componsable_global_variable_componsable_fn } from "src/output/common/componsable-common.js";
import { get_times } from "src/output/common/project-common.js";
import LogPopup from "project/caopan/components/table/log_popup/log_popup.vue";

const global_obj = src_componsable_global_variable_componsable_fn();

const { router, route, $q } = global_obj;

import { project_caopan_pages_baseball_secondary_play_config_common_componsable_fn } from "project/caopan/pages/baseball_secondary_play/config/common/componsable/index.js";
import { project_caopan_pages_baseball_secondary_play_config_common_componsable_variable_fn } from "project/caopan/pages/baseball_secondary_play/config/common/componsable/variable.js";

import { project_caopan_pages_baseball_secondary_play_componsable_fn } from "project/caopan/pages/baseball_secondary_play/componsable/index.js";
import { project_caopan_pages_baseball_secondary_play_componsable_variable_fn } from "project/caopan/pages/baseball_secondary_play/componsable/variable.js";

const common_state = project_caopan_pages_baseball_secondary_play_config_common_componsable_variable_fn();

const state = project_caopan_pages_baseball_secondary_play_componsable_variable_fn({ route });
const {
  MatchStart,
  flag_,
  params,
  dataList,
  playSetList,
  matchVolumeId,
  value2,
  teamList,
  matchSnapshot,
  secondsMatchStart,
  period,
  matchStatus,
  operate_match_status,
  eventCode,
  beginTime,
  data_loading,
  dataSourceCode,
  dataSourceCode_playId,
  dataSource_model,
  postIndex,
  tradeType,
  match_score,
  open_show,
  open_show_params,
  playSetScore,
  checkedPlaysetId,
  riskManagerCode,
  matchVolumeList,
  setNum,
  roundType,
  scoreList,
  servesFirst,
  mServesFirst,
  period_map,
  logPopup,
  log_params,
  computed_dataList,
} = state;

const raw_state = { route, ...common_state, ...state };

project_caopan_pages_baseball_secondary_play_config_common_componsable_fn(raw_state);
const {
  get_home_score,
  action_play_set_id,
  handle_click_log,
  change_play_status,
  get_play_childCategory,
  get_params_num,
  update_bet_amount_type,
  new_window,
  dataSource_model_show,
  dialogConfirmOpen_show,
  handle_cancle,
  handle_confirm,
  change_dataSource,
  get_10018_score,
  get_play_set_name,
  matchPstart,
} = project_caopan_pages_baseball_secondary_play_componsable_fn(raw_state);
</script>

<style lang="scss" scoped>
@import url("project/caopan/pages/baseball_secondary_play/css/index-scoped.scss");
</style>
