<!--
 * @FilePath: /project/caopan/components/query/select_sport_type/select_sport_type2.vue
 * @Description    :
-->
<template>
  <!-- 头部 体育大类条件 -->
  <div
    class="row bg-panda-sport-type-primary"
    style="z-index:1000"
  >
    <div class="row select-sport-type" >
      <div
        v-for="(items, indexs) in sport_type_left || []"
        :key="`sport_type_${indexs}`"
        @click="sport_type_click(items, indexs)"
        class="cursor-pointer border-panda-dark-dark box-sizing panda-tab-like-tab"
      >
        <div class="row p1-jcc p1-aic" style="height:100%">
          <span
            class="panda-tab-like-tab-label panda-tab-centen-tab"
            :class="
              select_sportId == items.id
                ? 'panda-icon-active'
                : 'panda-query-date-tab-label'
            "
          >{{ items.title }}</span>
        </div>
      </div>
      <i-dropdown trigger="click">
        <a href="javascript:void(0)" style="color: var(--q-color-panda-text-light);" class=" panda-tab-like-tab-label panda-tab-centen-tab"
        :class="
              sport_type_more_class(select_sportId)
                ? 'panda-icon-active'
                : 'panda-query-date-tab-label'
            "
        >
           {{i18n_t('common.more')}}
           <!-- 更多 -->
            <i-icon type="ios-arrow-down"></i-icon>
        </a>
        <i-dropdownMenu #list class="row ">
            <i-dropdownItem class="col-4" v-for="(i,index) in sport_type_more" :key="`${i.id}sport_type_more`"
            >
              <div
               @click="sport_type_click(i, index)"
                :class="i.id == select_sportId? 'dropdown-item-active':''"
              > {{i.title}}</div>

           </i-dropdownItem>
        </i-dropdownMenu>
    </i-dropdown>
    </div>
  </div>
</template>
<style lang="scss" scoped src='./css/index.scss'>
</style>
<script setup>
  /**
   * 此组件的场景 说明：
   * exclude  排除在外的 体育类型           使用场景 ： 例如某个 模块 不能 支持 某种球类的 操作 ，对应的 功能可能没开发
   * all      显示所有的 体育类型+全部按钮   使用场景 ： 显示 全部按钮 默认不显示
   */
  const props=defineProps({
    all: {
        type: Boolean,
        default: false
      },//是否有全部
      more_length: {
        type: Number,
        default: 3
      },//默认显示几条不加全部
      exclude: {
        type: Array,
        default: ()=>[]
      },//需要排除的赛种id
      sportId:{
        type: Number,
        default: 1
      }
})
const emit=defineEmits(['updata_select_sportId'])
const payload=project_caopan_components_query_select_sport_type_select_sport_type2_componsable_variable_componsable_fn(props);
const { sport_type_more,
        sport_type_left,
        select_sportId,
        
        }=payload
const { sport_type_click,sport_type_more_class
}=project_caopan_components_query_select_sport_type_select_sport_type2_componsable_variable_componsable_fn({
  ...payload,props,
  emit
})
</script>
