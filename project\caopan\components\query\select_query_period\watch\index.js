import { watch } from 'vue';
export const watcher_register = (payload) => {
    const {
        show_more_query,
        current_h,
        emit,
        init_query_form_date_arr,
        query_form_date_tab_click,
        default_select,
        route
    } = payload

    return [

        ///多个watch，透过array传出,可以直接使用payload上下文
        watch(() => show_more_query, () => {
            emit("show_more_query_change", show_more_query.value)
        }),
        watch(() => current_h, () => {
            init_query_form_date_arr(payload);
        }),

        watch(() => route.name, (val) => {
            query_form_date_tab_click()
            if (val == 'trader_manage_liveOddSupport') {
                query_form_date_tab_click(9)
            } else if (val == 'trader_manage_morning_o') {
                query_form_date_tab_click(0)
            } else {
                query_form_date_tab_click(default_select.value)
            }
        })
        // '$route.name': {
        //     handler: function(val, oldVal){
        //         init_query_form_date_arr();
        //             if(val == 'trader_manage_liveOddSupport'){
        //                   query_form_date_tab_click(9)
        //             }else if(val == 'trader_manage_morning_o'){
        //                   query_form_date_tab_click(0)
        //             }else{
        //                  query_form_date_tab_click(default_select.value);
        //             }
        //     },
        //       deep: true,
        //       immediate: true
        //      }
    ]
}