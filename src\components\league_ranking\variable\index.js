import { ref } from "vue";
import { table_columns_config } from "src/components/league_ranking/config/columns.js";
export const create_base_state_instance_fn = () => {
  const sta_switch = ref(0);
  // 优先级接口参数
  const ts = ref(null);
  //列表数据
  const data_list = ref([]);
  // 优先级排序
  const visible = ref(false);
  //修改排序值
  const is_modifying_playrules_order = ref(false);
  //当前联赛等级
  const checked_level = ref(1);
  const columns = ref(table_columns_config);
  //加载状态
  const tabledata_loading = ref(false);
  //筛选联赛名称
  const tournament_name = ref("");
  //筛选出来的内容
  const tournament_data = ref([{}]);
  //模糊查搜索中
  const tournament_loading = ref(false);
  //params参数
  const params = ref({
    sportId: 1,
    tournamentLevel: 1,
    start: 1,
    size: 200,
    id: null,
    versionNewStatus: "",
    tournamentName: "",
  });
  //列表数量
  const total = ref(0);
  //防抖
  const is_post = ref(false);
  // 当前点击row的id
  const row_click_id = ref(0);
  //侧边筛选
  const left_show = ref(false);
  //table_hover提示
  const table_hover = ref(false);
  //输入排序验证
  const sort_trigger = ref(false);
  //上一次请求的params
  const old_params = ref({});
  return {
    sta_switch,
    // 优先级接口参数
    ts,
    // 优先级排序
    visible,
    //修改排序值
    is_modifying_playrules_order,
    //当前联赛等级
    checked_level,
    //列表数据
    data_list,
    columns,
    //加载状态
    tabledata_loading,
    //筛选联赛名称
    tournament_name,
    //筛选出来的内容
    tournament_data,
    //模糊查搜索中
    tournament_loading,
    //params参数
    params,
    //列表数量
    total,
    //防抖
    is_post,
    // 当前点击row的id
    row_click_id,
    //侧边筛选
    left_show,
    //table_hover提示
    table_hover,
    //输入排序验证
    sort_trigger,
    //上一次请求的params
    old_params,
  };
};
