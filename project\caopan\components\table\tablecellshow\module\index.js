export const init_all_need_value = (payload) => {
const { str_arr,col_width} = payload;
  let col_widths = str_arr.value.col_width;
  col_width.value = col_widths ? 1.3 * col_widths : 200;

  compute_str_1();
  compute_str_all();
};
export const compute_str_1 = (payload) => {
    
const { str_1,str_arr} = payload;
  //  {
  //   col_width,
  //   return_arr
  // }
  let arr = str_arr.value.return_arr;
  if (arr) {
    let str = "";
    if (arr.length == 1) {
      str = arr[0];
    } else if (arr.length > 1) {
      str = arr[0] + "...";
    }
    str_1.value = str;
  }
};
export const compute_str_all = (payload) => {
    const { shwo_tooltip,str_arr,str_all} = payload;
  let arr = str_arr.value.return_arr;
  let str = "";

  if (arr) {
    if (arr.length > 1) {
      shwo_tooltip.value = true;

      str_all.value = arr.join("");
      let arr2 = lodash.chunk(arr, 2);

      arr2.map((x) => {
        str += `   <p>  ${x[0]} ${x[1] ? x[1] : ""} </p> <br/>`;
      });
    }
  }
  str_all.value = str;
};
