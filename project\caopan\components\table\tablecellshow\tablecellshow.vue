<template>
  <div>
    <div
      style="overflow: hidden; text-overflow: ellipsis"
      ref="word_need_break"
    >
      {{ str_1 }}
      <q-tooltip v-if="shwo_tooltip">
        <div v-html="str_all" :style="`max-width:${col_width} px; `"></div>
      </q-tooltip>
    </div>
  </div>
</template>
<script setup>
///引入 variable—composable-fn
import { project_caopan_components_table_tablecellshow_tablecellshow_variable_composable_fn } from "project/caopan/components/table/tablecellshow/componsable/variable.js";

//最基础的payload
const base_payload =
  project_caopan_components_table_tablecellshow_tablecellshow_variable_composable_fn();

///解构出 参数，符合 template 所需要的
const {str_1,shwo_tooltip,str_all,col_width} = base_payload;

import { project_caopan_components_table_tablecellshow_tablecellshow_composable_fn } from "project/caopan/components/table/tablecellshow/componsable/index.js";

const {
  ///输出 template中@事件需要使用到的方法
} = project_caopan_components_table_tablecellshow_tablecellshow_composable_fn({
  ...base_payload,
});
</script>
