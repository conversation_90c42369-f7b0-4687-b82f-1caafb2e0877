import { watch } from "vue";

import {
  get_router_cache_value_by_path,

} from "project/saicheng/componsable/router_cache/handler/module/index.js";

export const watcher_register = (payload) => {
  const { select_sportId, router_cache,when_router_cache_change } = payload;


  return [
    /**
     * @description: 处理路由缓存修正
    */
   watch(router_cache, () => {
      select_sportId.value = get_router_cache_value_by_path(payload, "sportId")
      if(when_router_cache_change){
        when_router_cache_change(payload);
      }

    },{deep:true}),
  ];
};
