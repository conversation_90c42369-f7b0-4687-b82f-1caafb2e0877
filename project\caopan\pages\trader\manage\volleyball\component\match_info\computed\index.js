import { computed } from "vue";
import {
  main_categoryset_id,
  categoryset_show_arr,
  stage_obj,
  liveOddBusiness,
  get_market_odds_kind,
  get_volume_switch_status,
} from "src/output/common/store-common.js";

export const computed_generator = (payload) => {
  const { props, categoryset_show_arr } = payload;
  const specialHeaderVisible = computed(() => {
    const { categorySetId, matchId, sportId } = props.value.row;

    // 玩法集显隐数组是该玩法集合的uuid(matchId+'-'+categorySetId)组成的，因为所有赛事的玩法集合都存储在这里数组里面，
    // 所以需要根据matchId做筛选到当前赛事的玩法集合显隐
    let cur_categoryset_show_arr = categoryset_show_arr.value.filter((item) =>
      item.includes(matchId)
    );

    // 副玩法集展开个数大于0并且是第一个副玩法集
    if (cur_categoryset_show_arr.length > 0) {
      cur_categoryset_show_arr.sort();
      const first_categoryset_id = cur_categoryset_show_arr[0].split("-")[1];
      if (first_categoryset_id == categorySetId) {
        return true;
      }
    }

    return false;
  });
  return {
    main_categoryset_id: main_categoryset_id(),
    categoryset_show_arr: categoryset_show_arr(),
    stage_obj: stage_obj(),
    liveOddBusiness: liveOddBusiness(),
    get_market_odds_kind: get_market_odds_kind(),
    get_volume_switch_status: get_volume_switch_status(),
    specialHeaderVisible,
  };
};
