import { ref, computed } from 'vue'
export function project_caopan_components_query_select_sport_type_select_sport_type2_componsable_variable_componsable_fn(props) {
    const sport_type_constant = ref([])//所有数据
    const sport_type_constant_copy = ref([])//处理后的数据
    const select_sportId = ref(1)//默认select_sportId
    const sport_type_more = computed((payload) => {
        return sport_type_constant.value.filter((item, index) => {
            return index > props.more_length
        })
    })//更多的赛种
    const sport_type_left = computed((payload) => {
        return sport_type_constant.value.filter((item, index) => {
            return index <= props.more_length
        })
    })
    //除更多的赛种
    return {
        sport_type_more,
        sport_type_left,
        select_sportId,
        sport_type_constant,
        sport_type_constant_copy,
    }
}