<!--
 * <AUTHOR> Rank
 * @Date           : 2020-09-22 11:07:39
 * @Description    : 联赛设置 -- 联赛等级
 * @FilePath: /project/caopan/pages/operations_management/league_ranking/components/level.vue
-->
<template>
  <div
    class="bg-panda-base-title-light line-height-32px items-center full-width border-bottom-light2 p-w100p"
  >
    <div class="p-pa show-left">
      <slot name="left"> </slot>
    </div>
    <i-tabs v-model="checked_sport_level">
      <i-tab-pane
        :label="`${c_numbers[item]}` + i18n_t('common.league')"
        :name="`${item}`"
        v-for="item in checked_sport_max_level"
        :key="item"
      ></i-tab-pane>
      <!-- 联赛 -->
      <i-tab-pane
        :label="`${c_numbers[0]}` + i18n_t('common.league')"
        name="0"
      ></i-tab-pane>
    </i-tabs>
    <div class="p-pa show-right">
      <slot name="right"> </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { i18n_t } from "src/output/common/project-common.js";
import {
  level,
  c_numbers,
} from "src/components/league_ranking/config/sport_level.js";

const props = defineProps({
  sport_id: {
    type: Number,
    default: 0,
  },
  sport_level: {},
});
const emit = defineEmits();
const checked_sport_max_level = ref(0);
const checked_sport_level = ref("1");
/**
 * @description:  赛种id
 */
watch(
  () => props.sport_id,
  () => {
    let s = level[0]?.max_level;
    checked_sport_max_level.value = s;
    checked_sport_level.value = "1";
  },
  { immediate: true }
);
/**
 * @description: 监听赛种id进行传值
 */
watch(
  () => props.sport_level,
  (n) => {
    if (n !== null) {
      checked_sport_level.value = n;
    }
  }
);

watch(checked_sport_level, () => {
  emit("get_level", checked_sport_level.value);
});
</script>

<style lang="scss" scoped>
@import url(src/components/league_ranking/component/level/css/index.scss);
</style>
