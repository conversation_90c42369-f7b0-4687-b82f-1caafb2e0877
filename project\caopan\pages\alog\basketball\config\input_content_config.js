
export const applyReverseKeys1=[
  ['ftSup','ftGe','ahSd','ouSd'],
  ['h1Sup','h1Ge','h1AhSd','h1OuSd'],
  ['h2Sup','h2Ge','h2AhSd','h2OuSd'],
  ['q1Sup','q1Ge','q1AhSd','q1OuSd'],
  ['q2Sup','q2Ge','q2AhSd','q2OuSd'],
  ['q3Sup','q3Ge','q3AhSd','q3OuSd'],
  ['q4Sup','q4Ge','q4AhSd','q4OuSd']
];
export const  applyReverseKeys= ['ftSup','ftGe','ahSd','ouSd'];

export const input_data_reverse_key={
  "ftSup":"1-2",
  "ftGe":"1-3",
  "ahSd":"1-4",
  "ouSd":"1-5",
};
export const stageKeyArr= ['q1Score','q2Score','h1Score', 'q3Score','q4Score','otScore','h2Score','ftScore'];