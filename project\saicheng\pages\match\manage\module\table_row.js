import { EMITTER } from "src/output/common/project-common.js"
// 常规表格
import tableCom from
  "project/saicheng/pages/match/manage/component/table-com/index.vue"

// 电子表格
import tableEsport from
  "project/saicheng/pages/match/manage/component/table-esport/index.vue"

// 关联列表
import tablessociation from
  "project/saicheng/pages/match/manage/component/table-association/index.vue"
// 赛事列表
import tableMatch from
  "project/saicheng/pages/match/manage/component/table-match/index.vue"

import { init_tabledata } from "project/saicheng/pages/match/manage/api_request/init_tabledata.js"
// 返回table
export const table_com_change = (payload, type) => {
  const {
    curren_table_com,
    curren_table_active
  } = payload
  const views = {
    //common: tableCom,
    esport: tableEsport,
    association: tablessociation
  }
  curren_table_com.value = type
  if (type === 'common') {
    init_tabledata(payload)
    return;
  }
  curren_table_active.value = views[type]
}

//联赛||赛事级别表格切换

export const table_module_change = (payload) => {
  const { is_table_type, curren_table_com,
    curren_table_active, ws_config, ws_timer, uuid,time_visibility } = payload
    //清空弹出框状态
    time_visibility.value = false
  if (is_table_type.value === 1) {
    curren_table_com.value = 'common'
    init_tabledata(payload)
    return;
  }
  if (is_table_type.value === 2) {
    curren_table_com.value = 'match'
    curren_table_active.value = tableMatch;
    ws_timer.value = setTimeout(() => {
      EMITTER.emit('project_saicheng_components_websocket_example_emit', { ws_config, uuid })
    }, 100);
  }

}