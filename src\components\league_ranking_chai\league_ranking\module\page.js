import { get_data_list } from "src/components/league_ranking/api-request/get_data_list.js";
/**
 * @description: 列表每页条数
 * @param {Number} 条数
 */
export const change_page_num = (payload, v) => {
  const { params, is_post } = payload;
  params.value.start = v;
  is_post.value || get_data_list(payload);
};
/**
 * @description: 列表页数，翻页
 * @param {Number} 页数
 */
export const change_page_size = (payload, v) => {
  const { params, is_post } = payload;
  params.value.size = v;
  is_post.value || get_data_list(payload);
};
