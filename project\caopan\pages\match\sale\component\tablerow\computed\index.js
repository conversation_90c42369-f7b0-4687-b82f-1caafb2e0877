
import { ref, computed, watch, reactive, useTemplateRef } from 'vue'
import {
    get_serverTime, 
    get_breadcrumbs, 
    get_optional_events_num,
    get_market_odds_kind,
    get_dataSource_obj,
  } from "src/output/common/store-common.js";
///payload上下文
export const computed_generator = (payload) => {

       // store 内的单例 状态 获取
       const serverTime = get_serverTime();
       const breadcrumbs = get_breadcrumbs();
       const optional_events_num = get_optional_events_num();
       //赔率切换
       const market_odds_kind = get_market_odds_kind();
       const dataSource_obj = get_dataSource_obj();

    // 其他简单的计算属性
    const compute_i18n_type = computed(() => {
        return sessionStorage.getItem("i18n") == "en";
    });

    // 返回 所有计算属性
    return {
        serverTime,
        breadcrumbs,
        optional_events_num,
        market_odds_kind,
        compute_i18n_type,
        dataSource_obj,
        get_breadcrumbs:breadcrumbs
    }
}