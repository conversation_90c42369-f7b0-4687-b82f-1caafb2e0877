
    import {
    api_bfw_resource,
  } from "project/bifenwang/api/index.js";
   import {
    Message,
    lodash,
    alert_message_default,
    i18n_t  } from "src/output/common/project-common.js"
  import {is_PA_or_PD,abort_match_modify} from "project/saicheng/pages/match/resource/component/match_edit/module/index.js"

   // 保存并应用
   export const submit_match_modify = async (payload,num) => {
    const {
      matchCategory,
      tournamentGroupType,
      tournamentName,
      round_type,
      level,
      matchType,
      leagueUrl,
      moveStatus,
      league_affiliation,
      openSummerWinterTimeFlag,
      shallow_edit_obj,
      filePath_72,
      filePath_50,
      requseted_id,
      editing_obj_id,
      promotion_quota,
      relegation_quota,
      tournamentCreateType,
      operatorStatus,
      matchPeriodType,
      other_cycles_obj,
      emit,
      props,
      language_list,
      matchCategory_obj,
      editingObjData
      } = payload;
 
    
    if (!props.cur_sportId || !editingObjData.value.id) {
      return false;
    }
    

    if (is_PA_or_PD(payload)&&round_type.value<0)return Message.warning(i18n_t('saicheng.manage_alert.alert43'))  // 比赛赛制不可为空
    if (!matchCategory.value) return Message.warning(i18n_t('event.add.w101'))  // 类型不可为空
    if (matchCategory_obj.value.show  && !matchCategory_obj.value.currentRoundNumber ) {
      return Message.warning(i18n_t('saicheng.match_create_league.sc_type_placeholder'))
    }  

    
    
    if (!matchPeriodType.value)
      return Message.warning(i18n_t("saicheng.match_match_cycle.match_cycle_message1"))   
  
    if (matchPeriodType.value == 5 && !other_cycles_obj.value.label) {
      return Message.warning(i18n_t("saicheng.match_match_cycle.match_cycle_message2"))   
    }
    if (matchPeriodType.value == 5 && !other_cycles_obj.value.code) {
      return Message.warning(i18n_t("saicheng.match_match_cycle.match_cycle_message3"))   
    }
    let otherPeriodType =  {"label":other_cycles_obj.value.label,"code":other_cycles_obj.value.code}

    var lanMap = {};
    let language_name = null;
    let language_key = null;

    for(let i=0;i<language_list.value.length;i++){
      var key = language_list.value[i].key;
      var name = language_list.value[i].name;
      var key_jc = key+"_jc";
      var value = language_list.value[i].title;
      var value_jc = language_list.value[i].title_jc;
      lanMap[key]= value;
      lanMap[key_jc]= value_jc;
      if(key == 'zs' || key == 'en' || key == 'zh' || key == 'vi'|| key == 'ko'|| key == 'th'){
        language_key = value ? false : true;
        language_name =name;
      }
      if(language_key){
        break;
      }
    }

    let language_message = i18n_t('saicheng.manage_alert.alert55').replace("{language}",language_name);

    if (language_key) return Message.warning(language_message)  // 联赛名称不可为空

   let tournamentNameJson = {
      // 联赛多语言名称
      ...lanMap
    };

    let params = {
      id:editingObjData.value.id,
      sportId: props.cur_sportId, // 球类ID

      ...lodash.pick(shallow_edit_obj.value, ["regionId"]), // 联赛区域
      matchType:matchType.value,
      tournamentCreateType: tournamentCreateType.value, // 自建联赛标识 0:非自建，1：自建
      openSummerWinterTimeFlag: openSummerWinterTimeFlag.value, //开启夏冬令时标识,默认是0(0:关闭,1:开启)
      leagueUrl: leagueUrl.value, // 联赛官网
      matchCategory:matchCategory.value,
      tournamentLevel: level.value, // 联赛等级
      operatorStatus:operatorStatus.value,
      // 晋升名额
      promotionQuota: promotion_quota.value,
      // 降级名额
      relegationQuota: relegation_quota.value,
      // 赛制选择
      tournamentGroupType: tournamentGroupType.value,
      tournamentNameJson: JSON.stringify(tournamentNameJson),
      tournamentRoundType: round_type.value, //比赛赛制
      matchPeriodType :matchPeriodType.value,// 比赛周期
      otherPeriodType  : JSON.stringify(otherPeriodType),
      currentRoundNumber : matchCategory_obj.value.currentRoundNumber

    };
    if (filePath_72.value) {
      params.logoUrl = filePath_72.value;
    }

     if (filePath_72.value) {
      params.logoUrl = filePath_72.value;
    }
    if (filePath_50.value) {
      params.logoUrlThumb = filePath_50.value;
    }


    try {
      let res = await api_bfw_resource.post_tournament_updateTournament(params);
      let code = lodash.get(res, "data.code");
      requseted_id.value = params.id;

      if (num == 2) {
        alert_message_default(res);
      }
      //暂时处理，解决联赛库编辑列左侧选中闪烁
      if (code == 200) {
        // 通知刷新
        abort_match_modify(payload)
        emit("edit_success");
      }
     } catch (err) {
    
     } finally {
    
    }

  }
