<!--
 * <AUTHOR> da<PERSON><PERSON>
 * @FilePath: /project/caopan/components/query/select_sport_type/select_sport_type1/select_sport_type1.vue
 * @Description: 头部 赛种 组件
-->
<template>
  <!-- 头部 体育大类条件 -->
  <div
    class="row bg-panda-sport-type-primary position-absolute border-bottom-light2"
    style="z-index: 1000; background: #28303f; border-bottom: 1px solid #121720"
    :style="{ width: `${window_size_info.width - 60}px` }"
    v-show="sport_type_visible"
  >
    <!-- 头部 各赛种展示开始 -->
    <div class="row" id="select-sport-type">
      <div
        v-for="(items, indexs) in sport_type_constant_lt_8 || []"
        :key="`sport_type_${indexs}`"
        @click="sport_type_click(items, indexs)"
        class="cursor-pointer border-panda-dark-dark box-sizing panda-tab-like-tab"
      >
        <div class="row p1-jcc p1-aic position-relative" style="height: 100%">
          <span v-if="select_sportId == items.id && items.img">
            <img class="panda-icon-mini vatb" :src="items.img" style="width: 14px; height: 14px" />
          </span>
          <span
            v-else
            class="panda-icon-mini vatb"
            :class="items.icon"
            :style="{
              'text-fill-color': select_sportId == items.id ? items.color : '#787878',
            }"
          ></span>
          <span style="margin-left: 4px" class="panda-tab-like-tab-label" :class="select_sportId == items.id ? 'text-pa' : 'text-pandabase'"
            >{{ items.introduction }} <span v-if="showNum">({{ items.numb }})</span></span
          ><!-- 展示赛事数量 -->
          <q-badge
            color="red"
            rounded
            floating
            class="champion_badge"
            v-if="cpomputed_show_badge(items.id)"
            ><!-- 展示赛未满足条件数量 -->
            {{ badge_num(items.id) }}
          </q-badge>
        </div>
      </div>
    </div>
    <!-- 头部 各赛种展示结束 -->
    <!-- 头部赛种 超出一定数量展示 开始  ---- 现已取消 -->
    <div v-if="show_fixed_more_sport_type" class="bg-panda-sport-type-primary" style="width: 100%">
      <div class="row sport-box-item">
        <div
          v-for="(items, indexs) in sport_type_constant_gt_8 || []"
          :key="`sport_type_${indexs}`"
          @click="sport_type_click(items, indexs)"
          class="sport-item cursor-pointer line-height-30px panda-tab-like-tab"
          style="width: 158px; height: 34px"
        >
        <div class="row p1-aic" style="height: 100%; justify-content: center">
          <span v-if="select_sportId == items.id">
            <img class="panda-icon-mini vatb" :src="items.img" style="width: 14px; height: 14px" />
          </span>
          <span v-else class="panda-icon-mini vatb" :class="items.icon" :style="{ 'text-fill-color': items.color }"></span>
          <span
              style="margin-left: 4px"
              class="panda-tab-like-tab-label"
              :class="select_sportId == items.id ? 'text-panda-text-light ' : 'text-panda-table-header-base'"
              >{{ items.introduction }} ({{ items.numb }})</span
            >
          </div>
        </div>
      </div>
    </div>
    <!-- 头部赛种 超出一定数量展示 结束  ---- 现已取消 -->
  </div>
</template>

<script setup>
  /**
   * 此组件的场景 说明：
   * exclude  排除在外的 体育类型           使用场景 ： 例如某个 模块 不能 支持 某种球类的 操作 ，对应的 功能可能没开发
   * all      显示所有的 体育类型+全部按钮   使用场景 ： 显示 全部按钮 默认不显示
   */
  // import {mapGetters, mapActions} from "vuex";
  // import routerCache_handler from "project/caopan/mixins/routerCache/routerCache_handler.js";
  // import windowResize from "project/caopan/mixins/style/windowResize.js";
  import { get_champion_activem_atchNum } from 'src/output/common/store-common.js';
  import { 
    src_componsable_global_variable_componsable_fn,
  } from 'src/output/common/componsable-common.js';
  import {
    
    project_caopan_components_router_cache_handler_componsable_index_componsable_fn,
  } from "project/caopan/components/router_cache/handler/componsable/index.js"
  import { project_caopan_components_query_select_sport_type_componsable_fn } from 'project/caopan/components/query/select_sport_type/select_sport_type1/componsable/index.js';
  import { project_caopan_components_query_select_sport_type_variable_componsable_fn } from 'project/caopan/components/query/select_sport_type/select_sport_type1/componsable/variable.js';

  const props = defineProps({
    exclude: {
      type: Array,
      default: () => [],
    },
    complete: {
      type: Boolean,
      default: false,
    },
    all: {
      type: Boolean,
      default: false,
    },
  });
  const global_obj = src_componsable_global_variable_componsable_fn();
  const bese_payload = project_caopan_components_query_select_sport_type_variable_componsable_fn();
  const {
    get_router_cache_value_by_path,
    set_router_cache_by_path_and_value,
  } = project_caopan_components_router_cache_handler_componsable_index_componsable_fn({
    ...global_obj,
    ...bese_payload,
  })
  const { 
    sport_type_constant_lt_8, 
    sport_type_constant_gt_8, 
    show_fixed_more_sport_type, 
    showNum, 
    select_sportId,
    window_size_info,
    sport_type_visible,
  } = bese_payload;
  const { 
    badge_num,
    sport_type_click,
    cpomputed_show_badge,
   } = project_caopan_components_query_select_sport_type_componsable_fn({
    ...bese_payload,
    get_router_cache_value_by_path,
    set_router_cache_by_path_and_value,
    props,
  });
</script>

<style lang="scss" scoped>
  @import url('project/caopan/components/query/select_sport_type/select_sport_type1/css/index.scss');
</style>
