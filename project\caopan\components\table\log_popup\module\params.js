import moment from "moment";
import { get_log_data } from "../api-request/get_log_data";

    /**
     * @description: 获取搜索默认值
     */
    export const get_search_params = (payload) => {
      const {

        traders_log_remember,
        search_params,


      } = payload
      let traders_log_remember_1 = localStorage.getItem("traders_log_remember");
      // 是否是记住？
      if (traders_log_remember) {
        traders_log_remember.value = JSON.parse(traders_log_remember_1);
        let localParams = localStorage.getItem("traders_log_search_params");
        if (localParams) {
          search_params.value = JSON.parse(localParams);
        }
      }
      get_log_data();
    }


        /**
     * @description: 页面显示多少条
     * @param {*} v
     */
        export const change_page_size = (payload,v) => {
          const {

            post_params,
            isPost,


          } = payload
          console.log(v, "change_page_size");
          post_params.value.pageSize = v;
          post_params.value.size = v;
          post_params.value.page = 1;  // 如果调整页面条数，page调整为第一页
          set(post_params.value, "pageSize", v);
          set(post_params.value, "size", v);
          isPost.value || get_log_data();
        }


    /**
     * @description: 页面当前页数
     * @param {*} v
     */
    export const change_page_num = (payload,v) => {
      const {

        post_params,
        isPost,


      } = payload
      console.log(v, "change_page_num");
      post_params.value.pageNum = v;
      post_params.value.page = v;
      isPost.value || get_log_data();
    }

        /**
     * @description: 获取请求参数
     * @return {*}请求参数
     */
        export const get_params = (payload) => {
          const {

            search_state,
            traders_log_remember,
            search_params,
            post_params,

            config,

            open,
            route

          } = payload
          let params = {};
          if (open.value) {
            // 页面新开有二类情况，可能是porp传入 ，可能是 route.query传入
            if (Object.keys(params.value).length >= 1) {
              params = Object.assign({}, params.value, post_params.value);
            } else {
              let query = route.query.params ? route.query.params : {};
              let query2 = {
                matchId: route.query.matchId,
              };

              params = Object.assign({}, query2, query, post_params.value);
            }
            // 有2种传值 param.value
          } else {
            // 这个是模态框参数都是porp传入不需要判断
            // post_params.value.pageSize = 10
            params = Object.assign({}, params.value, post_params.value);
          }
          if (search_state.value) {
            if (traders_log_remember.value) {
              localStorage.setItem(
                "traders_log_search_params",
                JSON.stringify(search_params.value)
              );
            }
            params = Object.assign(params, search_params.value);
            // iview 选择时间比实际少一天问题处理
            if (search_params.value.operateStartTime) {
              params.operateStartTime = moment(
                new Date(search_params.value.operateStartTime)
              ).format("YYYY-MM-DD HH:mm:ss");
            }
            if (search_params.value.operateEndTime) {
              params.operateEndTime = moment(
                new Date(search_params.value.operateEndTime)
              ).format("YYYY-MM-DD HH:mm:ss");
            }
            switch (params.operatePageCode) {
              case 119:
                params.sportId = 1; // 足球
                break;
              case 115:
                params.sportId = 2; // 篮球
                break;
              case 116:
                params.sportId = 5; // 网球
                break;
              case 117:
                params.sportId = 4; // 冰球
                break;
              case 100161:
                params.sportId = 1; // 足球
                params.operateModule = "100161"; // 预开售设置-篮种
                break;
              case 120:
                params.operateModule = "10007-1"; // 结算中心 (新) - 足球
                break;
              case 121:
                params.operateModule = "10007-2"; // 结算中心 (新) - 篮球
                break;
              case 122:
                params.operateModule = "10008-1"; // 预开售设置-足种
                break;
              case 123:
                params.operateModule = "10008-2"; // 预开售设置-篮种
                break;
            }
          }
          // 如果从其他页面跳转到操盘页面,通过url传值
          let query = route?.query;
          let key_query = Object.keys(query);
          if (key_query.length > 0) {
            params[key_query] = Number(query[key_query]);
            config.value.operatePageList = [];
          }
          return params;
        }

           /**
     * @description: 重置查询的params
     */
    export const clear_params = (payload) => {
      const {

        search_params,
        post_params,

        update_key,


      } = payload
      post_params.value = {
        pageNum: 1,
        pageSize: 20,
      };
      search_params.value = {
        operatePageCode: null, //操作页面代码
        objectId: null, //操作对象id
        objectName: null, //操作对象名称
        extObjectId: null, //操作对象扩展ID
        extObjectName: null, //操作对象扩展名称
        behavior: null, // 操作类型
        userId: null, // 操作人id
        userName: null, // 操作人
        operateStartTime: null, // 开始事件，
        operateEndTime: null, // 结束时间
      };
      update_key.value = !update_key.value;
      getApi();
      get_log_data();
    }

    export const change_select = (payload,value, key) => {
      const {

        search_params,


      } = payload
      search_params.value[key] = value;
    }
