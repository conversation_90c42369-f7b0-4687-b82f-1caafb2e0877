
import { onMounted, onBeforeUnmount } from "vue";



import { watcher_register } from "project/saicheng/pages/match/manage/component/create_match/watch/index.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";

import { query_form_date_tab_click, toOperate } from "project/caopan/components/query/select_query_period5/module/index.js";


export const project_caopan_components_query_select_query_period5_select_query_period5_composable_fn = (raw_payload) => {
  src_componsable_util_watcher_register_componsable_fn(
    watcher_register({
        ...raw_payload,
    })
);
const emit = emit_generator(raw_payload);
const payload = {
  ...raw_payload,
  ...emit
} 
  onMounted(() => {
    const {
      compute_current_h_gmt_add_8,
      timer,
      route,
      date_option,
      date_option2,
      show_more_query,
      more_query,
      optional_events_num,
      routeName,
    } = payload;
    // 初始化日历数据
    date_option.value = {
      disabledDate(payload, arg) {
        let now = that.get_serverTime() || new Date().getTime();
        return arg && arg.valueOf() > now - 12 * 60 * 60 * 1000;
      },
    };
    date_option2.value = {
      disabledDate(payload, arg) {
        let now = that.get_serverTime() || new Date().getTime();
        if (new Date(now).getHours() < 12) {
          return arg && arg.valueOf() < now + 5 * 24 * 60 * 60 * 1000;
        } else {
          return arg && arg.valueOf() < now + 6 * 24 * 60 * 60 * 1000;
        }
      },
    };
    // init_query_form_date_arr();
    // 控制更多查询初始值
    show_more_query.value = more_query.value;
    // current_.value.then(res => {
    //   query_form_date_tab_click(default_select.value);
    // });

    optional_events_num.value =
      sessionStorage.getItem("optional_events_num") || 0;
    routeName.value = route.name;

    timer.value = setInterval(compute_current_h_gmt_add_8.value, 1000);
  });

  onBeforeUnmount(() => {
    const { timer } = payload;
    set_history_startTimeFrom(null);
    clearInterval(timer.value);
  });

  return {
    ///这边预期会放置@方法体， 和 computed复杂的方法体（需要payload，或高耦合的代码）
     query_form_date_tab_click : ()=>query_form_date_tab_click(payload),
     toOperate : ()=>toOperate(payload),
     init_tabledata : ()=>init_tabledata(payload), //emit事件
     handle_history_date_confirm : ()=>handle_history_date_confirm(payload), //emit事件
  };
};
