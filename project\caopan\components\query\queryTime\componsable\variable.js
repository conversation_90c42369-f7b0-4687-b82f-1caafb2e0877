

  ///本页面的variable 文件，引入
  import {create_base_state_instance_fn} from "project/caopan/components/query/queryTime/variable/index.js";
  import { src_componsable_global_variable_componsable_fn} from "src/output/common/componsable-common.js"

  export const  project_caopan_components_query_query_time_variable_componsable_fn =
    (raw_payload) => {
      const global_obj = src_componsable_global_variable_componsable_fn();
      const query_query_time_state = create_base_state_instance_fn();

        const payload = {
        ///这三个必要内容
        ...query_query_time_state,
        ...global_obj,

      };
  
      return payload;
    };