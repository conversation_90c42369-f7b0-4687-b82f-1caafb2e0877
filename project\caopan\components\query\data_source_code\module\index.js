import { nextTick } from "vue";

export const init_queryform_form_dataSourceCode = (payload,item) => {

  const { queryform_form, filter_data_sources_constant } = payload
  
  let arr = [];
  if (filter_data_sources_constant.value) {
    filter_data_sources_constant.value.map(x => {
      arr.push(x.code);
    });
  }
  nextTick(() => {
    queryform_form.value.dataSourceCode = arr
  })
  //  拿到
}

export const emit_dataSourceCode_change = (payload) => {

  const { queryform_form, change } = payload
  // emit("change", queryform_form.value);
  //    dataSourceCode: [],
  // dataSourceCode_all: true
  //         { fullName: "已匹配", code: "MA" },
  // { fullName: "未匹配", code: "UMA" }
  //   {
  //   dataSourceCode:[],
  //   matchList:[],
  // }
  let arr = queryform_form.value.dataSourceCode.concat([]);
  let matchList = lodash.intersection(["MA", "UMA"], arr);
  let dataSourceCode_list = lodash.pullAll(arr, ["MA", "UMA"]);
  change({ 
    dataSourceCode: dataSourceCode_list,
    matchList: matchList 
  })
  // emit("change", {
  //   dataSourceCode: dataSourceCode_list,
  //   matchList: matchList
  // });
}
export const comoute_showrightdetail_by_router = (payload) => {

}

// 重複 ？？

// export const queryform_form_dataSourceCode_change = (payload) => {
//   set_router_cache_by_path_and_value(
//     "queryform_form.dataSourceCode",
//     queryform_form.value.dataSourceCode
//   );
//   set_router_cache_by_path_and_value(
//     "queryform_form.dataSourceCode_all",
//     queryform_form.value.dataSourceCode_all
//   );
// }

export const queryform_form_dataSourceCode_all_change = (payload,val) => {

  const { queryform_form } = payload

  if (queryform_form.value.dataSourceCode_all) {
    init_queryform_form_dataSourceCode();
  } else {
    queryform_form.value.dataSourceCode = [];
  }
}

export const queryform_form_dataSourceCode_change = (payload,item, index) => {

  const { filter_data_sources_constant, queryform_form } = payload

  let al =
    filter_data_sources_constant.value &&
    filter_data_sources_constant.value.length;
  let sl =
    queryform_form.value.dataSourceCode &&
    queryform_form.value.dataSourceCode.length;
  if (al != sl) {
    queryform_form.value.dataSourceCode_all = false;
  } else {
    queryform_form.value.dataSourceCode_all = true;
  }

}


// methods: {
//   ...mapActions(["set_reset_query"]),
 
// }
 // export const init_queryform_form_dataSourceCode = (payload,item) => {
    //   let arr = [];
    //   if (filter_data_sources_constant.value) {
    //     filter_data_sources_constant.value.map(x => {
    //       arr.push(x.code);
    //     });
    //   }
    //   nextTick(() => {
    //     queryform_form.value.dataSourceCode = arr
    //   })
    //   //  拿到
    // },
    // export const emit_dataSourceCode_change = (payload) => {
    //   // emit("change", queryform_form.value);
    //   //    dataSourceCode: [],
    //   // dataSourceCode_all: true
    //   //         { fullName: "已匹配", code: "MA" },
    //   // { fullName: "未匹配", code: "UMA" }
    //   //   {
    //   //   dataSourceCode:[],
    //   //   matchList:[],
    //   // }
    //   let arr = queryform_form.value.dataSourceCode.concat([]);
    //   let matchList = lodash.intersection(["MA", "UMA"], arr);
    //   let dataSourceCode_list = lodash.pullAll(arr, ["MA", "UMA"]);
    //   emit("change", {
    //     dataSourceCode: dataSourceCode_list,
    //     matchList: matchList
    //   });
    // },
    // export const comoute_showrightdetail_by_router = (payload) => {},
    // export const queryform_form_dataSourceCode_change = (payload) => {
    //   set_router_cache_by_path_and_value(
    //     "queryform_form.dataSourceCode",
    //     queryform_form.value.dataSourceCode
    //   );
    //   set_router_cache_by_path_and_value(
    //     "queryform_form.dataSourceCode_all",
    //     queryform_form.value.dataSourceCode_all
    //   );
    // },

    // export const queryform_form_dataSourceCode_all_change = (payload,val) => {
    //   if (queryform_form.value.dataSourceCode_all) {
    //     init_queryform_form_dataSourceCode();
    //   } else {
    //     queryform_form.value.dataSourceCode = [];
    //   }
    // },
    // export const queryform_form_dataSourceCode_change = (payload,item, index) => {
    //   let al =
    //     filter_data_sources_constant.value &&
    //     filter_data_sources_constant.value.length;
    //   let sl =
    //     queryform_form.value.dataSourceCode &&
    //     queryform_form.value.dataSourceCode.length;
    //   if (al != sl) {
    //     queryform_form.value.dataSourceCode_all = false;
    //   } else {
    //     queryform_form.value.dataSourceCode_all = true;
    //   }
    // }
