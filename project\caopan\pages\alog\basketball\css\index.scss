

.content {
  min-width: 1500px;
  max-width: 1500px;
}
.content-content {
  margin: auto;
}
.use-ivew .btn {
  line-height: 21px;
  padding: 0px 4px;
}
.name-content {
  border-radius: 2px 2px 0 0;
  height: 30px;
}
.border-red :deep( .ivu-input ){
  border: 1px solid red;
}
.bg-1 :deep( .ivu-input),
.bg-1 :deep( .ivu-input-group-append ){
  background-color: #2d3b55 !important;
}
.bg-1 :deep( .ivu-input ){
  border: 1px solid #2d3b55;
}
.bg-2 :deep( .ivu-input),
.bg-2 :deep( .ivu-input-group-append ){
  background-color: #273144 !important;
}
.bg-2 :deep( .ivu-input ){
  border: 1px solid #273144;
}
.content :deep( .ivu-input-group-append ){
  border-left: 1px solid;
  border-color: gray !important;
}
.input-content :deep( .ivu-input-group-append ){
  padding: 0;
  width: 0;
  border: 0;
}
.market .market-item:nth-child(4n) .q-pr-sm {
  padding-right: 0;
}
.use-ivew :deep( .ivu-select-single .ivu-select-selection),
.use-ivew :deep( .ivu-page-options-elevator input ){
  height: 24px;
}
.use-ivew
  :deep( .ivu-select-single)
  .ivu-select-selection
  .ivu-select-placeholder,
.use-ivew
  :deep( .ivu-select-single)
  .ivu-select-selection
  .ivu-select-selected-value {
  line-height: 23px;
}
.gray-bg-no:before {
  color: #9e9e9e;
}
.use-ivew .btn {
  line-height: 18px;
}
.change-btn{
  border: 1px solid #404758;
  padding: 0 5px;
  border-radius: 2px;
}
.checked-btn {
  background: #00aa98;
}
