import {
  api_events_analyse,
  api_risk_control,
  api_order,
  api_settlement_center,
} from "src/api/index.js";

import { lodash, language_value, Message } from "src/output/common/project-common";

   // 足蓝冰操作类型
    export const getFootType = (payload,params) => {

      const { config, isPost, loading  } = payload

      api_events_analyse
        .getOperateType(params)
        .then((res) => {
          let { code, msg, data } = lodash.get(res, "data");
          if (code == 200) {
            console.log(res);
            data.forEach((el) => {
              el.value = language_value(el.names);
              el.label = language_value(el.names);
            });
            config.value.operateTypeList = data;
          } else {
            console.log(msg);
            Message.error(msg);
          }
        })
        .catch((err) => {
          console.error(err);
          Message.error(err);
        })
        .finally(() => {
          isPost.value = false;
          loading.value = false;
        });
    }
