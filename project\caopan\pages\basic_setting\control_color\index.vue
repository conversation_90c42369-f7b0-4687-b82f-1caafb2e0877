<!--
 * @FilePath: /project/caopan/pages/basic_setting/control_color/index.vue
 * @Description: 设置/外观设置
-->
<template>
  <div class="use-ivew bg-panda-base-light full-height p-pr p-pa--lg">
    <div class="p-gutter-mr--lg">
      <span>{{ i18n_t("systemSet.color_1") }}</span>
      <!-- color_1赔率变化颜色 -->
      <q-radio
        :class="show_color == 0 ? 'text-panda-text-light' : ''"
        dark
        v-model="show_color"
        color="teal"
        val="0"
        :label="i18n_t('systemSet.color_2')"
      />
      <!-- color_2红升绿降 -->
      <q-radio
        :class="show_color == 1 ? 'text-panda-text-light' : ''"
        dark
        v-model="show_color"
        color="teal"
        val="1"
        :label="i18n_t('systemSet.color_3')"
      />
      <!-- color_3 绿升红降 -->
    </div>
    <div
      class="p-gutter-mr--xxs p-h--xl flex items-center bg-panda-base-dark p-pl--md set-btn"
    >
      <q-btn
        :label="i18n_t('common.confirm')"
        class="p-h--xs panda-btn-light-dense"
        @click="comfirm"
      ></q-btn>
      <q-btn
        :label="i18n_t('systemSet.set_3')"
        class="p-h--xs panda-btn-light-dense p-bgc--orange"
        @click="apply"
      ></q-btn>
      <q-btn
        :label="i18n_t('common.cancel')"
        class="p-h--xs panda-btn-dark-dense"
        @click="abort"
      ></q-btn>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive, onMounted, onUnmounted } from "vue";

import {
  get_show_color,
  set_show_color,
} from "src/output/common/store-common.js";

import {
 
  i18n_t,
  set_footer_breadcrumbs_part_all
  
} from "src/output/common/project-common.js"

import { src_componsable_global_variable_componsable_fn} from "src/output/common/componsable-common.js"
const global_obj =src_componsable_global_variable_componsable_fn()

const  {
  router,
 

}=global_obj
const show_color = ref("0");

onMounted(() => {
  show_color.value = get_show_color().value;
  set_footer_breadcrumbs_part_all(
    [i18n_t("setPage.text2")],
    [i18n_t("systemSet.set_2")],
    [],
    []
  );
});

// vuex更改颜色设置方法
 const comfirm = (payload) => {
  //确定按钮
  set_show_color(show_color.value);
  // vuex更改本地颜色设置
  router.go(-1);
  // 回退上一路由
};
 const apply = (payload) => {};
 const abort = (payload) => {
  //返回上一页
  router.go(-1);
  // 回退上一路由
};
</script>

<style scoped lang="scss">
.set-btn {
  position: absolute;
  bottom: 50px;
  left: 0px;
  right: 0px;
}
</style>
