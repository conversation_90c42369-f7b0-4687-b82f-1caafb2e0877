
import { 
  init_queryform_form_dataSourceCode,
  emit_dataSourceCode_change,
  comoute_showrightdetail_by_router,
  queryform_form_dataSourceCode_all_change,
  queryform_form_dataSourceCode_change
 } from "../module/index";

import { watcher_register } from "../watch/index";
import { emit_generator } from "../emits/emits";
import { computed_generator } from "../computed/index";


export const project_caopan_components_query_data_source_code_composable_fn = (raw_payload) =>{


  const all_emists = emit_generator(raw_payload)

  const all_computed  = computed_generator()

  const {  
    filter_data_sources_constant,
    reset_query 
  } = all_computed

  const payload = {...raw_payload, ...all_emists, ...all_computed}

  watcher_register(payload)

 

  return {
    init_queryform_form_dataSourceCode:(item) =>init_queryform_form_dataSourceCode(payload, item) ,
    emit_dataSourceCode_change:()=>emit_dataSourceCode_change(payload),
    comoute_showrightdetail_by_router: ()=>comoute_showrightdetail_by_router(payload),
    queryform_form_dataSourceCode_all_change:(val)=>queryform_form_dataSourceCode_all_change(payload, val),
    queryform_form_dataSourceCode_change:(item, index)=>queryform_form_dataSourceCode_change(payload, item, index),
    filter_data_sources_constant,
    reset_query  
  }
}