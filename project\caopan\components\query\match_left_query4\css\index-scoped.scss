:deep( .ivu-cascader .ivu-cascader-menu-item:hover),
:deep( .ivu-cascader-menu .ivu-cascader-menu-item-active ){
  background-color: var(--q-color-panda-base-light);
}

:deep( .ivu-cascader .ivu-cascader-menu-item),
:deep( .ivu-cascader .ivu-cascader-menu-item ){
  color: var(--q-color-panda-text-light);
}

:deep( .ivu-cascader-menu ){
  border-color: var(--q-color-panda-them--404758);
}

:deep( .ivu-cascader .ivu-cascader-menu-item:hover),
:deep( .ivu-cascader-menu .ivu-cascader-menu-item-active ){
  background-color: rgba(0, 170, 152, 0.1) !important;
}

:deep( .ivu-cascader-not-found-tip ){
  width: 123px;
  text-align: left;
  padding-left: 10px;
}

.panda-page-query :deep( .ivu-checkbox-inner ){
  width: 13px;
  height: 13px;
  margin-right: 6px;
}

:deep( .ivu-checkbox-indeterminate .ivu-checkbox-inner:after ){
  width: 7px;
  top: 5px;
}

:deep( .ivu-checkbox-checked .ivu-checkbox-inner:after ){
  left: 4px;
  top: 1px;
}

:deep( .ivu-input-icon-clear ){
  // 清除图标按钮
  right: 40px !important;
  cursor: pointer;
}

:deep( .ivu-checkbox-wrapper ){
  //min-width: 90px
}
:deep( .ivu-tooltip:hover ){
  .panda_set:before {
    color: #00a997;
  }
}
:deep( .ivu-select-multiple .ivu-select-item ){
  padding-right: 34px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.test-placeholder-light :deep( .ivu-select .ivu-select-placeholder),
:deep( .ivu-select-input ){
  color: var(--q-color-panda-text-light) !important;
}
.panda-page-query .bg-panda-field-grey :deep( .ivu-select-input ){
  left: 0;
}
:deep( .ivu-select-selection ){
  max-height: 124px;
  overflow: auto;
}
.regionId :deep( .ivu-select-dropdown ){
  max-height: 400px;
}
.checkbox_inner_bg {
  position: absolute;
  left: 8px;
  top: 13px;
  display: block;
  width: 6px;
  height: 6px;
  background: var(--q-color-panda-border-primary);
}

.panda_set {
  vertical-align: sub;
  font-size: 13px;
}
.icons {
  margin-left: 6px;
  font-size: 12px;
  cursor: pointer;
}
