import { swith_change } from "src/components/league_ranking/api-request/swith_change.js";
import { top_morning } from "src/components/league_ranking/api-request/top_morning.js";
import {
  modify_playrules_order,
  abort_modify_playrules_order,
  set_sort_beg,
  cross_page_sort,
  submit_set_table_sort,
  table_sort,
  row_click,
  click_back,
  row_class_name,
} from "src/components/league_ranking/module/handle.js";
import {
  change_page_num,
  change_page_size,
} from "src/components/league_ranking/module/page.js";
export const src_components_league_ranking_component_table_componsable_fn = (raw_payload) => {
  const payload = { ...raw_payload };
  return {
    click_back: (row) => click_back(payload, row),
    row_click: (item, index) => row_click(payload, item, index),
    table_sort: (row, index, type) => table_sort(payload, row, index, type),
    submit_set_table_sort: (row, index) => submit_set_table_sort(payload, row, index),
    cross_page_sort: (row, index) => cross_page_sort(payload, row, index),
    set_sort_beg: (row, index) => set_sort_beg(payload, row, index),
    abort_modify_playrules_order: () => abort_modify_playrules_order(payload),
    modify_playrules_order: () => modify_playrules_order(payload),
    row_class_name: (row, index) => row_class_name(payload, row, index),
    change_page_num: (v) => change_page_num(payload, v),
    change_page_size: (v) => change_page_size(payload, v),
    swith_change: (row, index, stant) => swith_change(payload, row, index, stant),
    top_morning: (row, val) => top_morning(row, val),
  };
};
