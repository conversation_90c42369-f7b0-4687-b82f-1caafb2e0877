<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Description    : 赛程管理 预开售 等 右侧实时事件 -- 比分统计
 * @FilePath: /src/pages/operation_set/pre_sale_set/component/right_info_event/module/score_statistics.vue
-->
<template>
    <div class="text-panda-text-light">
        <!-- 球队信息  下拉框筛选 -->
        <div
            class="wtHider row p-fwnw p-mxw100p p-jcsb height-40px line-height-40x p1-aic p-mb--xxs pl15x pr15x border-bottom-light">
            <!-- 主队名称 -->
            <div class="Names homeTeamNames" :title="compute_table_item_show_name(right_detail_obj.homeTeamNames)">
                {{ compute_table_item_show_name(right_detail_obj.homeTeamNames) }}
            </div>
            <!-- 斯洛克  英文 赛事阶段下拉框 -->
            <Select v-if="compute_i18n_type && right_detail_obj.sportId == 7" class="bg-panda-field-grey" size="small"
                style="width: 60px" v-model="btn_model">
                <template v-for="item in time_options">
                    <Option :key="item.value" :value="item.value" v-show="(scoresJson[data_source] &&
                            scoresJson[data_source][item.value]) ||
                        item.value == '-1'
                        ">
                        {{ item.value == "-1" ? item.label : "Frame " + item.value }}
                    </Option>
                </template>
            </Select>
            <!--斯洛克中文 以及 斯洛克 之外的球类 赛事阶段下拉框 -->
            <Select v-else class="bg-panda-field-grey" size="small" style="width: 60px" v-model="btn_model">
                <template v-for="item in time_options">
                    <Option :key="item.value" :value="item.value" v-show="(scoresJson[data_source] &&
                            scoresJson[data_source][item.value]) ||
                        item.value == '-1'
                        ">
                        {{ item.label }}
                    </Option>
                </template>
            </Select>
            <!-- 数据源选择下拉框 -->
            <Select class="bg-panda-field-grey p-ml--xxxs" size="small" style="width: 95px" v-model="data_source">
                <template v-for="item in data_source_opts">
                    <Option v-show="ws_all_dataSourceCode &&
                        ws_all_dataSourceCode.includes(item.value)
                        " :key="item.value" :value="item.value">
                        {{ item.label }}
                    </Option>
                </template>
            </Select>
            <!-- 客队名称 -->
            <div class="Names homeTeamNames" :title="compute_table_item_show_name(right_detail_obj.awayTeamNames)">
                {{ compute_table_item_show_name(right_detail_obj.awayTeamNames) }}
            </div>
        </div>
        <!-- 比分统计 -->
        <div v-if="scoresJson[data_source] && scoresJson[data_source][btn_model]" style="height: 170px"
            class="p-gutter-mb--xs p1-oxa p-f1 pl15x pr15x mt10x main_box">
            <div v-for="item in statistics_code_list" :key="item.key">
                <section v-if="scoresJson[data_source][btn_model][item.key]">
                    <h1 class="flex p-jcsb p-lh--xxxs p-fs--m p-mb4x fs12x">
                        <!-- 计算主队得分事件 -->
                        <span class="p-lp-tc--red">{{
                            scoresJson[data_source][btn_model][item.key].home
                            }}</span>
                        <!-- 事件名称 -->
                        <span>{{ item.name }}</span>
                        <!-- 计算客队得分事件 -->
                        <span class="p-lp-tc--orange">{{
                            scoresJson[data_source][btn_model][item.key].away
                            }}</span>
                    </h1>
                    <!-- 红黄线 -->
                    <q-linear-progress :value="compute_item_source_value_new(
                        scoresJson[data_source][btn_model][item.key]
                    )
                        " />
                </section>
            </div>
        </div>

        <div v-else class="row p-jcc pande-text-788299" style="height: 170px">
            <span>{{ $t("score_center.text_6") }}</span><!-- 无实时事件统计 -->
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import { get_ws_match_list_score_center } from "src/output/common/store-common.js";
import { src_componsable_global_variable_componsable_fn } from "src/output/common/componsable-common.js";
//****************** 该组件用于 足 网 乒 冰 斯 羽 美足 等球类***********************
let i18n = sessionStorage.getItem("i18n") || "zs";
const ws_match_list_score_center = get_ws_match_list_score_center();
const global_obj = src_componsable_global_variable_componsable_fn();
const { route } = global_obj;

const props = defineProps({
    // 右侧实时事件--当前选中row信息
    right_detail_obj: {
        type: Object,
        default: () => ({}),
    },
    // 当前球类的比分统计类型下拉框
    statistics_code_list: {
        type: Array,
        default: () => [],
    },
    // 当前球类的 赛事阶段下拉框
    time_options: {
        type: Array,
        default: () => [],
    },
});
comst emit = defineEmits(["change_ws_data_source"]);

const data_source_opts = ref([
    { label: `${props.$t("score_center.text_1")}`, value: "SR" },
    { label: `${props.$t("score_center.text_2")}`, value: "BC" },
    { label: `${props.$t("score_center.text_3")}`, value: "BG" },
    { label: `${props.$t("score_center.text_4")}`, value: "TX" },
    { label: `${props.$t("score_center.text_5")}`, value: "RB" },
]);
const btn_model = ref("-1"); // 比分统计--赛事阶段下拉框
const btn_model_1 = ref("-1"); // 事件流--赛事阶段下拉框
const data_source = ref(""); // 数据源阶段下拉框
const scoresJson = ref({
    // 统计比分对象
    SR: null,
    BC: null,
    BG: null,
    TX: null,
    RB: null,
});
const ws_all_dataSourceCode = ref([]); // 后台推送的ws数据源
//多语言显示球队信息  有当前语言信息的就展示当前语言  语言顺序展示
const compute_table_item_show_name = (name) => {
    let str = "";
    if (!name || typeof name != "object") return;
    str =
        i18n && name[i18n] ? name[i18n] : name.zs || name.jc || name.zh || name.en;
    return str;
};
//计算主客队得分所占比例
const compute_item_source_value_new = (item) => {
    let home = item.home;
    let away = item.away;
    let box = home + away;
    if (box) {
        if (home) {
            return home / box;
        } else {
            return 1;
        }
    } else {
        return 0.5;
    }
};

const compute_i18n_type = computed(() => {
    return sessionStorage.getItem("i18n") == "en";
});

watch(
    () => props.right_detail_obj,
    (val) => {
        data_source.value = val.dataSourceCode;
        btn_model.value = "-1";
        scoresJson.value = {}; // 清空比分对象
        sessionStorage.setItem("ws_dataSourceCode", val.dataSourceCode);
    }
);

// 切换数据源  更新 ws 请求
watch(
    () => data_source.value,
    (val) => {
        if (!val) return;
        // 切换数据源
        emit("change_ws_data_source", val);
        sessionStorage.setItem("ws_dataSourceCode", val);
    }
);

watch(ws_match_list_score_center, (val) => {
    if (!val || typeof val != "object") return;
    let { data } = val;
    if (!Array.isArray(data) || data.length == 0) return;
    let id_type = "matchInfoId";
    if (["match_manage", "event_review"].includes(route.name)) {
        // 赛程管理 事件审核 id_type 字段对应id  预开售 开售页面取matchInfoId
        id_type = "id";
    }
    // 通过matchId匹配 给 scoresJson 赋值
    data.forEach((item) => {
        if (item.matchId == props.right_detail_obj[id_type]) {
            data_source_opts.value.forEach((qst) => {
                if (!item.scoresJson || !qst.value) return;
                if (item.scoresJson[qst.value]) {
                    scoresJson.value[qst.value] = item.scoresJson[qst.value];
                }
                ws_all_dataSourceCode.value = item.allDataSourceCode;
                if (sessionStorage.getItem("ws_dataSourceCode")) {
                    data_source.value = sessionStorage.getItem("ws_dataSourceCode");
                }
            });
        }
    });
});

onMounted(() => {
    data_source.value = props.right_detail_obj.dataSourceCode;
});
onBeforeUnmount(() => {
    sessionStorage.removeItem("ws_dataSourceCode");
});
</script>

<style lang="scss" scoped>
@import "project/caopan/pages/operation_set/pre_sale_set/component/right_info_event/module/css/index.scss";
</style>
