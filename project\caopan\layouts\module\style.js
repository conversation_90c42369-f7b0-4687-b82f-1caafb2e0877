
// import { src_componsable_global_variable_componsable_fn } from "src/output/common/componsable-common.js"
import {
  set_window_size_info,
  set_themeColors,

} from "src/output/common/store-common.js"



export const compute_container_style = (payload, size)=>{
  const {page_container_style} = payload
  // 最小14寸屏幕兼容
  if (!size) {
    size = {
      width: window.innerWidth >= 1366 ? window.innerWidth : 1366,
      height: window.innerHeight >= 600 ? window.innerHeight : 600
    };
  }
  let hc = window.innerHeight - 4;
  page_container_style.value = {
    width: size.width + "px",
    minWidth: "1366px",
    height: size.height + "px"
  };
}

export  const onResize = (payload)=>{

  compute_container_style(payload)
  set_window_size_info({
      width: document.documentElement.clientWidth,
      height: document.documentElement.clientHeight,
  })
}



/* 计算第二级菜单开始 */
export  const compute_menu_bg_class = (payload,item)=>{
  const { menu_active_name_pool} = payload
  let str = ``;
  if (menu_active_name_pool.value.indexOf(item.name) > -1) {
    str = `panda-menu-select`;
  } else {
    str = "panda-menu-base";
  }
  return str;
}

export  const compute_menu_icon_class = (payload,item)=>{
  const { menu_active_name_pool} = payload
  let str = `${item.icon}`;
  if (menu_active_name_pool.value.indexOf(item.name) > -1) {
    str = `${str}  panda-icon-active`;
  } else {
    str = `${str}  panda-icon-base`;
  }
  return str;
}

export  const compute_menu_label_class = (payload,item)=>{
  const { menu_active_name_pool} = payload
  let str = ``;
  if (menu_active_name_pool.value.indexOf(item.name) > -1) {
    str = `${str}  panda-icon-active`;
  } else {
    str = `${str}  panda-icon-base`;
  }
  return str;
}

/* 计算第二级菜单结束 */
export  const compute_menu_1_class = (payload,item)=>{
  const { menu_active_name_pool} = payload
  let str = ``;
  if (menu_active_name_pool.value.indexOf(item.name) > -1) {
    str = `${str} panda-q-item-level-one-active bg-p1-menu__item`;
  } else {
    str = `${str} bg-p1-menu`;
  }
  return str;
}

export  const compute_moving_direction = (e)=>{

  // 获取容器的相关属性
  let target = e.target.getBoundingClientRect();
  let w = target.width
  let h = target.height
  let offsetLeft = target.left
  let offsetTop = target.top
  // let dirName = new Array('top', 'right', 'bottom', 'left');
  let x = (e.pageX - offsetLeft - (w / 2)) * (w > h ? (h / w) : 1);
  let y = (e.pageY - offsetTop - (h / 2)) * (h > w ? (w / h) : 1);
  // 用来获取移入的方位
  // 通过Math.atan2 计算角度
  let direction = Math.round((((Math.atan2(y, x) * (180 / Math.PI)) + 180) / 90) + 3) % 4;
  return direction
}

export  const change_theme = (payload,data)=>{
  const {theme, theme_class, typeUi} = payload
  // 项目 基调背景色
  // $panda-base-light     ='' #242B38
  // $panda-base-dark     = #1B212C
  // $panda-dark-light  = #23324
  // $panda-dark-dark  = #121720
  // // 项目 文字颜色
  // $panda-text-light  = #D5DEE7
  // $panda-text-dark  = #788299
  // colors.setBrand('panda-base-light', '#fff',document.getElementsByTagName("body")[0])
  theme.value = data;
  sessionStorage.setItem("theme", theme.value);
  localStorage.setItem("Skin",data); //请不要随便删改
  theme_class.create_theme({ data });
  typeUi.value = data;
  console.warn('主题色切换--')
  set_themeColors(data)
}

export  const compute_active_menu_level = (payload, route)=>{


  const {menu_active_name_pool} = payload

  menu_active_name_pool.value = [];

  if (route.name.includes("basesetting_playrules")) {
    menu_active_name_pool.value.push("setting_container");
    menu_active_name_pool.value.push("basesetting_playrules");
  }else if (route.name.includes("settlement_match") || route.name.includes("settlement_handicap")) {
    const arr = ["settlement_handicap", ]
    // 处于子页面时 使菜单处于激活状态 并不能跳转
    menu_active_name_pool.value.push("settlement_handicap")
    menu_active_name_pool.value.push("settlement_match")
    menu_active_name_pool.value.push("settlement_center") // 让2级菜单变为选中状态
    menu_active_name_pool.value.push("settlement_manage") // 让1级菜单变为选中状态
  } else if (route.name.includes("bulletin_board_create") || route.name.includes("bulletin_board_edit")) {
    // 运营管理-公告栏-编辑/发布公告 菜单栏选中
    menu_active_name_pool.value.push("bulletin_board");
    menu_active_name_pool.value.push("operations_management");
  } else {
    if (route.matched[1] && route.matched[1].name == "match_container") {
      menu_active_name_pool.value.push(route.matched[1].name);
      let str = route.matched[2].name
        .split("_")
        .slice(0, 2)
        .join("_");
      menu_active_name_pool.value.push(str);
    } else {
      let list = route.matched;
      for (let i = list.length - 1; i >= 0; i--) {
        let item = list[i];
        let parent = item.parent;
        let redirect = parent && parent.redirect;
        let push = name => {
          if (name && !menu_active_name_pool.value.includes(name)) {
            menu_active_name_pool.value.push(name);
          }
        };
        if (redirect) {
          if (redirect.name === item.name) {
            push(parent.name);
          } else {
            push(item.name);
          }
        } else {
          push(item.name);
        }
      }
      menu_active_name_pool.value.reverse();
    }
  }
}
