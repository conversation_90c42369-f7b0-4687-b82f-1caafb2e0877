import {
    Message,
    i18n_t,
    lodash
} from "src/output/common/project-common.js"



// 复制赛事id
export const copy_id = (value) => {
    if (!value) return
    let oInput = document.createElement('input');
    oInput.value = value;
    document.body.appendChild(oInput);
    oInput.select();
    document.execCommand("Copy");
    oInput.remove()
    Message.success(`${i18n_t('event.right.w4')}：${value}`) // 已复制赛事ID
}

// 赛事对阵 独赢 弹窗
export const p_ups = (playload,row)=> {
  const {p_ups} = playload
  // this.$emit('p_ups', row)
  p_ups(row)
}

// // 查看日志
// export const log_click =( row) => {
//     emit('log_click',row)
// }

// 查看日志
export const log_click =(playload, row) => {
    const { show_presell_log, right_detail_log } = playload
    show_presell_log.value = true;
    right_detail_log.value = row;
}

export const compute_dataSourceCode_fullname =(playload,str) => {
    const { dataSource_obj } = playload
    if (!str) {
      return;
    }
    let obj = lodash.find(dataSource_obj.value.raw_dataSource, el => {
      return el.code == str;
    });
    if (obj && obj.fullName) {
      return obj.fullName;
    }
    return str;
  }
