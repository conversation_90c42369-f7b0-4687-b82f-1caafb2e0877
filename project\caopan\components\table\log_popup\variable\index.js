
import {ref} from 'vue'
const {operatePageList_default} = 'project/caopan/components/table/log_popup/config/index.js'

export const create_base_state_instance_fn =(raw_payload)=>{

   const { route, search, open } = raw_payload

   // 路由
  const  url_data = ref(route) ;
    const update_key = ref(false)  ;
     //显示列表数据
  const  table_data = ref([]) ;
     //节
  const  isPost = ref(false) ;
     //加载状态
  const  loading = ref(false) ;
     //无数据
  const  nodata = ref(true) ;
     //总条数
  const  total = ref(0) ;
     //搜索状态
  const  search_state = ref(search.value) ;
     //表格展示
  const  columns = ref([]) ;
     //记住
  const  traders_log_remember = ref(false) ;
    const newline = ref(false)  ;
    const routerName = ref("")  ;
     // 报球版刷新按钮
  const  hrefLog = ref(false) ;
   //报球网球版下拉框
    const objectNameListShow = ref(false)  ;
    const sport_list = ref([])  ;
 
 //翻页参数
  const  post_params = ref({
     pageNum: 1,
     pageSize: open.value ? 20 : 10,
     page: 1,
     size: 20,
   }) ;
 
 
  const  search_params = ref({
     operatePageCode: null, //操作页面代码
     objectId: null, //操作对象id
     objectName: null, //操作对象名称
     extObjectId: null, //操作对象扩展ID
     extObjectName: null, //操作对象扩展名称
     behavior: null, // 操作类型
     userId: null, // 操作人id
     userName: null, // 操作人
     operateStartTime: null, // 开始事件，
     operateEndTime: null, // 结束时间
     // operateTargetNameEn:null,//操作对象英文名称
     // extOperateTargetNameEn:null,//操作对象扩展英文名称
     // uid:null,//操作人
     // beginTime:null,
     // endTime:null,
     sportId: null, // 赛种
     matchManageId: null, // 赛事ID
   }) ;
 
 //配置项
   const  config = ref({
     // To DO
     operatePageList:operatePageList_default,
     playList: [],
     operateTypeList: [],
     request_api: "t_rcsCommenLogInfos", // 默认Api接口
   }) ;
 

   return {    
       url_data,
      update_key,
       table_data,
       isPost,
       loading,
       nodata,
       total,
       search_state,
       columns,
       traders_log_remember,
      newline,
      routerName,
       hrefLog,
      objectNameListShow,
      sport_list,
       post_params,
       search_params,
       config,
   }
}

