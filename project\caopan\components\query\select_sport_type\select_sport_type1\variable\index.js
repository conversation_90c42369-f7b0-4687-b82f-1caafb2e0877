import { ref } from 'vue';

export const create_base_state_instance_fn = () => {
  const sport_type_constant_lt_8 = ref([]);
  const sport_type_constant_gt_8 = ref([]);
  // 头部赛种 超出一定数量展示
  const show_fixed_more_sport_type = ref(false);
  const left_side_style = ref({});
  const right_side_style = ref({});
  const per_sportType_style = ref({});
  const more_sportType_btn_style = ref({});
  const sport_type_constant_copy = ref([]);
  // 展示赛事数量
  const showNum = ref(true);
  // 运动球类
  const select_sportId = ref(Number(sessionStorage.getItem("sportId")) || 1);
  // 展示冠军 红点
  const show_champion_badge = ref(true);
  // 只显示虚拟体育
  const only_show_virtual = ref([
    'virtual_play_management',
    'virtual_play_collection_manage',
    'virtual_play_collection_manage_main',
    'virtual_play_collection_manage_edit',
  ]);
  // 只显示电子竞技
  const only_show_e_sports = ref(['e_play_management', 'e_play_collection_manage', 'e_play_collection_manage_main', 'e_play_collection_manage_edit']);

  const componentKey = ref(Date.now());

  return {
    sport_type_constant_lt_8,
    sport_type_constant_gt_8,
    show_fixed_more_sport_type,
    left_side_style,
    right_side_style,
    per_sportType_style,
    more_sportType_btn_style,
    sport_type_constant_copy,
    showNum,
    show_champion_badge,
    only_show_virtual,
    only_show_e_sports,
    componentKey,
    select_sportId,
  };
};
