import { computed_generator } from "project/caopan/components/query/match_left_query4/computed/index.js";

import {
  src_componsable_global_variable_componsable_fn,
 
} from "src/output/common/componsable-common.js";
import { create_base_state_instance_fn } from "project/caopan/components/query/match_left_query4/variable/index.js";
export const project_caopan_components_query_match_left_query4_variable_componsable_fn =
  () => {
    const base_state = create_base_state_instance_fn();
    const global_obj = src_componsable_global_variable_componsable_fn();

    const all_computed = computed_generator({
      ...base_state,
      ...global_obj,
     
    });
    return {
      ...base_state,
      ...global_obj,
      ...all_computed,
    };
  };
