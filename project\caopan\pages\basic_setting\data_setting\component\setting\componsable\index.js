import {
  change_select,
  change_switch,
  show_hint,
  submit,
} from "project/caopan/pages/basic_setting/data_setting/component/setting/module/index.js";
import { emit_generator } from "project/caopan/pages/basic_setting/data_setting/component/setting/emit/index.js";
import { watcher_register } from "project/caopan/pages/basic_setting/data_setting/component/setting/watch/index.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";
export const project_caopan_pages_basic_setting_data_setting_component_setting_componsable_index_componsable_fn =
  (raw_payload) => {
    const payload = {
      ...raw_payload,
    };
    src_componsable_util_watcher_register_componsable_fn(
      watcher_register(payload)
    );
    const emit_methods = emit_generator(payload);
    return {
      ...emit_methods,
      change_select: () => change_select(payload, v, k),
      change_switch: () => change_switch(payload, type),
      show_hint: () => show_hint(payload, type),
      cancel: () => cancel(payload),
      submit: () => submit(payload),
    };
  };
