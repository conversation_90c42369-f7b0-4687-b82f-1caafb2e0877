/**
 * @description: 时间维度点击
 * @param {*} v
 * @return {*}
 */
export const item_click = (payload, item) => {
    if (timeType_num.value == item.value + (item.type ? item.type : '')) {
        return
    }
    let v = item.value
    let time = get_before_time("d", { type: "d", value: 0 }, true).trim()
    if (item.type) {
        timeType_num.value = v + item.type
    } else {
        timeType_num.value = v
    }
    let obj = btnList.value.find(item => timeType_num.value == item.value + (item.type ? item.type : '')) || {}
    if (obj.value == "custom") {
        times.value = [...customTime.value]
    } else {
        if (obj.type == 'm') {
            let timeArr = [time]
            let date = new Date(time)
            let day = time.slice(8)
            let y = date.getFullYear()
            let m = date.getMonth() + 1
            let n_m = (m < 10) ? `0${m}` : m

            // 本月等逻辑
            if (obj.value == 0) {
                timeArr.unshift(`${y}-${n_m}-01`)
            }
            else if (Math.abs(obj.value) >= m) {
                // 月份不够
                y = (y - 1)
                n_m = 12 - (Math.abs(obj.value) - m)
                n_m = (n_m < 10) ? `0${n_m}` : n_m
                timeArr.unshift(`${y}-${n_m}-${get_day(n_m, day, y)}`.trim())
            } else {
                let n1_m = m - Math.abs(obj.value)
                n1_m = (n1_m < 10) ? `0${n1_m}` : n1_m
                timeArr.unshift(`${y}-${n1_m}-${get_day(n1_m, day, y)}`.trim())
            }
            times.value = timeArr
        } else {
            times.value = [
                get_before_time("d", { type: "d", value: obj.value }, true).trim(),
                time,
            ];
        }
    }
    emit('change_time', timeType_num.value, times.value)
}
/**
 * @description: 自定义时间框的值
 * @param {*} n
 * @return {*}
 */
export const time_on_change = (payload, n) => {
    emit('change_time', timeType_num.value, n)
}
export const get_day = (payload, m, d, y) => {
    let arr = [1, 3, 5, 7, 8, 10, 12]
    // 大月
    if (arr.some(item => item == m)) {
        return d
    } else if (m == 2) {
        // 闰年 能被4整除且不能被100整除的为闰年,或能被100整除且能被400整除
        let r = ((y % 4) === 0) && ((y % 100) !== 0) || ((y % 400) === 0)
        if (d > 28) {
            return r ? d : 28
        } else {
            return d
        }
    } else {
        // 小月
        return d > 30 ? 30 : d
    }
}