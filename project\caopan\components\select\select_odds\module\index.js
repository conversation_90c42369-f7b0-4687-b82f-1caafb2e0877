import { set_market_odds_kind } from "src/output/common/store-common.js";

export const init_data = (payload) => {
  const { market_odds_kind } = payload;
  let getmarket_odds_kind = localStorage.getItem("set_market_odds_kind");
  if (getmarket_odds_kind) {
    set_market_odds_kind(getmarket_odds_kind);
    model.value = getmarket_odds_kind;
  } else {
    localStorage.setItem("set_market_odds_kind", "MY");
    model.value = market_odds_kind.value;
  }
};
export const modify_odds_fields = (payload, val) => {
  set_market_odds_kind(val);
  localStorage.setItem("set_market_odds_kind", val);
};
