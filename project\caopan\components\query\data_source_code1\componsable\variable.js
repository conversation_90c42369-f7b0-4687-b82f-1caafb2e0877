

///共用的store
import {
  } from "src/output/common/store-common.js";
  
  
  ///共用模块的 composeable variable 方法。用于解构 variable
  import {
    src_componsable_global_variable_componsable_fn,
    // project_saicheng_componsable_table_base_config_variable_componsable_fn,
    // project_saicheng_componsable_table_table_query_variable_componsable_fn,
    // project_saicheng_componsable_constant_query_variable_componsable_fn,
    // project_saicheng_componsable_pages_match_selected_table_item_variable_componsable_fn,
    // project_saicheng_componsable_router_cache_handler_variable_componsable_fn,
    // project_saicheng_componsable_pages_match_match2_variable_componsable_fn,
    // project_saicheng_componsable_style_window_resize2_variable_componsable_fn,
    // project_saicheng_componsable_layout_rightinfo_variable_componsable_fn
  
  } from "src/output/common/componsable-common.js";
  
  ///本页面的variable 文件，引入
  import { create_base_state_instance_fn } from "project/caopan/components/query/data_source_code1/variable/index.js";
  
  ///本页面的computed 数据，引入
  import { computed_generator } from "project/caopan/components/query/data_source_code1/computed/index.js";
  
  ////XXXXXXXXXXX 按照 上面的命名规则，
  export const  XXXXXXXXXX_variable_componsable_fn =
    () => {
  
      /// variable。js 多例语法 export 导出
      const base_state = create_base_state_instance_fn();
  
  
      ///全局的 composeable variable fun
      const global_obj = src_componsable_global_variable_componsable_fn();
  
      // ---------- 以下是 共用逻辑的variable，有用到在加------------
  
    //   const table_base_config_state =
    //     project_saicheng_componsable_table_base_config_variable_componsable_fn();
    //   // 查询常量
    //   const constant_query_state =
    //     project_saicheng_componsable_constant_query_variable_componsable_fn();
    //   const match_selected_table_item_state =
    //     project_saicheng_componsable_pages_match_selected_table_item_variable_componsable_fn();
    //   // 表格查询页面的通用参数生成  逻辑组合
    //   const table_query_state =
    //     project_saicheng_componsable_table_table_query_variable_componsable_fn();
    //   const pages_match_match2_state =
    //     project_saicheng_componsable_pages_match_match2_variable_componsable_fn();
    //   const router_cache_state =
    //     project_saicheng_componsable_router_cache_handler_variable_componsable_fn();
    //   const style_window_resize_state  =project_saicheng_componsable_style_window_resize2_variable_componsable_fn()
    //   const  layout_rightinfo_state= project_saicheng_componsable_layout_rightinfo_variable_componsable_fn()
      // ---------- 以上是 共用逻辑的variable，有用到在加------------
  
  
  
      ///本页逻辑的 computed 方法集成。
      const all_computed = computed_generator({
        ...base_state,
        ...global_obj,
      });
  
      // 最终集成所有的 variable（本页+共用）的参数，成一个大型payload
      const payload = {
        ///这三个必要内容
        ...base_state,
        ...global_obj,
        ...all_computed,
  
      };
  
      return payload;
    };
  