

  // 用户风控标签
  export const change_select = (payload,v, k) => {
    const {
      params,
 
    } = payload
    params.value[k]=v
  }
  // 漏单比例验证
  export const change_switch = (payload,type) => {
    const {
      params,
      label_hint,
      limit_open,
      miss_focus,
    } = payload
    if (type == 1) {
      params.value.bqStatus==1 && params.value.levelId.length==0 ? label_hint.value=true : label_hint.value=false
    } else {
      if(params.value.qjStatus == 1 && params.value.volumePercentage==null) {
        params.value.volumePercentage = 100
        limit_open.value = true
        miss_focus.value = false
      }
    }
  }
  // 输入框聚焦时提示语设置
  export const show_hint = (payload,type) => {
    const {
      params,
      limit_open,
      miss_focus,
      betting_amount,
    } = payload
    // type为1 则漏单比例输入框提示语显示  type不为1则投注额区间提示语显示
    if(type == 1) {
      limit_open.value = false
      if(!params.value.volumePercentage && params.value.volumePercentage != 0){
        miss_focus.value = true
      }else{
        miss_focus.value = false
      }
    } else{
      // 如果投注额区间都有输入值
      if(params.value.minMoney && params.value.maxMoney){
        params.value.minMoney > params.value.maxMoney ? betting_amount.value=true : betting_amount.value =false
      }else{
        betting_amount.value =false
      }
    }
  }
  // 取消
  export  const cancel =   (payload)=>{
const {props} =payload
const {edit_data}=props
    const {
      params,
      merchantsIds,
    } = payload

    if(edit_data.value){
      params.value = JSON.parse(JSON.stringify(edit_data.value))
    }else{
      merchantsIds.value=''
    }
    emit('setting_close',false)
  }
  // 提交
  export const submit = (payload) => {
   const {props}=payload
   const {set_modal}=props
    const {
      params,
      label_hint,
      miss_focus,
      betting_amount,
      merchantsIds,
 
      
    } = payload
    let param = Object.assign({},params.value)
    let post = {
      type:set_modal.value.type,//参数设置 1:批量設置、2:默認設置、3:例外設置
      volumePercentage: param.volumePercentage, // 漏单比例值
      levelId:param.levelId, // 用户风控标签
      bqStatus: param.bqStatus, // 投注特征标签漏单比例设置
      qjStatus: param.qjStatus, // 全局漏单比例设置
      minMoney: param.minMoney, // 投注额区间小值
      maxMoney: param.maxMoney, // 投注额区间大值
      merchantIds: [] // 商户ID
    }
    // param.type = set_modal.value.type
    // 参数验证
    // 漏单比例验证
    if(post.volumePercentage!=0 && !post.volumePercentage)
    return miss_focus.value = true
    // 用户标签验证
    console.log(post)
    if(post.bqStatus==1 && post.levelId.length==0)
    return label_hint.value=true
    // 投注额验证
    if(post.maxMoney && post.maxMoney && post.maxMoney < post.minMoney)
    return betting_amount.value=true
    // 商户id验证
    // type 2  默认设置
    if(post.type==2){
      post.merchantIds=[]
    }else{
      //  settype  3 编辑
      if(set_modal.value.settype==3){
        post.merchantIds.push(param.merchantsId.toString())
        post.id=1
      }else{
        post.merchantIds = merchantsIds.value ? merchantsIds.value.split(',').slice(0,100) : []
      }
    }
    merchantsIds.value=''
    emit('submit',post)
    emit('setting_close',false)
  }
