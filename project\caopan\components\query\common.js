class Submit_data {
  constructor (arg = {})  {
    this.keys = arg.keys || [];
  }
  set_key_all = (arg) => {
    if (!arg || typeof arg !== "object") return;
    let { data, keys } = arg;
    keys = keys || keys.value;
    if (!data || typeof data !== "object" || !Array.isArray(keys)) return;
    keys.forEach(x => {
      if (data[x] === "") {
        data[x] = "all_";
      }
    });
  }
  recover_key_all = (arg) => {
    if (!arg || typeof arg !== "object") return;
    let { data, keys } = arg;
    keys = keys || this.keys;
    if (!data || typeof data !== "object" || !Array.isArray(keys)) return;
    keys.forEach(x => {
      if (data[x] === "all_") {
        data[x] = "";
      }
    });
  }
}

export default Submit_data;
