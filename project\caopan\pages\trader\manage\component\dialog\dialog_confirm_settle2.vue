<!--
 * @FilePath: /project/caopan/pages/trader/manage/module/table_row/module/dialog_confirm_settle2.vue
 * @Description:
-->
<template>
  <div class="use-ivew" style=" height: 450px;min-width: 950px; margin: 0 20px; overflow-x: hidden; background: var(--q-color-panda-base-dark);">
    <!-- <q-card class="fs14 bg-panda-light-light text-panda-text-light" > -->
      <q-bar class="drag">
        <q-space />
        <q-btn dense flat icon="close" v-close-popup></q-btn>
      </q-bar>
    <!-- </q-card> -->
    <div style="width: 100%; height: calc(100% - 32px); overflow-y: auto; overflow-x: hidden;">
      <!-- 进球类 -->
      <ul class="custom_table" v-if="settle_params.categorySetId == 10003 || settle_params.categorySetId == 10001">
        <li :class="['list_li','list_li_title']">
            <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_55')}}</div><!-- Goal -->
            <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_56')}}</div><!-- Home -->
            <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_57')}}</div><!-- Away -->
            <div class="list_li_box col_3">{{i18n_t('settlement_v2.v_58')}}</div><!-- Action -->
            <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_59')}}</div><!-- Outstanding -->
        </li>
        <li
            :class="['list_li',  [101,105,109,1010,1014,1018,1019].includes(item.settleNum)&&'list_li_border_bottom', item.needCheck==1&&'li_yellow' ]"
                v-for="(item, index) in list_table_data" :key="'yige' + item.id + index">
            <div class="list_li_box list_li_box_left col_2">{{ get_name(item.settleNum) }}</div>
            <template v-if="[1021, 1031, 1032, 1033].includes(item.settleNum)">
                <div style="display:flex;">
                    <div class="list_li_box col_2">
                        <template v-if="item.is_show">
                            <div>
                                {{the_winning_way(item)}}
                            </div>
                        </template>
                        <template v-else>
                            <!-- 获胜方式 -->
                            <iSelect class="bg-panda-field-grey mr5x" v-if="item.settleNum == 1021" v-model="item.extryInfo_a" :placeholder="i18n_t('settlement_v2.v_20')" size="small" style="width:140px">
                                <iOption v-for="val in list_select" :value="val.value"  :key="val.value">{{val.label}}</iOption>
                            </iSelect>
                            <!-- 是否点球大战 -->
                            <iSelect class="bg-panda-field-grey mr5x" v-if="item.settleNum == 1031" v-model="item.extryInfo_a" :placeholder="i18n_t('settlement_v2.v_20')" size="small" style="width:140px">
                                <iOption v-for="val in list_select_dian" :value="val.value"  :key="val.value">{{val.label}}</iOption>
                            </iSelect>
                            <!-- 没有进行加时赛 & 点球大战 (走水)   没有进行点球大战 (走水)  -->
                            <iSelect class="bg-panda-field-grey mr5x" v-if="[1032, 1033].includes(item.settleNum)"
                                v-model="item.extryInfo_a" :placeholder="i18n_t('settlement_v2.v_20')" size="small" style="width:140px">
                                <iOption v-for="val in list_select_zou" :value="val.value"  :key="val.value">{{val.label}}</iOption>
                            </iSelect>
                        </template>
                    </div>
                </div>
            </template>
            <template v-else>
                <div style="display:flex;">
                    <div class="list_li_box col_1">
                        <template v-if="item.is_show">
                            <div>
                                {{item.t1}}
                            </div>
                        </template>
                        <template v-else>
                            <i-input
                                v-model="item.t11"
                                style="width:70px;height:28px;padding: 4px 5px">
                            </i-input>
                        </template>
                    </div>
                    <div class="list_li_box col_1">
                        <template v-if="item.is_show">
                            <div>
                                {{item.t2}}
                            </div>
                        </template>
                        <template v-else>
                            <i-input
                                v-model="item.t22"
                                style="width:70px;height:28px;padding: 4px 5px;">
                            </i-input>
                        </template>
                    </div>
                </div>
            </template>
            <!--   list_icon_box   icon控制的主样式容器   -->
            <div class="list_li_box col_3 list_icon_box">
                <template v-if="item.is_show">
                    <span :key="'icon_1' + index"
                        @click="set_event_score(item)" :title="i18n_t('settlement_v2.v_5')"
                        :class="['list_icon', [1,2,3].includes(item.status) && 'list_icon_11',item.settleCount>0 && 'list_icon_111']"></span>
                    <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                    <span
                        :key="'icon_2' + index" @click="item.status == 1 && confirmation_score(item,4)"
                        :title="i18n_t('settlement_v2.v_6')" :class="['list_icon', item.status != 1 && 'list_icon_d', [2,3].includes(item.status) && 'list_icon_22']"></span>
                    <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                    <span :key="'icon_3' + index"
                        @click="settlement_score(item, 4)" :title="i18n_t('settlement_v2.v_7')" :class="['list_icon', [3].includes(item.status) && 'list_icon_33']"></span>
                    <span :key="'icon_4' + index" @click="re_stage_score_settlement(item)" :title="i18n_t('settlement_v2.v_8')" class="list_icon"></span>
                    <span :key="'icon_5' + index" @click="rollback_stage_score_settlement(item)" :title="i18n_t('settlement_v2.v_9')" class="list_icon"></span>
                    <span :key="'icon_6' + index" @click="get_unsettle_order_num(item)" :title="i18n_t('settlement_v2.v_10')" class="list_icon"></span>
                    <!-- <span :key="'icon_7' + index" :title="1 == 2 ? i18n_t('settlement_v2.v_13') : i18n_t('settlement_v2.v_14')" :class="[1 == 2 ? 'list_icon' : 'list_icon_dong']"></span> -->
                    <!-- 取消冻结 -->
                    <span :key="'icon_10118' + index" @click="set_freeze_settle(1, {...item, type: 1, is_cmp: true})"
                        v-if="item.scoresPeriodFreeze == 1" :title="i18n_t('settlement_v2.v_14')" class="list_icon_dong"></span>
                    <!-- 结算冻结 -->
                    <span :key="'icon_7' + index" @click="set_freeze_settle(1, {...item, type: 1, is_cmp: true})" v-else :title="i18n_t('settlement_v2.v_13')" class="list_icon"></span>
                </template>
                <template v-else>
                    <span :key="'icon_8' + index" @click="set_score_num(item)" :title="i18n_t('settlement_v2.v_11')" class="list_icon list_icon_2"></span>
                    <span :key="'icon_9' + index" @click="set_close_is_show(item)" :title="i18n_t('settlement_v2.v_12')" class="list_icon list_icon_2"></span>
                </template>
            </div>
            <div class="list_li_box col_1">{{item.outstanding || '0'}}</div>
        </li>
        <li :class="['list_li']">
            <iIcon type="ios-arrow-forward" size="16"/>
        </li>
      </ul>
      <!-- 点球类 -->
      <ul class="custom_table" style="margin-bottom: 50px;" v-if="settle_params.categorySetId == 10004">
        <li :class="['list_li','list_li_title']">
            <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_136')}} &nbsp;<span class="icon_refresh" @click="search_penalty_scores(2, penalty_kick_data.jie1_5.standardMatchId)"></span></div>
            <!-- 默认固定五场，后续可以增加 -->
            <div class="list_li_box col_1" v-for="item in 5" :key="'title_00' + item">
                <small :key="'title_span' + item" :class="[item % 3 == 100 && 'dian_r']">{{item}}</small>
                &nbsp;&nbsp;&nbsp;
                <iIcon v-show="item == 5" :style="{cursor: is_click_stop ? 'pointer' : 'no-drop'}"
                    @click="add_penalty_scores(penalty_kick_data.jie1_5.standardMatchId)" type="md-add-circle" size="18" />
            </div>
            <div class="list_li_box col_1 xiaoshou" @click="set_show_cmp('5')">
                <small>{{i18n_t('settlement_v2.v_98')}}</small>
            </div>
            <template v-if="penalty_kick_data && penalty_kick_data.num > 0">
                <div class="list_li_box col_1" v-for="item in penalty_kick_data.num" :key="'title_***' + item">
                    <small :key="'title_span' + item" :class="[false && 'dian_r']">{{item + 5}}</small>
                </div>
            </template>
            <div class="list_li_box col_1 xiaoshou" @click="set_show_cmp('all')">
                <small>{{i18n_t('settlement_v2.v_97')}}</small>
            </div>
        </li>
        <li :class="['list_li', (penalty_kick_data && penalty_kick_data.type) == 'home' && 'list_li_a']"  v-if="(penalty_kick_data && penalty_kick_data.home)">
            <template>
                <div
                    v-for="(val, key) in penalty_kick_data.home"
                    @click="set_show_cmp('1', val, key, 'home')"
                    :class="['list_li_box', key == 0 ? 'col_2' : 'col_1', ![0,5,(penalty_kick_data.home.length - 1)].includes(key) && 'xiaoshou']"
                    :key="'col_list_home' + key">
                    {{
                        key == 0 ? val.name :
                            (key == 6 || key == (penalty_kick_data.home.length - 1)) ? val.t1 :
                            (['0', '1', '-1'].includes(val.extryInfo) ? ((val.extryInfo == '-1' ? 'X' : val.extryInfo) + ' | ' + val.t1 + ' - ' + val.t2) : '')
                    }}
                </div>
            </template>
        </li>
        <li :class="['list_li', (penalty_kick_data && penalty_kick_data.type) == 'away' && 'list_li_a']" v-if="(penalty_kick_data && penalty_kick_data.away)">
            <template>
                <div v-for="(val, key) in penalty_kick_data.away"
                    @click="set_show_cmp('1', val, key, 'away')"
                    :class="['list_li_box', key == 0 ? 'col_2' : 'col_1', ![0,5,(penalty_kick_data.home.length - 1)].includes(key) && 'xiaoshou']" :key="'col_list_away' + key">
                    {{
                        key == 0 ? val.name :
                            (key == 6 || key == (penalty_kick_data.away.length - 1)) ? val.t2 :
                            (['0', '1', '-1'].includes(val.extryInfo) ? ((val.extryInfo == '-1' ? 'X' : val.extryInfo) + ' | ' + val.t1 + ' - ' + val.t2) : '')
                    }}
                </div>
            </template>
        </li>
        <li :class="['list_li list_li_heng']">
            <iIcon type="ios-arrow-forward" size="16"/>
        </li>
      </ul>

       <!-- 加时类 -->
      <ul class="custom_table" v-if="settle_params.categorySetId == 10003  || settle_params.categorySetId == 10001 ">
        <li :class="['list_li','list_li_title']">
            <!-- 进球 -->
            <div class="list_li_box col_1" style="justify-content: left;">{{i18n_t('settlement_v2.v_55')}}</div>
            <!-- 阶段 -->
            <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_63')}}</div>
            <!-- 主 / 客 -->
            <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_61')}}</div>
            <!-- 比分 -->
            <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_65')}}</div>
            <!-- 操作 -->
            <div class="list_li_box col_3">{{i18n_t('settlement_v2.v_58')}}</div>
            <!-- 未结算 -->
            <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_59')}}</div>
        </li>
        <li
          :class="[
            'list_li',
            (settle_params.categorySetId !== 10003 && list_table_event_data[index + 1] && item.settleNum !== list_table_event_data[index + 1].settleNum)&&'list_li_border_bottom',
            (settle_params.categorySetId == 10003 && item.isBottom) && 'list_li_border_bottom'
            ]"
            v-for="(item, index) in list_table_event_data" :key="'sange' + item.id">
              <!-- {{list_table_event_data[index + 1] && list_table_event_data[index + 1].settleNum}} -->
            <div class="list_li_box col_1" style="justify-content: left;">
                <!-- {{item.stage + ' ' + item.stage_key}}  -->
                {{item.stage_key}}
                &nbsp;&nbsp;&nbsp;
                <iIcon v-show="item.is_show_icon"  :style="{cursor: is_click_stop ? 'pointer' : 'no-drop'}"  @click="add_match_settle_event(item, 'goal')" type="md-add-circle" size="18" />
            </div>
            <div class="list_li_box col_2">{{item.stage}}</div>
            <div class="list_li_box col_2">
                <template v-if="item.is_show">
                    <div>
                        {{((item.t1 || item.t1 == '0') || (item.t2 || item.t2 == '0')) ? get_home_away_str(item.homeAway) : ''}}
                    </div>
                </template>
                <template v-else>
                    <iSelect class="bg-panda-field-grey mr5x" v-model="item.homeAway_a" size="small"  :placeholder="i18n_t('settlement_v2.v_20')" style="width:85px">
                        <iOption v-for="item in list_select_home_away" :key="item.value" :value="item.value">{{item.label}}</iOption>
                    </iSelect>
                </template>
            </div>
            <div class="list_li_box col_1">{{item.t1}} - {{item.t2}}</div>
            <!--   list_icon_box   icon控制的主样式容器   -->
            <div class="list_li_box col_3 list_icon_box">
                <template v-if="item.is_show">
                    <span :key="'icon_11' + index" @click="set_event_score(item)" :title="i18n_t('settlement_v2.v_5')"
                        :class="['list_icon', [1,2,3].includes(item.status) && 'list_icon_11', item.settleCount>0 && 'list_icon_111']"></span>
                    <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                    <span :key="'icon_22' + index" @click="item.status == 1 && confirmMatch_settle_event(item, 4)"
                        :title="i18n_t('settlement_v2.v_6')" :class="['list_icon', item.status != 1 && 'list_icon_d', [2,3].includes(item.status) && 'list_icon_22']"></span>
                    <span :key="'icon_33' + index" @click="settle_match_event(item, 4)" :title="i18n_t('settlement_v2.v_7')"  :class="['list_icon', [3].includes(item.status) && 'list_icon_33']"></span>
                    <span :key="'icon_44' + index" @click="re_stage_match_event(item, 4)" :title="i18n_t('settlement_v2.v_8')" class="list_icon"></span>
                    <span :key="'icon_55' + index" @click="roll_back_settle_match_event(item, 4)" :title="i18n_t('settlement_v2.v_9')" class="list_icon"></span>
                    <span :key="'icon_66' + index" @click="get_unsettle_order_num(item)" :title="i18n_t('settlement_v2.v_10')" class="list_icon"></span>
                    <!-- <span :key="'icon_77' + index" :title="1 == 1 ? i18n_t('settlement_v2.v_13') : i18n_t('settlement_v2.v_14')" :class="[1 == 1 ? 'list_icon' : 'list_icon_dong']"></span> -->
                    <span :key="'icon_0118' + index"
                        @click="set_freeze_settle(1, {...item, type: 2, is_cmp: true})"
                        v-if="item.scoresPeriodFreeze == 1 || row.freezeStatus == 1" :title="i18n_t('settlement_v2.v_14')" class="list_icon_dong"></span>
                    <span :key="'icon_77' + index"  @click="set_freeze_settle(1, {...item, type: 2, is_cmp: true})" v-else :title="i18n_t('settlement_v2.v_13')" class="list_icon"></span>
                </template>
                <template  v-else>
                    <span :key="'icon_88' + index" @click="edit_match_settle_event(item, index, 2, 'goal')" :title="i18n_t('settlement_v2.v_11')" class="list_icon list_icon_2"></span>
                    <span :key="'icon_99' + index"  @click="set_close_is_show(item)" :title="i18n_t('settlement_v2.v_12')" class="list_icon list_icon_2"></span>
                </template>
            </div>
            <div class="list_li_box col_1">{{item.outstanding}}</div>
        </li>
        <li :class="['list_li']">
            <iIcon type="ios-arrow-forward" size="16"/>
        </li>
      </ul>

      <!--角球类-->
      <div v-if="settle_params.categorySetId == 10002">
          <!--第一个Corner表格-->
        <ul class="custom_table">
            <li :class="['list_li','list_li_title']">
                <!-- 角球 -->
                <div class="list_li_box col_3" style="justify-content: left;">{{i18n_t('settlement_v2.v_64')}}</div>
                <!-- 主 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_56')}}</div>
                <!-- 客 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_57')}}</div>
                <!-- 比分 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_65')}}</div>
                <!-- 操作 -->
                <div class="list_li_box col_3">{{i18n_t('settlement_v2.v_58')}}</div>
                <!-- 未结算 -->
                <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_59')}}</div>
                <!-- 三方事件比分 -->
                <template v-if="showbehindEvent">
                    <div class="list_li_box col_1" v-for="(item,index) in behindListEvent" :key="index">{{item}}</div>
                </template>
            </li>
            <li :class="['list_li',['201','202','203','206','207'].includes(item.settleNum)&&'list_li_border_bottom', item.needCheck==1&&'li_yellow' ]"
                v-for="(item, index) in list_table_event_data" :key="'sange' + item.id + index">
                <div class="list_li_box col_3" style="justify-content: left;">
                    <!-- (item.stage + ' ' + item.stage_key) -->
                    {{['201','202','203','206','207','208'].includes(item.settleNum) ? (item.stage) : (item.stage + ' ' + item.stage_key)}}
                    &nbsp;&nbsp;&nbsp;
                    <iIcon v-show="item.is_show_icon"  :style="{cursor: is_click_stop ? 'pointer' : 'no-drop'}" @click="add_match_settle_event(item, 'corner')" type="md-add-circle" size="18" />
                </div>
                <!-- ['201','202','203','206','207','208'] 这几个阶段展示比分  否则展示homeAway字段对应赢的队伍 get_corner_num(item, type) item.homeAway == 'none' ? 'NC' : item.homeAway == 'home' ? '1' : ''-->
                <div class="list_li_box col_1">{{['201','202','203','206','207','208'].includes(item.settleNum) ? (item.t1) : get_corner_num(item, 1)}}</div>
                <div class="list_li_box col_1">{{['201','202','203','206','207','208'].includes(item.settleNum) ? (item.t2) : get_corner_num(item, 2)}}</div>
                <div class="list_li_box col_1">{{item.t1}} - {{item.t2}}</div>
                <!--   list_icon_box   icon控制的主样式容器   -->
                <div class="list_li_box col_3 list_icon_box">
                    <template>
                        <!-- ['201','202','203','206','207','208'] 这几个阶段点击调用的是比分接口 其它阶段点击调用的是事件接口 -->
                        <span :key="'icon_11' + index" @click="set_score_goals(item, index)" :title="i18n_t('settlement_v2.v_5')"
                            :class="['list_icon', [1,2,3].includes(item.status) && 'list_icon_11', item.settleCount>0 && 'list_icon_111']"></span>
                        <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                        <!-- ['201','202','203','206','207','208'] 这几个阶段点击调用的是比分接口 其它阶段点击调用的是事件接口 -->
                        <span
													:key="'icon_22' + index"
													@click="item.status == 1 && (['201','202','203','206','207','208'].includes(item.settleNum) ? confirmation_score(item, 4) : confirmMatch_settle_event(item, 4))"
													:title="i18n_t('settlement_v2.v_6')" :class="['list_icon', item.status != 1 && 'list_icon_d', [2,3].includes(item.status) && 'list_icon_22']"
                        >
                        </span>
                        <!-- ['201','202','203','206','207','208'] 这几个阶段点击调用的是比分接口 其它阶段点击调用的是事件接口 -->
                        <span
													:key="'icon_33' + index"
													@click="['201','202','203','206','207','208'].includes(item.settleNum) ? settlement_score(item, 4) : settle_match_event(item, 4)"
													:title="i18n_t('settlement_v2.v_7')"   :class="['list_icon', [3].includes(item.status) && 'list_icon_33']"
												>
												</span>
                        <!-- ['201','202','203','206','207','208'] 这几个阶段点击调用的是比分接口 其它阶段点击调用的是事件接口 -->
                        <span
													:key="'icon_44' + index"
													@click="['201','202','203','206','207','208'].includes(item.settleNum) ? re_stage_score_settlement(item) : re_stage_match_event(item, 4)"
													:title="i18n_t('settlement_v2.v_8')" class="list_icon"
												>
												</span>
                        <!-- ['201','202','203','206','207','208'] 这几个阶段点击调用的是比分接口 其它阶段点击调用的是事件接口 -->
                        <span
													:key="'icon_55' + index"
													@click="['201','202','203','206','207','208'].includes(item.settleNum) ? rollback_stage_score_settlement(item) : roll_back_settle_match_event(item, 4)"
													:title="i18n_t('settlement_v2.v_9')" class="list_icon"
												>
												</span>
                        <span :key="'icon_66' + index" @click="get_unsettle_order_num(item,2)" :title="i18n_t('settlement_v2.v_10')" class="list_icon"></span>
                        <!-- type: ['201','202','203','206','207','208'].includes(item.settleNum) ? 1 : 2    typ == 1 代表是比分的，  == 2 代表的是事件的-->
                        <span
													:key="'icon_0118' + index"
													@click="set_freeze_settle(1, {...item, type: ['201','202','203','206','207','208'].includes(item.settleNum) ? 4 : 2, is_cmp: true})"
													v-if="item.scoresPeriodFreeze == 1 || row.freezeStatus == 1" :title="i18n_t('settlement_v2.v_14')" class="list_icon_dong"
												>
												</span>
                        <span :key="'icon_77' + index"  @click="set_freeze_settle(1, {...item, type:  ['201','202','203','206','207','208'].includes(item.settleNum) ? 4 : 2, is_cmp: true})" v-else
                            :title="i18n_t('settlement_v2.v_13')" class="list_icon"></span>
                    </template>
                </div>
                <div class="list_li_box col_2">

                    <!-- 已结算的注单数 / 总注单数  -->
                    {{item.settleBetTotal || '0'}}/{{item.betTotal || '0'}}
                    <!-- 已结算的玩法数 / 总玩法数 -->
                    {{item.settlePlayTotal || '0'}}/{{item.playTotal || '0'}}

                </div>
                <template v-if="showbehindEvent">
                    <div  v-for="(item1,index1) in behindListEvent" :key="index1" :class="['list_li_box col_1',item[item1+'showRed1']&&'list_li_diff']">
                        {{item[item1+'homeAway']}}
                        {{item[item1+'playerNameCode']}}
                        {{item[item1+'extryInfo']}}
                    </div>
                </template>
            </li>
            <li :class="['list_li']">
                <iIcon type="ios-arrow-forward" size="16" @click="show_behindList('event')" :class="showbehindEvent?'opposite':''"/>
            </li>
        </ul>
        <div></div>
        <!--第二个Corner表格-->
        <ul class="custom_table">
            <li :class="['list_li','list_li_title']">
                <!-- 角球 -->
                <div class="list_li_box col_2" style="justify-content: left;">{{i18n_t('settlement_v2.v_64')}}</div>
                <!-- 主 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_56')}}</div>
                <!-- 客 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_57')}}</div>
                <!-- 操作 -->
                <div class="list_li_box col_3">{{i18n_t('settlement_v2.v_58')}}</div>
                <!-- 未结算 -->
                <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_59')}}</div>
                <template  v-if="showbehindScore">
                    <div class="list_li_box col_1" v-for="(item,index) in behindListScore" :key="index">{{item}}</div>
                </template>
            </li>
            <li :class="['list_li',[2013,2016,2019].includes(item.settleNum)&&'list_li_border_bottom', item.needCheck==1&&'li_yellow' ]"
                v-for="(item, index) in list_table_data" :key="'yige' + item.id + index">
                <div class="list_li_box list_li_box_left col_2">{{ get_name(item.settleNum) }}</div>
                <template>
                    <div style="display:flex;">
                        <div class="list_li_box col_1">
                            <template v-if="item.is_show">
                                <div>
                                    {{item.t1}}
                                </div>
                            </template>
                            <template v-else>
                                <i-input
                                    v-model="item.t11"
                                    style="width:70px;height:28px;padding: 4px 5px">
                                </i-input>
                            </template>
                        </div>
                        <div class="list_li_box col_1">
                            <template v-if="item.is_show">
                                <div>
                                    {{item.t2}}
                                </div>
                            </template>
                            <template v-else>
                                <i-input
                                    v-model="item.t22"
                                    style="width:70px;height:28px;padding: 4px 5px;">
                                </i-input>
                            </template>
                        </div>
                    </div>
                </template>
                <!--   list_icon_box   icon控制的主样式容器   -->
                <div class="list_li_box col_3 list_icon_box">
                    <template v-if="item.is_show">
                        <span :key="'icon_1' + index" @click="set_event_score(item)"
                            :title="i18n_t('settlement_v2.v_5')"  :class="['list_icon', [1,2,3].includes(item.status) && 'list_icon_11', item.settleCount>0 && 'list_icon_111']"></span>
                        <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                        <span :key="'icon_2' + index" @click="item.status == 1 && confirmation_score(item, 4)" :title="i18n_t('settlement_v2.v_6')"
                            :class="['list_icon', item.status != 1 && 'list_icon_d', [2,3].includes(item.status) && 'list_icon_22']"></span>
                        <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                        <span :key="'icon_3' + index" @click="settlement_score(item, 4)"
                            :title="i18n_t('settlement_v2.v_7')"  :class="['list_icon', [3].includes(item.status) && 'list_icon_33']"></span>
                        <span :key="'icon_4' + index" @click="re_stage_score_settlement(item)" :title="i18n_t('settlement_v2.v_8')" class="list_icon"></span>
                        <span :key="'icon_5' + index" @click="rollback_stage_score_settlement(item)" :title="i18n_t('settlement_v2.v_9')" class="list_icon"></span>
                        <span :key="'icon_6' + index" @click="get_unsettle_order_num(item,2)" :title="i18n_t('settlement_v2.v_10')" class="list_icon"></span>
                        <!-- <span :key="'icon_7' + index" :title="1 == 2 ? i18n_t('settlement_v2.v_13') : i18n_t('settlement_v2.v_14')" :class="[1 == 2 ? 'list_icon' : 'list_icon_dong']"></span> -->
                        <!-- 取消冻结 -->
                        <span :key="'icon_10118' + index" @click="set_freeze_settle(1, {...item, type: 1, is_cmp: true})"
                            v-if="item.scoresPeriodFreeze == 1 || row.freezeStatus == 1" :title="i18n_t('settlement_v2.v_14')" class="list_icon_dong"></span>
                        <!-- 结算冻结 -->
                        <span :key="'icon_7' + index" @click="set_freeze_settle(1, {...item, type: 1, is_cmp: true})" v-else :title="i18n_t('settlement_v2.v_13')" class="list_icon"></span>
                    </template>
                    <template v-else>
                        <span :key="'icon_8' + index" @click="set_score_num(item)" :title="i18n_t('settlement_v2.v_11')" class="list_icon list_icon_2"></span>
                        <span :key="'icon_9' + index" @click="set_close_is_show(item)" :title="i18n_t('settlement_v2.v_12')" class="list_icon list_icon_2"></span>
                    </template>
                </div>
                <div class="list_li_box col_2">

                    <!-- 已结算的注单数 / 总注单数  -->
                    {{item.settleBetTotal || '0'}}/{{item.betTotal || '0'}}
                    <!-- 已结算的玩法数 / 总玩法数 -->
                    {{item.settlePlayTotal || '0'}}/{{item.playTotal || '0'}}

                </div>
                <template  v-if="showbehindScore">
                    <div  v-for="(item1,index1) in behindListScore" :key="index1" :class="['list_li_box col_1',item[item1+'showRed']&&'list_li_diff']">
                        {{item[item1+'t1']}}
                        <span v-if="item[item1+'t1']!=null">:</span>
                        {{item[item1+'t2']}}
                    </div>
                </template>
            </li>
            <li :class="['list_li']">
                <iIcon type="ios-arrow-forward" size="16"  @click="show_behindList('score')" :class="showbehindScore?'opposite':''"/>
            </li>
        </ul>
      </div>
      <!--罚牌类-->
      <div v-if="settle_params.categorySetId == 10005">
        <!--第一个Goal表格-->
        <ul class="custom_table">
            <li :class="['list_li','list_li_title']">
                <!-- 罚牌 -->
                <div class="list_li_box col_2" style="justify-content: left;">{{i18n_t('settlement_v2.v_67')}}</div>
                <!-- 主 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_56')}}</div>
                <!-- 客 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_57')}}</div>
                <!-- 操作 -->
                <div class="list_li_box col_3">{{i18n_t('settlement_v2.v_58')}}</div>
                <!-- 未结算 -->
                <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_59')}}</div>
                <div class="list_li_box col_1" v-for="(item,index) in behindListScore" :key="index" v-show="showbehindScore">{{item}}</div>
            </li>
            <li :class="['list_li',[304,308,309,3013,3017].includes(item.settleNum)&&'list_li_border_bottom', item.needCheck==1&&'li_yellow' ]"
                v-for="(item, index) in list_table_data" :key="'yige' + item.id + index">
                <div class="list_li_box list_li_box_left col_2">{{ get_name(item.settleNum) }}</div>
                <template v-if="item.settleNum == 1021">
                    <div style="display:flex;">
                        <div class="list_li_box col_2">
                            <template v-if="item.is_show">
                                <div>
                                    {{the_winning_way(item.extryInfo)}}
                                </div>
                            </template>
                            <template v-else>
                                <iSelect class="bg-panda-field-grey mr5x" v-model="item.extryInfo_a" :placeholder="i18n_t('settlement_v2.v_20')" size="small" style="width:140px">
                                    <iOption v-for="val in list_select" :value="val.value"  :key="val.value">{{val.label}}</iOption>
                                </iSelect>
                            </template>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div style="display:flex;">
                        <div class="list_li_box col_1 use-ivew">
                            <template v-if="item.is_show">
                                <div>
                                    {{item.t1}}
                                </div>
                            </template>
                            <template v-else>
                                <i-input
                                    v-model="item.t11"
                                    style="width:70px;height:28px;padding: 4px 5px">
                                </i-input>
                            </template>
                        </div>
                        <div class="list_li_box col_1 use-ivew">
                            <template v-if="item.is_show">
                                <div>
                                    {{item.t2}}
                                </div>
                            </template>
                            <template v-else>
                                <i-input
                                    v-model="item.t22"
                                    style="width:70px;height:28px;padding: 4px 5px;">
                                </i-input>
                            </template>
                        </div>
                    </div>
                </template>
                <!--   list_icon_box   icon控制的主样式容器   -->
                <div class="list_li_box col_3 list_icon_box">
                    <template v-if="item.is_show">
                        <span :key="'icon_1' + index" @click="set_event_score(item)" :title="i18n_t('settlement_v2.v_5')"
                            :class="['list_icon', [1,2,3].includes(item.status) && 'list_icon_11', item.settleCount>0 && 'list_icon_111']"></span>
                        <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                        <span :key="'icon_2' + index" @click="item.status == 1 && confirmation_score(item, 4)" :title="i18n_t('settlement_v2.v_6')"
                            :class="['list_icon', item.status != 1 && 'list_icon_d', [2,3].includes(item.status) && 'list_icon_22']"></span>
                        <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                        <span :key="'icon_3' + index" @click="settlement_score(item, 4)" :title="i18n_t('settlement_v2.v_7')"  :class="['list_icon', [3].includes(item.status) && 'list_icon_33']"></span>
                        <span :key="'icon_4' + index" @click="re_stage_score_settlement(item)" :title="i18n_t('settlement_v2.v_8')" class="list_icon"></span>
                        <span :key="'icon_5' + index" @click="rollback_stage_score_settlement(item)" :title="i18n_t('settlement_v2.v_9')" class="list_icon"></span>
                        <span :key="'icon_6' + index" @click="get_unsettle_order_num(item)" :title="i18n_t('settlement_v2.v_10')" class="list_icon"></span>
                        <!-- <span :key="'icon_7' + index" :title="1 == 2 ? i18n_t('settlement_v2.v_13') : i18n_t('settlement_v2.v_14')" :class="[1 == 2 ? 'list_icon' : 'list_icon_dong']"></span> -->
                        <!-- 取消冻结 -->
                        <span :key="'icon_10118' + index" @click="set_freeze_settle(1, {...item, type: 1, is_cmp: true})"
                            v-if="item.scoresPeriodFreeze == 1 || row.freezeStatus == 1" :title="i18n_t('settlement_v2.v_14')" class="list_icon_dong"></span>
                        <!-- 结算冻结 -->
                        <span :key="'icon_7' + index" @click="set_freeze_settle(1, {...item, type: 1, is_cmp: true})" v-else :title="i18n_t('settlement_v2.v_13')" class="list_icon"></span>
                    </template>
                    <template v-else>
                        <span :key="'icon_8' + index" @click="set_score_num(item)" :title="i18n_t('settlement_v2.v_11')" class="list_icon list_icon_2"></span>
                        <span :key="'icon_9' + index"  @click="set_close_is_show(item)" :title="i18n_t('settlement_v2.v_12')" class="list_icon list_icon_2"></span>
                    </template>
                </div>
                <div class="list_li_box col_2">
                    <!-- 已结算的注单数 / 总注单数  -->
                    {{item.settleBetTotal || '0'}}/{{item.betTotal || '0'}}
                    <!-- 已结算的玩法数 / 总玩法数 -->
                    {{item.settlePlayTotal || '0'}}/{{item.playTotal || '0'}}
                </div>
                 <div v-show="showbehindScore"  v-for="(item1,index1) in behindListScore" :key="index1" :class="['list_li_box col_1',item[item1+'showRed']&&'list_li_diff']">
                    {{item[item1+'t1']}}
                    <span v-if="item[item1+'t1']!=null">:</span>
                    {{item[item1+'t2']}}
                </div>
            </li>
            <li :class="['list_li']">
                <iIcon type="ios-arrow-forward" size="16"  @click="show_behindList('score')" :class="showbehindScore?'opposite':''"/>
            </li>
        </ul>
        <div></div>
        <!--第二个 对应赛制 表格-->
        <ul class="custom_table">
            <li :class="['list_li','list_li_title']">
                <!-- 罚牌顺序 -->
                <div class="list_li_box col_2" style="justify-content: left;">{{i18n_t('settlement_v2.v_68')}}</div>
                <!-- 阶段 -->
                <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_63')}}</div>
                <!-- 罚牌 -->
                <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_69')}}</div>
                <!-- 黄牌 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_70')}}</div>
                <!-- 红牌 -->
                <div class="list_li_box col_1">{{i18n_t('settlement_v2.v_71')}}</div>
                <!-- 操作 -->
                <div class="list_li_box col_3">{{i18n_t('settlement_v2.v_58')}}</div>
                <!-- 未结算 -->
                <div class="list_li_box col_2">{{i18n_t('settlement_v2.v_59')}}</div>
                <!-- 三方事件比分 -->
                <div class="list_li_box col_1" v-for="(item,index) in behindListEvent" :key="index" v-show="showbehindEvent">{{item}}</div>
            </li>
            <li
                :class="['list_li', (list_table_event_data[index + 1] && item.settleNum !== list_table_event_data[index + 1].settleNum)&&'list_li_border_bottom', item.needCheck==1&&'li_yellow' ]"
                v-for="(item, index) in list_table_event_data" :key="'sange' + item.id + index">
                <div class="list_li_box col_2" style="justify-content: left;">
                    {{item.stage + ' ' + item.stage_key}}
                    &nbsp;&nbsp;&nbsp;
                    <iIcon v-show="item.is_show_icon" style="cursor: pointer;" @click="add_match_settle_event(item, 'fa_card')" type="md-add-circle" size="18" />
                </div>
                <div class="list_li_box col_2">{{item.stage}}</div>
                <div class="list_li_box col_2">
                    <template v-if="item.is_show">
                        <div>
                            {{((item.t1 || item.t1 == '0') || (item.t2 || item.t2 == '0')) ? get_card_name(item) : ''}}
                        </div>
                    </template>
                    <template v-else>
                        <iSelect class="bg-panda-field-grey mr5x" v-model="item.homeAway_a" size="small"  :placeholder="i18n_t('settlement_v2.v_20')" style="width:85px">
                            <iOption v-for="item in list_select_booking_home_away" :key="item.value" :value="item.value">{{item.label}}</iOption>
                        </iSelect>
                    </template>
                </div>
                <div class="list_li_box col_1">{{item.firstT1}} - {{item.firstT2}}</div>
                <div class="list_li_box col_1">{{item.secondT1}} - {{item.secondT2}}</div>
                <!--   list_icon_box   icon控制的主样式容器   -->
                <div class="list_li_box col_3 list_icon_box">
                    <template v-if="item.is_show">
                        <span :key="'icon_11' + index" @click="set_event_score(item)"
                            :title="i18n_t('settlement_v2.v_5')"  :class="['list_icon', [1,2,3].includes(item.status) && 'list_icon_11', item.settleCount>0 && 'list_icon_111']"></span>
                        <!-- status   未编辑 0 确认比分 1 已确认比分 2 已结算 3 -->
                        <span :key="'icon_22' + index"
                            @click="item.status == 1 && confirmMatch_settle_event(item, 4)" :title="i18n_t('settlement_v2.v_6')"
                            :class="['list_icon', item.status != 1 && 'list_icon_d', [2,3].includes(item.status) && 'list_icon_22']"></span>
                        <span :key="'icon_33' + index"
                             @click="settle_match_event(item, 4)" :title="i18n_t('settlement_v2.v_7')"  :class="['list_icon', [3].includes(item.status) && 'list_icon_33']"></span>
                        <span :key="'icon_44' + index"
                            @click="re_stage_match_event(item, 4)" :title="i18n_t('settlement_v2.v_8')" class="list_icon"></span>
                        <span :key="'icon_55' + index"
                            @click="roll_back_settle_match_event(item, 4)" :title="i18n_t('settlement_v2.v_9')" class="list_icon"></span>
                        <span :key="'icon_66' + index"
                            @click="get_unsettle_order_num(item)" :title="i18n_t('settlement_v2.v_10')" class="list_icon"></span>
                        <span :key="'icon_0118' + index"
                            @click="set_freeze_settle(1, {...item, type: 2})" v-if="item.scoresPeriodFreeze == 1 || row.freezeStatus == 1" :title="i18n_t('settlement_v2.v_14')" class="list_icon_dong"></span>
                        <span :key="'icon_77' + index"
                            @click="set_freeze_settle(1, {...item, type: 2})" v-else :title="i18n_t('settlement_v2.v_13')" class="list_icon"></span>
                    </template>
                    <template  v-else>
                        <span :key="'icon_88' + index" @click="edit_match_settle_event(item, index, 2, 'fa_card')" :title="i18n_t('settlement_v2.v_11')" class="list_icon list_icon_2"></span>
                        <span :key="'icon_99' + index"  @click="set_close_is_show(item)" :title="i18n_t('settlement_v2.v_12')" class="list_icon list_icon_2"></span>
                    </template>
                </div>
                <div class="list_li_box col_2">

                    <!-- 已结算的注单数 / 总注单数  -->
                    {{item.settleBetTotal || '0'}}/{{item.betTotal || '0'}}
                    <!-- 已结算的玩法数 / 总玩法数 -->
                    {{item.settlePlayTotal || '0'}}/{{item.playTotal || '0'}}

                </div>
                 <div v-show="showbehindEvent"  v-for="(item1,index1) in behindListEvent" :key="index1" :class="['list_li_box col_1',item[item1+'showRed1']&&'list_li_diff']">
                    {{item[item1+'homeAway']}}
                    {{item[item1+'playerNameCode']}}
                    {{item[item1+'extryInfo']}}
                 </div>
            </li>
            <li :class="['list_li']">
                <iIcon type="ios-arrow-forward" size="16" @click="show_behindList('event')" :class="showbehindEvent?'opposite':''"/>
            </li>
        </ul>
      </div>

      <ul class="custom_table" v-if="settle_params.categorySetId == 10003 || settle_params.categorySetId == 10001"></ul>
      <!-- 点球大战弹窗 -->
      <q-dialog v-model="dianshow" :content-style="{zIndex:1000}" persistent>
        <div class="cpm_box" v-if="dianshow">
            <div class="event_dialog use-ivew" >
                <div>
                    <p class="title">
                        <iIcon type="ios-close" @click="set_close" size="35"/>
                    </p>
                    <div class="textdiv">
                        <div class="shua">
                            <!-- 未结算 -->
                            {{i18n_t('settlement_v2.v_59')}}
                            <span class="icon_shua"  @click="get_unsettle_order_num(list_select_cmp_data.data, 2)"></span>
                            <i-input
                                :value="list_select_cmp_data.outstanding" readonly
                                style="width:70px;height:28px;padding: 4px 5px">
                            </i-input>
                        </div>
                        <!-- 点球大战 选择进去 -->
                        <div class="xuan_box" v-if="list_select_cmp_data.type == '1'">
                            <!-- R7 - 14th Penalty: -->
                            <span>{{list_select_cmp_data.data.firstNum}}&nbsp;{{i18n_t('settlement_v2.v_165')}}{{list_select_cmp_data.data.eventOrder}}&nbsp;{{i18n_t('settlement_v2.v_166')}}</span>
                            &nbsp;<iSelect class="bg-panda-field-grey mr5x" v-model="list_select_cmp_a" size="small" placeholder="-" style="width:130px">
                                <iOption v-for="item in list_select_cmp" :key="item.value" :value="item.value">{{item.label}}</iOption>
                            </iSelect>
                        </div>
                        <!-- 点球大战 修改1-5和全程比分 -->
                        <div class="xuan_box" v-else>
                            <!-- Total 1 - 5 Penalty: -->
                            {{list_select_cmp_data.data.is_show_text == '5' ? i18n_t('settlement_v2.v_98') : i18n_t('settlement_v2.v_97')}}
                            <i-input
                                v-model="list_select_cmp_data.data.t1"
                                style="width:50px;height:28px;padding: 4px 5px">
                            </i-input>
                            -
                            <i-input
                                v-model="list_select_cmp_data.data.t2"
                                style="width:50px;height:28px;padding: 4px 5px">
                            </i-input>
                        </div>
                    </div>
                    <!-- {{list_select_cmp_data}} -->
                    <!-- icon按钮 -->
                    <p class="btnbox list_icon_box">
                        <span key="icon_0211" :title="i18n_t('settlement_v2.v_5')"
                            @click="set_penalty_scores(list_select_cmp_data, 'popup')" :class="['list_icon', [1,2,3].includes(list_select_cmp_data.data.status) && 'list_icon_11']"></span>
                        <span key="icon_0212" :title="i18n_t('settlement_v2.v_6')"
                            @click="confirmMatch_settle_event(list_select_cmp_data.data, 4, 'popup')"  :class="['list_icon', [2,3].includes(list_select_cmp_data.data.status) && 'list_icon_22']"></span>
                        <span key="icon_0213" :title="i18n_t('settlement_v2.v_7')"
                            @click="settle_match_event(list_select_cmp_data.data, 4, 'popup')"  :class="['list_icon', [3].includes(list_select_cmp_data.data.status) && 'list_icon_33']"></span>
                        <span key="icon_0214" :title="i18n_t('settlement_v2.v_8')"
                            @click="re_stage_match_event(list_select_cmp_data.data, 4)" class="list_icon"></span>
                        <span key="icon_0215" :title="i18n_t('settlement_v2.v_9')"
                            @click="roll_back_settle_match_event(list_select_cmp_data.data, 2, 'popup')" class="list_icon"></span>
                        <span key="icon_0216" :title=" i18n_t('settlement_v2.v_14')"
                        v-if="list_select_cmp_data.data && list_select_cmp_data.data.scoresPeriodFreeze == 1 || row.freezeStatus == 1"
                        @click="set_freeze_settle(1, {...list_select_cmp_data.data, type: 3, is_cmp: true})" :class="['list_icon_dong']" ></span>
                        <span key="icon_0217" :title="i18n_t('settlement_v2.v_13')" v-else @click="set_freeze_settle(1, {...list_select_cmp_data.data, type: 3, is_cmp: true})" :class="['list_icon_jie']"></span>
                    </p>
                </div>
            </div>
        </div>
      </q-dialog>
       <!-- 修改角球弹窗 -->
        <q-dialog v-model="score_goals.show_score_goals" :content-style="{zIndex:1000}" persistent v-if="settle_params.categorySetId == 10002">
            <div class="event_dialog use-ivew" v-if="score_goals.show_score_goals">
                <div>
                    <p class="title">
                        <iIcon type="ios-close" @click="close_score_goals" size="35"/>
                    </p>
                    <div class="textdiv" style="padding-top: 35px;">
                        <!-- 进球球员 -->
                        <div class="xuan_box">
                            <span>{{['201','202','203','206','207','208'].includes(score_goals.settleNum) ? (score_goals.stage) : (score_goals.stage + ' ' + score_goals.stage_key)}}:</span>
                            <i-input
                                v-model="score_goals.t11"
                                :disabled="score_goals.single"
                                style="width:50px;height:28px;padding: 4px 5px">
                            </i-input>
                            -
                            <i-input
                                v-model="score_goals.t22"
                                :disabled="score_goals.single"
                                style="width:50px;height:28px;padding: 4px 5px">
                            </i-input>
                        </div>
                        <!-- 进球方式 -->
                        <div class="xuan_box" style="margin-top: 15px;" v-if="score_goals.is_show_checkbox">
                            <iCheckbox v-model="score_goals.single">&nbsp;&nbsp;{{i18n_t('settlement_v2.v_141')}}</iCheckbox>
                        </div>
                    </div>
                    <!-- btn 按钮 -->
                    <p class="btnbox list_icon_box">
                        <q-btn :label="i18n_t('settlement_v2.v_49')" no-caps  @click="close_score_goals" class="panda-btn-dark-dense cbtn"/><!-- 关闭 -->
                        <q-btn :label="i18n_t('settlement_v2.v_48')" no-caps @click="set_submit" color="secondary" class="panda-btn-light-dense sbtn" ></q-btn><!-- 保存 -->
                    </p>
                </div>
            </div>
        </q-dialog>
      <!-- 进球球员及进球方式 -->

        <!-- 进球球员及进球方式 -->
      <div class="cpm_box" v-if="score_goals.show_score_goals && ( settle_params.categorySetId == 10003 || settle_params.categorySetId == 10001)">
        <div class="event_dialog use-ivew" >
            <div>
                <p class="title">
                    <iIcon type="ios-close" @click="close_score_goals(1)" size="35"/>
                </p>
                <div class="textdiv" style="padding-top: 35px;">
                    <!-- 进球球员 -->
                    <div class="xuan_box">
                        <span>{{i18n_t('settlement_v2.v_156')}}:</span>
                        <iSelect class="bg-panda-field-grey mr5x" v-model="score_goals.extryEvent.playerNameCode_a" size="small" :placeholder="i18n_t('settlement_v2.v_20')" style="width:130px">
                            <!-- 这里（ + ''）是需要把数字转换为字符串，不然选项框不会选中对应的 -->
                            <iOption v-for="(item, index) in list_select_player" :key="index" :value="item.nameCode + ''">{{$language_value(item.names)}}</iOption>
                        </iSelect>
                    </div>
                    <!-- 进球方式 -->
                    <div class="xuan_box" style="margin-top: 15px;">
                        <!-- Method -->
                        <span>{{i18n_t('settlement_v2.v_96')}}:</span>
                        <iSelect class="bg-panda-field-grey mr5x" v-model="score_goals.extryEvent.extryInfo_a" size="small" :placeholder="i18n_t('settlement_v2.v_20')" style="width:130px">
                            <!-- 这里（ + ''）是需要把数字转换为字符串，不然选项框不会选中对应的 -->
                            <iOption v-for="(item, index) in list_score_goals" :key="index" :value="item.code + ''">{{i18 == 'zs' ? item.zhValue : item.enValue}}</iOption>
                        </iSelect>
                    </div>
                </div>
                <!-- btn 按钮 -->
                <p class="btnbox list_icon_box">
                    <q-btn :label="i18n_t('settlement_v2.v_49')" no-caps  @click="close_score_goals(1)" class="panda-btn-dark-dense cbtn"/><!-- 关闭 -->
                    <q-btn :label="i18n_t('settlement_v2.v_48')" no-caps
                        @click="set_goal_method_and_player(score_goals, 1, 'popup')" color="secondary" class="panda-btn-light-dense sbtn" ></q-btn><!-- 保存 -->
                </p>
            </div>
        </div>
      </div>
       <!-- 进球球员及进球方式 -->
        <div class="cpm_box"  v-if="score_goals.show_score_goals && settle_params.categorySetId == 10005">
            <div class="event_dialog use-ivew">
                <div>
                    <p class="title">
                        <iIcon type="ios-close" @click="close_score_goals(1)" size="35"/>
                    </p>
                    <div class="textdiv" style="padding-top: 35px;">
                        <!-- 被罚球员 -->
                        <div class="xuan_box">
                            <span>{{i18n_t('settlement_v2.v_147')}}:</span>
                            <iSelect class="bg-panda-field-grey mr5x" v-model="score_goals.extryEvent.playerNameCode_a" size="small" :placeholder="i18n_t('settlement_v2.v_20')" style="width:130px">
                                <iOption v-for="(item, index) in list_select_player" :key="index" :value="item.nameCode">{{$language_value(item.names)}}</iOption>
                            </iSelect>
                        </div>
                    </div>
                    <!-- btn 按钮 -->
                    <p class="btnbox list_icon_box">
                        <q-btn :label="i18n_t('settlement_v2.v_49')" no-caps  @click="close_score_goals(1)" class="panda-btn-dark-dense cbtn"/><!-- 关闭 -->
                        <q-btn :label="i18n_t('settlement_v2.v_48')" no-caps @click="set_goal_method_and_player(score_goals, 2)" color="secondary" class="panda-btn-light-dense sbtn" ></q-btn><!-- 保存 -->
                    </p>
                </div>
            </div>
        </div>

      <!-- 冻结弹窗 -->
      <q-dialog v-model="freeze_settle_data.show_cmp" :content-style="{zIndex:1000}" persistent>
        <div class="event_dialog use-ivew flex items-center justify-between" v-if="freeze_settle_data.show_cmp">
            <div>
                <p class="title">
                    <iIcon type="ios-close" @click="set_freeze_settle(2)" size="35"/>
                </p>
                <div class="textdiv" style="padding-top: 50px;">
                    <!-- 提示内容 -->
                    <div class="xuan_box">
                        {{freeze_settle_data.str_text}}
                    </div>
                </div>
                <!-- btn 按钮 -->
                <p class="btnbox list_icon_box" style="text-align: center;">
                    <q-btn :label="i18n_t('settlement_v2.v_12')" no-caps  @click="set_freeze_settle(2)" class="panda-btn-dark-dense cbtn"/><!-- 关闭 -->&nbsp;&nbsp;&nbsp;&nbsp;
                    <q-btn :label="i18n_t('settlement_v2.v_11')" no-caps @click="freeze_settle(freeze_settle_data)" color="secondary" class="panda-btn-light-dense sbtn" ></q-btn><!-- 确定 -->
                </p>
            </div>
        </div>
      </q-dialog>
    </div>
  </div>
</template>

<script>
import { api_event } from "src/api/index.js"
import { SessionStorage } from "quasar";
import panel_goal from "app/project/caopan/pages/settlement_manage/settlement_v2/components/panel_goal/panel_goal.vue";
import settlement_v2 from "app/project/caopan/pages/settlement_manage/settlement_v2/config/settlement_v2/settlement_v2.js";
export default {
  mixins: [settlement_v2],
  data() {
    return {
      dialog_data: {}, // 接口返回的数据
      geti18n: SessionStorage.getItem('i18n') || 'zs', // 当前语言
      matchBatchResult_params: {}, // 批量结算参数对象
      tabledata_loading: false,
      list_table_data: [],
      list_icon:[
          {id: 'list_icon_1', name:i18n_t('settlement_v2.v_5'),icon: require('/assets/settlement_v2_icon/edit.svg'),icon_a: require('/assets/settlement_v2_icon/edit_a.svg')}, // 修改
          {id: 'list_icon_2', name:i18n_t('settlement_v2.v_6'),icon: require('/assets/settlement_v2_icon/ok.svg'),icon_a: require('/assets/settlement_v2_icon/ok_a.svg')}, // 确认比分
          {id: 'list_icon_3', name:i18n_t('settlement_v2.v_7'),icon: require('/assets/settlement_v2_icon/settle.svg'),icon_a: require('/assets/settlement_v2_icon/settle_a.svg')}, // 结算
          {id: 'list_icon_4', name:i18n_t('settlement_v2.v_8'),icon: require('/assets/settlement_v2_icon/re_settle.svg'),icon_a: require('/assets/settlement_v2_icon/re_settle_a.svg')}, // 重新结算
          {id: 'list_icon_5', name:i18n_t('settlement_v2.v_9'),icon: require('/assets/settlement_v2_icon/rollback.svg'),icon_a: require('/assets/settlement_v2_icon/rollback_a.svg')}, // 回滚
          {id: 'list_icon_6', name:i18n_t('settlement_v2.v_10'),icon: require('/assets/settlement_v2_icon/refresh.svg'),icon_a: require('/assets/settlement_v2_icon/refresh_a.svg')}, // 搜索未结算
      ],
      list_select:[ // 获胜方式
          { value: 1, label:i18n_t('settlement_v2.v_157'),},
          { value: 2, label:i18n_t('settlement_v2.v_158'),},
          { value: 3, label:i18n_t('settlement_v2.v_159'),},
          { value: 4, label:i18n_t('settlement_v2.v_160'),},
          { value: 5, label:i18n_t('settlement_v2.v_161'),},
          { value: 6, label:i18n_t('settlement_v2.v_162'),},
      ],
      list_select_dian:[ // 释放点球大战
          { value: '1', label:i18n_t('settlement_v2.v_23'),}, // 是
          { value: '0', label:i18n_t('settlement_v2.v_24'),}, // 不是
      ],
      list_select_zou:[ // 没有进行加时赛 & 点球大战 (走水)   或  没有进行点球大战 (走水)
          { value: '1', label:i18n_t('settlement_v2.v_172'),}, // 走水
      ],
      list_select_cmp_a: '', //选中赛果选项数据
      list_select_cmp:[ // 赛果选项 弹窗
          { value: '1', label: i18n_t('settlement_v2.v_137'),},
          { value: '0', label: i18n_t('settlement_v2.v_138'),},
          { value: '-1', label: i18n_t('settlement_v2.v_139'),},
      ],
      list_select_home_away_a: '', //选中主客队选项数据
      list_select_home_away:[ // 主客队下拉框
          { value: 'home', label:i18n_t('settlement_v2.v_56'),},
          { value: 'away', label:i18n_t('settlement_v2.v_57'),},
          { value: 'no goal', label: i18n_t('settlement_v2.v_140'),},
      ],
      list_select_booking_home_away:[ // 主客队下拉框
        { value: '1', label: i18n_t('settlement_v2.v_143'),}, //主队黄牌
        { value: '2', label:i18n_t('settlement_v2.v_144'),}, //客队黄牌
        { value: '3', label:i18n_t('settlement_v2.v_145'),}, //主队红牌
        { value: '4', label:i18n_t('settlement_v2.v_146'),}, //客队红牌
        { value: '5', label:i18n_t('settlement_v2.v_142'),}, //没有罚牌
      ],
      // list_select_player_a: '', //选中球员选项数据
      list_select_player:[ // 球员下拉框
          {"id": 'NO', "nameCode": 'NO', names:{ zs: i18n_t('settlement_v2.v_41'), en: i18n_t('settlement_v2.v_41')}}, // 没有
          {"id": '000', "nameCode": '000', names: {zs: i18n_t('settlement_v2.v_20'), en: i18n_t('settlement_v2.v_20')}}, // 请选择
      ],
      // list_score_goals_a: '', //选中进球方式选项数据
      list_score_goals:[
          {code: '000', zhValue: '请选择', enValue:'Please select'},
          {code: '0', zhValue: '未知', enValue:'Unknown'},
          {code: '-1', zhValue: '没有', enValue:'None'},
          {code: '1', zhValue: '点球', enValue:'Penalty'},
          {code: '2', zhValue: '乌龙球', enValue:'Own Goal'},
          {code: '3', zhValue: '头球', enValue:'Header'},
          {code: '-100', zhValue: '射门', enValue:'Shot'},
          {code: '-200', zhValue: '任意球', enValue:'Free Kick'},
      ], // 进球方式
       event_code:{
        'fa_card':  i18n_t('settlement_v2.v_142'), // 没有罚牌
        'yellow_card':   i18n_t('settlement_v2.v_70'),// 黄牌
        'red_card':   i18n_t('settlement_v2.v_71'),// 红牌
      },
      list_select_cmp_data: { // 弹窗数据
          outstanding: '',
          type: '', // 1  是展示选项框  all 或 5 是展示input输入框
          data: null, // 对应数据
      },
      dianshow: false, // 点球大战对于弹窗
      score_goals: { // 进球方式弹窗数据
          show_score_goals: false, // 进球方式弹窗是否展示
      },
      score_num: '10',
      row: {},
      stop_timer: true, // 不轮询
      list_table_data: [], // 对应进球第一张表格数据
      list_table_data_copy: [], // 对应进球第一张表格数据备份
      list_table_event_data: [], // 下一个进球（next goal） / 角球(corner) / 下一个罚牌(Bookings) 对应数据
      list_table_event_data_copy: [], // 下一个进球（next goal） / 角球(corner) / 下一个罚牌(Bookings) 对应数据 数据备份
      penalty_kick_data:null, // 点球大战数据
      penalty_kick_data_title_num: 0, // 点球大战title的值
      second_dialog_visible:false,
      parobj:{}, // 传递参数
      userInfo: {}
    }
  },
  props: {
    settle_params: {
      type: Object,
      required: true
    }
  },
  components: {
    tableNoData:() => import("project/caopan/components/table/tableNoData.vue"), //无数据组件
    panelGoal: panel_goal
  },
  created(){
    userInfo.value = JSON.parse(sessionStorage.getItem('userInfo'));
    init_match_result();
    get_settle_centre_goal_player(settle_params.value.matchId);
  },
  methods: {
    //获取对应进球方式
    export const get_list_score_goals = (payload,code) => {
        if(!code && code != '0') return ''
        let data = list_score_goals.value.find(item => (item.code == code))
        return (i18.value == 'zs' ? data?.zhValue : data?.enValue)
    },
    // 获取对应展示主客队
    export const get_home_away_str = (payload,str) => {
        if(!str) return ''
        let name = list_select_home_away.value.find(item => item.value == str)
        return name?.label
    },
    //获取对应球员名
    export const get_player_name = (payload,code) => {
        if(code === ''){
            return  i18n_t('settlement_v2.v_41')
        }
        let data = list_select_player.value.find(item => (item.nameCode == code))

        return data ? language_value(data?.names) : '' //i18n_t('settlement_v2.v_41')
    },
     // 关闭下一个角球弹窗
    close_score_goals(){
        score_goals.value = {
             show_score_goals: false, // 下一个角球弹窗是否展示
             single: false, // 是否 No Corner
        }
    },
     // 提交对应比分及事件
    export const set_submit = (payload) => {
        // ['201','202','203','206','207','208'] 对应阶段是修改比分  这个是比分的
        let obj = score_goals.value
        if(['201','202','203','206','207','208'].includes(obj?.settleNum)){
          if(isNaN(obj.t11) || isNaN(obj.t22) || (!obj.t11 && obj.t11 != '0') || (!obj.t22 && obj.t22 != '0') || obj?.t11?.length > 2 || obj?.t22?.length > 2){
               Message.warning(i18n_t('settlement_v2.v_50'))
          }else{
              set_score_num(obj, 1)
          }
        }else { // 该阶段是修改事件   这个是事件的
          // 兼容输入框和后端值，且限制输入其它值
          if((['0','1', 0, 1].includes(obj?.t11) && ['0','1', 0, 1].includes(obj?.t22)) || obj.single){
              if(obj?.t11 == 1 && obj?.t22 == 1){
                  Message.warning(i18n_t('settlement_v2.v_19'))
                  return
              }
              let item = {
                  ...obj,
                  // 由于弹窗修改的是t1和t2，这里需要根据他们传递 home away none
                  homeAway_a: obj.single ? 'none' : obj.t11 == '1' ? 'home' : obj.t22 == '1' ? 'away' : 'none'
              }, key = obj.key
              edit_match_settle_event(item, key, 2, 'corner')
          }else {
              Message.warning(i18n_t('settlement_v2.v_50'))
          }
        }
    },
     // 展示下一个角球弹窗
    export const set_score_goals = (payload,item, key) => {
      //console.log(item,'------------------------------')
      score_goals.value = { ...score_goals.value, key: key, ...item, }
      score_goals.value.is_show_checkbox = ['201','202','203','206','207','208'].includes(score_goals.value?.settleNum) ? false : true
      if(item.homeAway == 'none' && ((item.t1 || item.t1 == '0') || (item.t2 || item.t2 == '0'))) score_goals.value.single = true
      score_goals.value.show_score_goals = true
    },
     // 获取对应角球值 type 这个参数  1 代表主队获取  2 代表客队获取
    export const get_corner_num = (payload,item, type) => {
      // 只有主客队（t1或t2）有值的时候才展示对应的  （NC 没有角球  1 对应进球主客队  0 对应没有进球的主客队）
      // ((item.t1 || item.t1 == '0') || (item.t2 || item.t2 == '0')) 这个是判断逻辑，（t1或t2）可能为0，
      // 判断过程中0默认隐式转换为false所以增加 (itme.(t1或t2/主客队) == '0')
      if(item.homeAway == 'none' && ((item.t1 || item.t1 == '0') || (item.t2 || item.t2 == '0'))){
        return  'NC'
      }else if((item.homeAway == 'home' && type == 1) || (item.homeAway == 'away' && type == 2)){
        return  '1'
      }else {
        return (((item.homeAway && type == 1) || (item.homeAway && type == 2)) && ((item.t1 || item.t1 == '0') || (item.t2 || item.t2 == '0'))) ? '0' : ''
      }
    },
    /**
     * @description 删除-删除事件接口
     * @return {undefined} undefined
     */
    async handle_delete(item){
      let params = {
        operatorId: userInfo.value.user.userId,
        operatorName: userInfo.value.user.userCode,
        id: item.id
      }
      try {
        let res = await api_event.post_confirmDel(params)
        if(res.data.code == 200){
          Message.success('删除成功！')
          init_match_result(); //删除成功后重新调用事件接口
        }else{
          Message.error(res.data.msg)
        }
      } catch (error) {
        console.error(error)
      }
    },
     //  获取主客队红黄牌
    export const get_card_name = (payload,item) => {
      if(item.homeAway == 'none'){ // 没有
          return i18n_t('settlement_v2.v_41')
      }else if(item.homeAway == 'home'){
          return `${i18n_t('settlement_v2.v_56')} / ${event_code.value[item.eventCode || 'fa_card']}`
      }else if(item.homeAway == 'away'){
          return `${i18n_t('settlement_v2.v_57')} / ${event_code.value[item.eventCode || 'fa_card']}`
      }else {
          return ''
      }
    },
    /**
     * @description 拒绝-删除事件接口
     * @return {undefined} undefined
     */
    async handle_refuse(item){
      let params = {
        id: item.id,
        operatorId: userInfo.value.user.userId,
        operatorName: userInfo.value.user.userCode
      }
      try {
        let res = await api_event.post_ignoreDel(params)
        if(res.data.code == 200){
          Message.success('拒绝成功！')
          init_match_result(); //删除成功后重新调用事件接口
        }else{
          Message.error(res.data.msg)
        }
      } catch (error) {
        console.error(error)
      }
    },
    /**
     * @description 操盘/结算审核确认结算接口
     * @return {undefined} undefined
     */
    async handle_confirm_settle(){
      if(lodash.isEmpty(matchBatchResult_params.value)) return
      try {
        matchBatchResult_params.value.operatorId = userInfo.value.user.userId;
        matchBatchResult_params.value.operatorId = userInfo.value.user.userCode
        let res = await api_event.post_matchBatchResult_matchResultBatchConfirm(matchBatchResult_params.value);
        let { code,msg,data} = res.data;
        if(code == 200){
          Message.success(i18n_t('event.index.w47'))
          handle_close(true);
        }else{
          Message.error({
            content: msg || data,
            duration: 3
        })
        }
      } catch (error) {
        Message.error({
            content: error.msg ? error.msg : error.massgae ? error.massgae : error,
            duration: 3
        })
      }
    },
    /**
     * @description 商业数据源字段处理
     * @return {undefined} undefined
     */
    export const getcode = (payload,str) => {
      let arr = []
      if(str){
        let map = { SR: "#B83636", BC: "#5064D5", BG: "#299EB3", PA: "#00a997", RB: "#546BEC" }
        str = str.split(',')
        str.forEach(x => {
          arr.push({name:x,color:map[x]})
        })
      }
      return arr
    },
    async init_match_result(){
      let params = {
        operatorId: userInfo.value.user.userId,
        operatorName: userInfo.value.user.userCode,
        standardMatchId: settle_params.value.matchId,  // 赛事id
        categorySetId:  settle_params.value.categorySetId  // 玩法集id
      }
      try {
        tabledata_loading.value = true;
        let res = await api_event.post_footBallSettle_matchPeriodQuery(params)
        let code = lodash.get(res,'data.code')
        if(code == 200){
          let data = lodash.get(res,'data.data')
          let arr = []
          if([10001, 10002, 10003, 10005].includes(settle_params.value.categorySetId)){
            if(data.score.length > 0){
                data.score.forEach(item => {
                    arr.push({...item, is_show: true, outstanding: 0, extryInfo: item.extryInfo ?? ''})
                })
                // 设置排序
                arr.sort((a, b) => {
                    return a.settleNum - b.settleNum; //a-b为升序
                });
            }
            list_table_data.value = arr
            list_table_event_data.value =
                data.event.length > 0 ? set_data_settle_event(data.event, 1, settle_params.value.categorySetId == 10002 ? 'corner' : '', settle_params.value.categorySetId == 10003 ? 'overTime' : '') : []
            list_table_event_data_copy.value = JSON.parse(JSON.stringify(list_table_event_data.value))
         }else if(settle_params.value.categorySetId == 10004){
            penalty_kick_data.value = data ? get_penalty_kick_data(data) : []
          }
        //  dialog_data.value
        }else{
          Message.error(`${res.data.msg}`);
        }
      } catch (error) {
        Message.error(`接口报错！`);
        console.error(error)
      } finally{
        tabledata_loading.value = false;
      }
    },
    export const handle_close2 = (payload) => {
      emit('handle_close2')
    },
    export const handle_confirm = (payload) => {
      emit('handle_confirm')
    }
  },
}
</script>
<style lang="scss" src="project/caopan/pages/settlement_manage/settlement_v2/components/table_style.scss" scoped></style>
<style lang="scss"  scoped >
.cpm_box{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0,0,0,.2);
    z-index: 1000;
    .event_dialog{
        min-width: 320px;
        min-height: 210px;
        padding: 10px 10px 20px 10px;
        font-size: 14px;
        background: var(--q-color-panda-light-grey2);
        .title{
            position: relative;
            i{
                position: absolute;
                right: 0px;
                top: 0px;
                z-index: 1;
                cursor: pointer;
            }
        }
        .textdiv{
            padding: 10px 30px;
            margin-bottom: 20px;
            .shua{
                display: flex;
                justify-content: left;
                align-items: center;
                margin-bottom: 10px;
                .icon_shua{
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    margin: 0 10px;
                    cursor: pointer;
                    background: url('/assets/settlement_v2_icon/refresh.svg') no-repeat 100%/100%;
                }
                .icon_shua:hover{
                    background: url('/assets/settlement_v2_icon/refresh_a.svg') no-repeat 100%/100%;
                }
            }
            .xuan_box{
                >span{
                    display: inline-block;
                    width: 80px;
                }
            }
        }
    }
}
</style>
