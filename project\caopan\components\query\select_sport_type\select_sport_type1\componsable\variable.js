import { create_base_state_instance_fn } from 'project/caopan/components/query/select_sport_type/select_sport_type1/variable/index.js';
import {
  src_componsable_global_variable_componsable_fn,
} from 'src/output/common/componsable-common.js';
import { get_window_size_info, get_sport_type_visible } from "src/output/common/store-common.js";

export const project_caopan_components_query_select_sport_type_variable_componsable_fn = () => {
  const global_obj = src_componsable_global_variable_componsable_fn();
  const payload = create_base_state_instance_fn();
   
  const window_size_info = get_window_size_info()
  const sport_type_visible = get_sport_type_visible()
  return {
    ...global_obj,
    ...payload,
    window_size_info,
    sport_type_visible,
  };
};
