import { watch } from "vue";
import { get_data_list } from "src/components/league_ranking/api-request/get_data_list.js";
import { set_footer_fn } from "src/components/league_ranking/module/index.js";
export const watcher_register = (payload) => {
  /**
   * @description: 头部赛种切换-切换时params参数处理
   * @param {Number} 赛种id
   */
  const { select_sportId, params, tournament_name } = payload;
  return [
    watch(
      select_sportId,
      (n) => {
        params.value.sportId = n; // 赛种id
        params.value.start = 1; //列表页数
        params.value.tournamentLevel = 1; //列表等级
        tournament_name.value = "";
        params.value.versionNewStatus = "";
        params.value.tournamentName = "";
        params.value.id = null;
        get_data_list(payload); //请求列表数据
        set_footer_fn(payload); //头部联赛参数设置/足球的设置
      },
      { immediate: true }
    ),
  ];
};
