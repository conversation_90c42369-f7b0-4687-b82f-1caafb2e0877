import{ 
  compute_menu_1_class, 
  compute_menu_label_class, 
  compute_menu_icon_class,
  compute_menu_bg_class
} from "project/caopan/layouts/module/style.js"


export const style_componsable_fn = (payload) => {


    return {
      compute_menu_1_class: item => compute_menu_1_class(payload, item ),
      compute_menu_label_class: item => compute_menu_label_class(payload, item ),
      compute_menu_icon_class: item => compute_menu_icon_class(payload, item ),
      compute_menu_bg_class: item => compute_menu_bg_class(payload, item)
    }
}