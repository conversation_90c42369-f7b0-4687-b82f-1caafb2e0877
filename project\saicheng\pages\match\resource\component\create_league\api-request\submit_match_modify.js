import {
  api_bfw_resource,
} from "project/bifenwang/api/index.js";

import {
  Message,
  lodash,
  i18n_t,
  alert_message_default,
} from "src/output/common/project-common.js";
import { is_add_roundType } from "project/saicheng/pages/match/resource/component/create_league/module/index.js";

export const post_tournament_addTournament_handle = (payload, res) => {
  const { emit } = payload;
  let code = lodash.get(res, "data.code");
  alert_message_default(res);
  if (code == 200) {
    // 通知刷新
    emit("close");
    emit("edit_success");
  }
};
// 保存并应用
export const submit_match_modify = async (payload) => {
  const {
    region,
    matchCategory,
    tournamentGroupType,
    round_type,
    league_affiliation,
    level,
    matchType,
    league_url,
    openSummerWinterTimeFlag,
    shallow_edit_obj,
    filePath_72,
    filePath_50,
    is_click,
    promotion_quota,
    relegation_quota,
    tournamentCreateType,
    props,
    language_list,
    matchCategory_obj,
    matchPeriodType,
    other_cycles_obj
  } = payload;
  if (!props.cur_sportId) return;
  if (!region.value)
    return Message.warning(i18n_t("saicheng.manage_alert.alert31")); // 联赛区域不可为空
  // 组别是否必填
  // if (matchCategory.value == 2 && !tournamentGroupType.value) {
  //   return Message.error(i18n_t("saicheng.match_edit.group_no_empty"));
  // }
 
  if (is_add_roundType(payload) && round_type.value < 0)
    return Message.warning(i18n_t("saicheng.manage_alert.alert43")); // 比赛赛制不可为空
  if (!matchCategory.value) return Message.warning(i18n_t("event.add.w101")); // 类型不可为空
  

  if (matchCategory_obj.value.show  && !matchCategory_obj.value.currentRoundNumber ) {
    return Message.warning(i18n_t('saicheng.match_create_league.sc_type_placeholder'))
  }  
  
  if (!matchPeriodType.value)
    return Message.warning(i18n_t("saicheng.match_match_cycle.match_cycle_message1"))   

  if (matchPeriodType.value == 5 && !other_cycles_obj.value.label) {
    return Message.warning(i18n_t("saicheng.match_match_cycle.match_cycle_message2"))   
  }
  if (matchPeriodType.value == 5 && !other_cycles_obj.value.code) {
    return Message.warning(i18n_t("saicheng.match_match_cycle.match_cycle_message3"))   
  }
  let otherPeriodType =  {"label":other_cycles_obj.value.label,"code":other_cycles_obj.value.code}


  var lanMap = {};
  let language_name = null;
  let language_key = null;

  for(let i=0;i<language_list.value.length;i++){
    var key = language_list.value[i].key;
    var name = language_list.value[i].name;
    var key_jc = key+"_jc";
    var value = language_list.value[i].title;
    var value_jc = language_list.value[i].title_jc;
    lanMap[key]= value;
    lanMap[key_jc]= value_jc;
    if(key == 'zs' || key == 'en' || key == 'zh' || key == 'vi'|| key == 'ko'|| key == 'th'){
      language_key = value ? false : true;
      language_name =name;
    }
    if(language_key){
      break;
    }
  }

  let language_message = i18n_t('saicheng.manage_alert.alert55').replace("{language}",language_name);

  if (language_key) return Message.warning(language_message)  // 联赛名称不可为空

 let tournamentNameJson = {
    // 联赛多语言名称
    ...lanMap
  };

  let params = {
    sportId: props.cur_sportId, // 球类ID
    matchType: matchType.value,
    matchCategory: matchCategory.value,
    tournamentLevel: level.value, // 联赛等级
    leagueUrl: league_url.value, // 联赛官网
    tournamentCreateType: tournamentCreateType.value, // 自建联赛标识 0:非自建，1：自建
    openSummerWinterTimeFlag: openSummerWinterTimeFlag.value, //开启夏冬令时标识,默认是0(0:关闭,1:开启)

    // 晋升名额
    promotionQuota: promotion_quota.value,
    // 降级名额
    relegationQuota: relegation_quota.value,
    // 赛制选择
    tournamentGroupType: tournamentGroupType.value,
    tournamentNameJson: JSON.stringify(tournamentNameJson),
    ...lodash.pick(shallow_edit_obj.value, ["regionId"]), // 联赛区域
    tournamentRoundType: round_type.value, //比赛赛制
    currentRoundNumber : matchCategory_obj.value.currentRoundNumber,
    matchPeriodType :matchPeriodType.value,// 比赛周期
    otherPeriodType  : JSON.stringify(otherPeriodType),
  };

  // 处理联赛LOGO
  if (filePath_72.value) {
    params.logoUrl = filePath_72.value;
  }
  if (filePath_50.value) {
    params.logoUrlThumb = filePath_50.value;
  }
  if (!is_click.value) return;
  is_click.value = false;

  try {
    let res = await api_bfw_resource.post_tournament_createTournament(params);

    post_tournament_addTournament_handle(payload, res);
  } catch (err) {
  } finally {
    is_click.value = true;
  }
};
