/**
 * @description: 判断当前环境，开发和预开售
 */
export const process = () => {
  let env = BUILDIN_CONFIG.ENVVARIABLES.FRONT_WEB_ENV;
  return env == "local_dev" || env == "idc_pre";
};
/**
 * @description: 重置列表排序 type有直接执行下面
 */
export const clearSort = (payload, type) => {
  const { old_params, params, columns } = payload;
  // 表格排序状态处理
  if (!type) {
    let { sportId, tournamentLevel } = old_params.value;
    if (
      sportId == params.value.sportId &&
      tournamentLevel == params.value.tournamentLevel
    ) {
      return false;
    }
  }
  let column = columns.value.map((item) => {
    item.sortType = "normal";
    return item;
  });
  columns.value = column;
};

/**
 * @description: 前端排序
 * @param {*} new_index 新下标
 * @param {*} old_index 老下标
 * @return {*}
 */
export const sort_table_data = (payload, new_index, old_index) => {
  const { params, data_list } = payload;
  let arr = data_list.value;
  arr.splice(new_index, 0, arr.splice(old_index, 1)[0]); // 数据处理
  arr.map((item, index) => {
    item.sort_index = index + 1 + (params.value.start - 1) * params.value.size;
  });
};
