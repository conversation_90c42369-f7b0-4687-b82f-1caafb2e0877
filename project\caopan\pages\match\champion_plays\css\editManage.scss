:deep( .ivu-radio-wrapper){
  margin-right: 20px;
  font-size: 12px;
}
:deep( .ivu-radio){
  transform: scale(.8);
}
:deep(  .ivu-input ){
  height: 24px;
  vertical-align: middle;
  border: 1px solid var(--q-color-panda-secondary2);
  background-color: var(--q-color-panda-field-grey) !important;
  color: var(--q-color-panda-text-light);
}

:deep( .ivu-input-wrapper){
  width: 70%;
  padding-bottom: 2px;
}
.edit_manage :deep( .ivu-tooltip){
  width: initial;
}
.one_line {
display: -webkit-box;
margin-right: 8px;
overflow: hidden;
text-overflow: ellipsis;
white-space: normal;
-webkit-line-clamp: 1;
-webkit-box-orient: vertical;
}
.radio_group {
p {
  margin-bottom: 20px;
}
.showLanguageDialog_icon {
  span:nth-child(1){
     display: inline-block;
     height: 20px;
     line-height: 20px;
     margin-right: 5px;
  }
  span:nth-child(2){
      display: inline-block;
      width: 15px;
      height: 15px;
      cursor: pointer;
      vertical-align: middle;
      background: url('/assets/settlement_v2_icon/edit.svg') no-repeat 100%/100% !important;
  }
  span:nth-child(2):hover{
      background: url('/assets/settlement_v2_icon/edit_a.svg') no-repeat 100%/100% !important;
  }
}
}
.list {
margin-bottom: 40px;
border: 1px solid var(--q-color-panda-table-border);
overflow-y: auto;
background-color: var(--q-color-panda-base-dark);
li {
  height: 30px;
  line-height: 30px;
  border-bottom: 1px solid var(--q-color-panda-table-border);
  &:last-child {
    border: none;
  }
  &:nth-child(1) {
    position: fixed;
    z-index: 100;
    width: 708px;
    background-color: var(--q-color-panda-secondary2);
  }
  &:nth-child(2) {
    margin-top: 30px;
  }
  & > span {
    display: inline-block;
    &:nth-child(1) {
      width: 100px;
      padding-left: 10px;
    }
    &:nth-child(2) {
      margin-right:20px;
    }
    &:nth-child(3) {
      width: 30%;
    }
    &:nth-child(4) {
      width: 20%;
    }
  }
}
}
.buttons {
text-align: center;
span {
  margin: 0 6px;
  padding: 6px 20px;
  border: 1px solid var(--q-color-panda-text-light);
  border-radius: 4px;
  cursor: pointer;
  color: color var(--q-color-panda-text-light);
}
.green {
  background-color: #00a997;
  border-color: #00a997;
  color: #fff;
}
}
.grey_bg {
display: inline-block;
width: 40px;
height: 24px;
line-height: 24px;
border-radius: 2px;
text-align: center;
background-color: var(--q-color-panda-field-grey) !important;
}
.btn {
height: 24px;
line-height: 24px;
font-size: 12px;
}

// 编辑管理 保存时loading的样式
:deep( .ivu-spin-fix){
  background-color: inherit !important
  // opacity: 0.7
}
.edit-loading{
  width: 635px;
  top: -10px;
  z-index: 1000;
  position: relative;
}
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
  font-size: 30px!important
}
@keyframes ani-demo-spin {
  from { transform: rotate(0deg);}
  50%  { transform: rotate(180deg);}
  to   { transform: rotate(360deg);}
}