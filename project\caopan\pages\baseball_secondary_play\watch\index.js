
import { watch } from "vue"
export const watcher_register = (payload) => {
    const {
        item,
        time
    } = payload

    return [
        //多个watch，透过array传出,可以直接使用payload上下文
        watch(() => item, (val, oldval) => {

            if (val.matchManageId == oldval.matchManageId) { // 对比赛事id是否一致
                if (val.secondsMatchStart !== oldval.secondsMatchStart && val.secondsMatchStart > oldval.secondsMatchStart) {
                    // 判断推送过来的倒计时 是否大于当前的倒计时  否则不给与赋值
                    time.value.secondsMatchStart = val.secondsMatchStart;   //倒计时最新值
                } else {
                    time.value.matchPeriodId = val.matchPeriodId; //赋值赛事阶段
                }
            } else {
                time.value = val;
            }
        }),

    ]
}