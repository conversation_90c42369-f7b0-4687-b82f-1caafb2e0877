.sort_btn {
  position: relative;
  right: 20px;
}
.sort {
  position: relative;
  right: 30px;
  display: flex;
  align-items: center;
}
.sort > div {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  margin-left: 10px;
  padding: 0 10px;
  border-radius: 2px;
  background-color: var(--q-color-panda-primary);
  color: #fff;
  cursor: pointer;
}
.sort > div:hover {
  background-color: rgba(0, 169, 151, 0.4);
}
.operate {
  display: flex;
  align-items: center;
  img {
    width: 16px;
    height: 16px;
    &.refresh_menu_active {
      transform: rotate(1000deg);
      transition: all 1s linear;
    }
    &.gray {
      -webkit-filter: grayscale(100%);
      -moz-filter: grayscale(100%);
      -ms-filter: grayscale(100%);
      -o-filter: grayscale(100%);
      filter: grayscale(100%);
      filter: rgb(153, 153, 153);
    }
  }
}
.operate > div {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  margin-left: 10px;
  padding: 0 10px;
  border-radius: 2px;
  background-color: var(--q-color-panda-primary);
  color: #fff;
  cursor: pointer;
}
.operate > div:hover {
  background-color: rgba(0, 169, 151, 0.4);
}
.borderBlue :deep(.ivu-input) {
  border-color: #00a997 !important;
}
.warnNum {
  margin-left: 10px;
  cursor: pointer;
  height: 32px;
  line-height: 46px;
}
.warnNum .panda_icon_alert {
  font-size: 16px;
}
.warnNum .panda_icon_alert:before {
  color: #00a997;
}
.warnNum .num {
  position: relative;
  top: -12px;
  left: -6px;
  background-color: #e23c39;
  color: #fff;
  border-radius: 50px;
  padding: 1px 5px;
}
.panda_icon_warn {
  font-size: 14px;
}

.text-panda-date-base {
  color: var(--q-color-panda-text-base);
}

.text-panda-date-light {
  color: var(--q-color-panda-text-light);
}

.panda-border-1px {
  border: solid 1px var(--q-color-panda-secondary);
}

.bg-panda-query-date-active .panda-query-date-tab-label {
  background-color: var(--q-color-panda-primary);
  border: none;
}
.bg-panda-query-date-active .panda-query-date-tab-label:hover {
  background: #00a99766 !important;
  color: #fff !important;
  border: 0 none;
}

.text-panda-date-light.panda-query-date-tab-label:hover {
  color: var(--q-color-panda-primary) !important;
  border: solid 1px var(--q-color-panda-primary);
}
.panda_show_more {
  position: relative;
  display: inline-block;
}

.panda_show_more:before {
  position: absolute;
  bottom: -6px;
  right: 0;
  left: 0;
  margin: 0 auto;
  content: '';
  display: block;
  width: 0;
  height: 2px;
  background-color: var(--q-color-panda-primary);
  transition: all 0.3s;
}

.panda_show_more:hover:before {
  width: 100%;
}

.panda_show_more:hover {
  color: var(--q-color-panda-primary);
  [class^='panda_']:before {
    color: var(--q-color-panda-primary);
  }
}

.period__icon-badge {
  display: inline-block;
  min-width: 13px;
  height: 13px;
  background-color: #ff494a;
  color: #fff;
  border-radius: 50%;
  font-size: 13px;
  line-height: 13px;
  font-weight: normal;
  text-align: center;
  transform: translate(-5px, -5px);
}
