.child-modal {
    z-index: 1010;
}

.link {
    height: 15px;
    vertical-align: -3px;
    display: inline-block;
    width: 15px;
    cursor: pointer;
    background: url("/assets/settlement_v2_icon/link.svg") no-repeat 100%/100%;
}

.icon {
    font-size: 18px;
}

.icon:hover {
    color: #00a997;
}

:deep(.ivu-input-icon) {
    line-height: 22px;
    cursor: pointer;
}

:deep(.ivu-input) {
    height: 20px;
    background-color: var(--q-color-panda-field-grey);
    border-radius: 2px;
    color: var(--q-color-panda-text-light);
    font-weight: 400;
    border: 1px solid var(--q-color-panda-secondary2);
}

:deep(.ivu-table, :deep(.ivu-table th)) {
    color: var(--q-color-panda-text-light);
    border-bottom: 1px solid var(--q-color-panda-table-border);
}

:deep(.ivu-modal-content, :deep(.ivu-table td)) {
    background: var(--q-color-panda-base-light4) !important;
    border-bottom: 1px solid var(--q-color-panda-table-border);
}

:deep(.ivu-table-wrapper) {
    border: 1px solid var(--q-color-panda-base-light4) !important;
}

:deep(.ivu-modal-header) {
    border-bottom: none;
}

:deep(.ivu-modal-body) {
    padding: 0 !important;
}

:deep(.ivu-table-overflowX, :deep(.ivu-table-tip)) {
    overflow-x: hidden !important;
}

:deep(.ivu-table:before) {
    height: 0px;
}

:deep(.ivu-modal-close) {
    top: 0;
}

:deep(.ivu-modal-content > .ivu-modal-close :hover) {
    background-color: transparent;
    color: #00a997;
}