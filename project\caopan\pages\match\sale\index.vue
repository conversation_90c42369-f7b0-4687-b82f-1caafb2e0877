<template>
  <div class="full-height full-width use-ivew">
    <div>
      <selectSportType/>
    </div>
    <main class="row p-fwnw"  style="height: calc(-75px + 100vh);">
      <!-- 左侧查询区域begin -->
      <div v-show="show_more_query" class="bg-panda-base-light4 text-panda-text-light p-fs0 p1-bs--1"
        style="border-right:1px solid var(--q-color-panda-border-color-secondary);margin-left: 1px"
        :style="{ width: show_more_query ? '250px' : '0' }">
        <moreQuery v-if="get_dsc_options().length > 0" ref="moreQuery4"
          :current_match_status_constant="match_sell_status_arr" :odds_status_arr="odds_status_constant"
          :league_areaList="league_areaList" :operation_group="operation_group" :params="query_module"
          :sources_data="get_dsc_options()" :match_levels="match_levels" :league_name="league_name"
          :select_sportId="select_sportId" :query_times="query_times" @query="handle_query"
          @toggle_modal="toggle_modal"></moreQuery>
      </div>
      <!-- 列表主区域begin -->
      <div :style="main_component_style">
        <div class="p-mw--sm p-fg1">
          <!-- 查询条件区域  -->
          <div v-show="!show_presell_log" class="col-12 ">
            <!-- 日期选择组件 -->
            <selectQueryPeriod :default_select="query_time" :query_times="query_times"
              :show_show_more_query_btn="false" :right_bnt="(right_bnt = true)" :is_export="is_export"
              :bgclass="'bg-panda-expand-bg-color'" :tabledata_loading="tabledata_loading"
              :tableDataCount="tabledata ? tabledata.length : 0" @query_period_change="query_period_change"
              @show_more_query_change="show_more_query_change" @export_data="export_data"
              @init_tabledata="init_tabledata" @edit_sort_status="edit_sort_status"
              @sort_save="sort_save"></selectQueryPeriod>
            <div v-if="$util.show_yj_mock"
              class="panda-py-4px panda-px-10px border-radius-2px panda-query-date-tab-label mr-10 yjks"
              @click="handle_yjks_mock">
              {{ i18n_t('saleTable.sa_101') }}
            </div>
          </div>
          <!-- 表格展示开始 -->
          <div v-show="!show_presell_log">
            <tableNoData ref="table-no-data" :table_no_data_visable="show_table_no_data"
              :tabledata_loading="tabledata_loading"
              :style="{ width: '100%', height: 'calc(100% - 77px)' }"></tableNoData>
            <div class="panda-table-father" >
              <q-table  v-table_fixed_columns="{ left: 8 }" :rows="tabledata" :data="tabledata" :columns="tablecolumns"
                :visible-columns="visibleColumns" dense table-class="tablescrollTop" row-key="id"
                class="bg-panda-base-dark text-panda-text-light panda-table panda-sticky-header-table full-width"
                table-header-class="panda-table panda-table-col-text-indent" :separator="separator"
                :pagination="pagination" hide-pagination :no-data-label="data_label" :table-style="`max-height: ${scroll_area_height}px; height: ${scroll_area_height}px `
                  ">
                  <!-- :pagination.sync="pagination" hide-pagination :no-data-label="data_label" :table-style="`max-height: ${scroll_area_height}px; height: ${scroll_area_height}px ` -->
                <template v-slot:body="props">
                    <tablerow
                    :table_props="props"
                    :matchStatus="matchStatus"
                    :sort_status="sort_status"
                    :play_data="play_data"
                    :is_top_slot="0"
                    :tablecolumns="tablecolumns"
                    :show_right_detail_obj="show_right_detail_obj"
                    :select_sportId="select_sportId"
                    :queryform_form="queryform_form"
                    :isFavorite="isFavorite"
                    :match_sell_status_arr="match_sell_status_arr"
                    :actived_matchInfoId="actived_matchInfoId"
                    @table_item_click_expanded="table_item_click_expanded"
                    @change_preRiskManagerCode="change_preRiskManagerCode"
                    @item_click="table_item_click"
                    @edit_match_sale="edit_match_sale"
                    @p_ups="(row) => { console.log('父组件接收到 p_ups 事件:', row); p_ups_local(row); }"
                    @related_events="related_events"
                    @submit_success="submit_success"
                    @collect_num="collect_num"
                    @change_source_weight="change_source_weight"
                    @r_click="r_click"
                    @mts_sold="mts_sold"
                    @vieworAddTrader="vieworAddTrader"
                    @build_match_click="build_match_click"
                    @log_click="log_click"
                    ></tablerow>
                    <!-- @click="row_click(props.row);" -->
                    <template>
                  </template>
                </template>
              </q-table>
              <iPage class="pages" style="margin-top: 3px;text-align: center" @on-change="change"
                @on-page-size-change="size_change" :current="queryform_form?.currentPage"
                :page-size="queryform_form?.pageSize" :page-size-opts="[10, 20, 50, 100]" :total="total" show-sizer
                show-elevator show-total />
            </div>
          </div>
          <!--日志标题-->
          <div v-show="show_presell_log" class="col-12 ">
             <logTitle :show_toggle="false" :show_right_detail_obj="right_detail_log"
              @close_presell_log="show_presell_log = false"></logTitle> 
          </div>
          <div class="panda-table-father" v-if="show_presell_log">
            <presellLog :right_detail_obj="right_detail_log" :isLive=false></presellLog>
          </div>
          <!--日志记录结束-->
        </div>
      </div>
      <!-- 右侧主区域begin -->
      <!-- v-if="showrightdetail == 1" -->
      <div class="bg-panda-base-light4 panda-table text-panda-text-light p-fs0 p1-bs--1" v-if="showrightdetail?.match_sale == 1" 
        style="width:350px;">
        <rightInfo2 v-show="show_edit_match_sale" :right_detail_obj="show_right_detail_obj" @close="close_right_info()"
          @commit_success="submit_success" class="border-left-light2">
        </rightInfo2>
      </div>
    </main>

    
    <!-- 调试信息 -->
    <div v-if="true" style="position: fixed; top: 10px; right: 10px; background: red; color: white; padding: 10px; z-index: 9999; font-size: 12px;">
      <div>模板中的 Pup_dialog: {{ Pup_dialog }}</div>
      <div>Pup_dialog 类型: {{ typeof Pup_dialog }}</div>
      <div>Pup_dialog 是否为 ref: {{ Pup_dialog && Pup_dialog.__v_isRef ? 'true' : 'false' }}</div>
      <br>
      <button @click="testPupDialog" style="margin-top: 5px; background: green; color: white; border: none; padding: 5px;">
        测试设置为 true
      </button>
      <br>
      <button @click="Pup_dialog = false" style="margin-top: 5px; background: blue; color: white; border: none; padding: 5px;">
        测试设置为 false
      </button>
      <br>
      <button @click="debugPupDialog" style="margin-top: 5px; background: purple; color: white; border: none; padding: 5px;">
        调试 Pup_dialog
      </button>
      <br>
      <button @click="testQuasarDialog" style="margin-top: 5px; background: yellow; color: black; border: none; padding: 5px;">
        测试 $q.dialog
      </button>
    </div>

    <!-- 测试用简单模态框 -->
    <div v-if="Pup_dialog" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
      <div style="background: white; padding: 20px; border-radius: 8px; max-width: 600px; width: 90%;">
        <h3>测试模态框</h3>
        <p>如果你能看到这个，说明响应式绑定是正常的！</p>
        <button @click="Pup_dialog = false" style="background: red; color: white; border: none; padding: 10px; border-radius: 4px;">
          关闭
        </button>
      </div>
    </div>

    <!-- 自定义模态框替代 Quasar Dialog -->
    <div v-if="Pup_dialog" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" @click.self="Pup_dialog = false">
      <div style="min-width:600px;height: auto;background: #2d3748;border-radius: 8px;box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);" class="bg-panda-secondary" ref="touchAutomatic">
        <div class="dialog_hider" style="text-align: right;width:90%;height: 31px;">
          <button @click="Pup_dialog = false" style="position: absolute;right: 10px;top: 10px;background: none;border: none;color: white;font-size: 18px;cursor: pointer;">×</button>
        </div>
        <div class="dialog_title bg-panda-base-light pl10x border-bottom-light border-top-light"
          style="line-height: 30px;">
          <span>{{ compute_team_table_item_show_name(name_obj.leagueName) }}</span>
        </div>
        <div class="row border-bottom-col" style="line-height: 30px;">
          <div class="col-3 pl10x border-right-col"> {{ i18n_t("traderTable.qTable_2") }} </div>
          <div class="col pl10x border-right-col" v-for="(item, index) in pup_data" :key="`pup_data_${index}`">
            {{
              item.marketCategoryId == 1
                ? i18n_t("traderTable.qTable_3")
                : item.marketCategoryId == 2
                  ? i18n_t("traderTable.qTable_12")
                  : i18n_t("traderTable.qTable_11")
            }}
          </div>
        </div>
        <div class="row border-bottom-col">
          <div class="col-3 pl10x border-right-col" style="line-height:22px;">
            <p>
              <span>{{
                compute_team_table_item_show_name(name_obj.homeTeamNames)
              }}</span>
            </p>
            <p>
              <span>{{
                compute_team_table_item_show_name(name_obj.awayTeamNames)
              }}</span>
            </p>
            <p>{{ i18n_t("traderTable.with") }}</p>
          </div>
          <div v-for="(item, index) in pup_data" :key="`pup_data_${index}`" class="col border-right-col">
            <div v-show="item.marketCategoryId == 1 &&
              item.standardMarketOddsBOList &&
              item.standardMarketOddsBOList.length > 0
              ">
              <div v-for="(i, index2) in item.standardMarketOddsBOList" :key="`standard_market_oddsBO_list_${index2}`"
                class="row">
                <span class="nameExpressionValue mr5x" style="color: #E93D3D;width:30px;display: inline-block;">
                  <span v-show="index2 == 0">{{ item.marketValue }}</span>
                </span>
                <span class="fieldOddsOriginValue">{{ i.oddsValue }}</span>
              </div>
            </div>
            <div v-show="item.marketCategoryId == 2 &&
              item.standardMarketOddsBOList &&
              item.standardMarketOddsBOList.length > 0
              ">
              <div v-for="(i, index2) in item.standardMarketOddsBOList" :key="`standard_market_oddsBO_list_${index2}`"
                class="row">
                <span class="nameExpressionValue mr5x" style="color: #E93D3D;width:30px;display: inline-block;">
                  <span v-show="index2 == 0">{{ item.marketValue }}</span>
                </span>
                <span class="fieldOddsOriginValue">{{ i.oddsValue }}</span>
              </div>
            </div>
            <div v-show="item.marketCategoryId == 4 &&
              item.standardMarketOddsBOList &&
              item.standardMarketOddsBOList.length > 0
              ">
              <div v-for="(i, index2) in item.standardMarketOddsBOList" :key="`standard_market_oddsBO_list_${index2}`"
                class="row">
                <span class="nameExpressionValue mr5x" style="color: #E93D3D;width:30px;display: inline-block;">
                  <span v-show="index2 == 0">{{ item.marketValue }}</span>
                </span>
                <span class="fieldOddsOriginValue">{{ i.oddsValue }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--设置数据源权重弹窗 开始-->
    <iModal class="set-data-source" v-model="show_dialog" @on-ok="set_source_weight"><!-- 数据源权重设置 -->
      <template #header>
        <span style="font-size: 16px;">{{ i18n_t('saleTable.sa_76') }}</span>
        <span v-if="select_sportId == 1" style="height: 39px; margin-left: 20px;">
          <span style="font-size: 14px;">{{ i18n_t('traderTable.no_reject_order') }}</span>
          <iSwitch v-model="rejectStatus" :true-value="1" :false-value="0" style="margin-left: 4px;">
            <template #open>
              <span>{{ i18n_t("e_stop.t13") }}</span>
            </template>
            <template #close>
              <span>{{ i18n_t("e_stop.t14") }}</span>
            </template>
          </iSwitch>
        </span>
      </template>
      <iForm :model="source_weight_obj" :label-width="80">
        <div v-for="(item, index) in business_dataSourceCode?.filter(x => x != 'BT')" :key="index">
          <iFormItem :label="item">
            <iInput v-if="source_weight_obj.businessEventList.includes(item)"
              v-model.number="source_weight_obj[`${item.toLowerCase()}Weight`]" :disabled="item == 'BT'"></iInput>
          </iFormItem>
        </div>
      </iForm>
    </iModal>
    <!-- 数据源权重切换成功后弹窗 开始-->
    <iModal v-model="show_dialog2" :title="i18n_t('saleTable.sa_103')"><!-- 早盘数据源切换 -->
      <div>
        <p>{{ i18n_t('saleTable.sa_105') }}</p><!-- 数据源切换完成！ -->
        <!-- 系统对该赛事进行了关盘处理，请到早盘操盘页面确认后开盘！ -->
        <p>{{ i18n_t('saleTable.sa_106') }}<span style="color: #E93D3D">{{ i18n_t('saleTable.sa_107') }}</span>{{
          i18n_t('saleTable.sa_108') }}</p>
        <p>
          {{ i18n_t('saleTable.sa_110') }}
          <span style="color: #E93D3D">{{ i18n_t('common.inPlay') }}</span>
          {{ i18n_t('saleTable.sa_111') }}
          <span style="color: #E93D3D">{{ i18n_t('saleTable.sa_107') }}</span>
          {{ i18n_t('expectations.text29') }}！
        </p><!-- 注意：如果不开盘则该赛事进入滚球也会处于关盘状态 -->
      </div>
      <div name="footer">
        <iButton type="primary" @click="show_dialog2 = false">{{ i18n_t('common.confirm') }}</iButton><!-- 确定 -->
      </div>
    </iModal>
    <!--特殊关注组管理弹窗 开始-->
    <iModal v-model="show_modal" width="620" :footer-hide="true" :title="i18n_t('selectPeriodTab.Tab_15')"
      class="reset-modal use-ivew"><!--特殊关注设置-->
      <iTransfer :data="left_keys" :target-keys="target_keys" :list-style="list_style"
        :titles="[i18n_t('selectPeriodTab.Tab_16'), i18n_t('selectPeriodTab.Tab_17')]" filterable
        @on-change="handle_change" class="live-itransfer"><!-- 人员搜索   特殊关注人员列表-->
      </iTransfer>
    </iModal>
    <!--重新开赛弹窗 开始-->
    <iModal v-model="show_r" @on-ok="rest_match" @on-cancel="cancel_click">
      <p>
        <span class="panda_icon_info_waring">
          <span class="path1"></span><span class="path2"></span>
        </span>
        {{ i18n_t('saleTable.sa_95') }}
      </p><!--请确认是否重新激活赛事-->
      <p class="mt10x ml20x">{{ i18n_t('saleTable.sa_231') }}：<!--状态源选择-->
        <span v-for="(item, index) in reopen_data_source_list" :key="index" class="mr10x">
          <q-radio v-model="reopen_data_source" :label="item" :val="item" />
        </span>
      </p>
    </iModal>
    <!-- MTS操盘 切换 PA操盘 确认弹窗  开始-->
    <iModal class="mts_pa" v-model="change_mts_pa" @on-ok="change_pre_risk_manager_code_ok"
      @on-cancel="change_mts_pa = false">
      <p>
        <span class="panda_icon_info_waring"><span class="path1"></span><span class="path2"></span></span>
        <span style="color:#e93d3d;">{{ i18n_t('saleTable.la_10') }}?</span> <!-- 确认切换到PA操盘(数据源不变) -->
      </p>
    </iModal>
    <iModal class="mts_pa mts_pa1" v-model="change_mts_pa_conform" @on-ok="change_mts_pa_conform = false">
      <p>{{ i18n_t('saleTable.la_11') }}<span style="color:#e93d3d;">{{ i18n_t('saleTable.la_12') }}</span>,{{
        i18n_t('saleTable.la_13') }}</p>
      <!--  切换完成！赛事关盘,请确认赔率后手动开盘！ -->
    </iModal>
    <!-- 操盘指派弹窗 -->
    <!-- 操盘指派-->
    <iModal v-model="trader_model" :width="850" footer-hide :mask-closable="false" :closable="false">
      <assign v-if="trader_model" :assign_row="assign_row" @close_modal="close_trader_model"></assign>
    </iModal>
    <!-- 构建范特西赛事弹窗 开始-->
    <iModal @on-ok="save_build_match" v-model="build_match_model" draggable sticky scrollable :mask="false"
      :title="i18n_t('saleTable.sa_2550_2')" :ok-text="i18n_t('saleTable.sa_2550_1')">
      <div class="column" style="font-size:21px;">
        <div class="row">
          <div style="flex:1">
            {{ build_select_row != null ? compute_team_table_item_show_name(build_select_row.homeTeamNames) : "" }}
          </div>
          <div>
            <iButton type="info" @click="select_team(1, 1)"
              :disabled="build_select_row.teamHomeId === build_match_data.newHomeTeamId && build_match_data.newHomeMatchId === build_select_row.matchInfoId"
              style="height: 35px;font-size:15px;">
              1
            </iButton>
          </div>
          <div class="q-ml-xs">
            <iButton type="info" @click="select_team(1, 2)"
              :disabled="build_select_row.teamHomeId === build_match_data.newAwayTeamId && build_match_data.newAwayMatchId === build_select_row.matchInfoId"
              style="height: 35px;font-size:15px;">
              2
            </iButton>
          </div>
        </div>
        <div class="row q-mt-xs">
          <div style="flex:1">
            {{ build_select_row != null ? compute_team_table_item_show_name(build_select_row.awayTeamNames) : "" }}
          </div>
          <div>
            <iButton type="info" @click="select_team(2, 1)"
              :disabled="build_select_row.teamAwayId === build_match_data.newHomeTeamId && build_match_data.newHomeMatchId === build_select_row.matchInfoId"
              style="height: 35px;font-size:15px;">
              1
            </iButton>
          </div>
          <div class="q-ml-xs">
            <iButton type="info" @click="select_team(2, 2)"
              :disabled="build_select_row.teamAwayId === build_match_data.newAwayTeamId && build_match_data.newAwayMatchId === build_select_row.matchInfoId"
              style="height: 35px;font-size:15px;">
              2
            </iButton>
          </div>
        </div>
      </div>
      <div class="column q-mt-md">
        <div class="row text-center" style="font-size:21px;">
          <div style="flex:1;border:1px solid #000;background-color:#70b603">{{ i18n_t('saleTable.sa_2550_3') }}</div>
          <div style="flex:1;border:1px solid #000;background-color:#00bfbf">{{ i18n_t('saleTable.sa_2550_4') }}</div>
        </div>
        <div class="row text-center" style="font-size:18px;">
          <div style="flex:1;border:1px solid #000;background-color:#fff;width:50%;">
            <div v-if="build_match_data.newHomeMatchId" class="row"
              style="align-items: center;width:100%;height: 35px;font-size:15px;background-color:#fff;color:#000">
              <iIcon type="md-close" @click="remove_team(1)" style="cursor: pointer;" />
              <div style="flex:1;text-overflow:ellipsis;width:170px;white-space: nowrap; overflow: hidden; ">
                {{ compute_team_table_item_show_name(build_match_data.newHomeTeamNames) }}
              </div>
              <iIcon type="ios-arrow-forward" />
            </div>
          </div>
          <div style="flex:1;border:1px solid #000;background-color:#fff;width:50%;">
            <div v-if="build_match_data.newAwayTeamId" class="row"
              style="align-items: center;width:100%;height: 35px;font-size:15px;background-color:#fff;color:#000">
              <iIcon type="ios-arrow-back" />
              <div style="flex:1;text-overflow:ellipsis;white-space: nowrap; overflow: hidden; ">
                {{ compute_team_table_item_show_name(build_match_data.newAwayTeamNames) }}
              </div>
              <iIcon type="md-close" @click="remove_team(2)" style="cursor: pointer;" />
            </div>
          </div>
        </div>
      </div>
    </iModal>
    <!-- 构建范特西赛事弹窗 结束-->
  </div>
</template>
<script setup>
import {defineAsyncComponent,computed,ref,watch,provide,nextTick } from "vue"
const selectQueryPeriod = defineAsyncComponent(() =>import("project/caopan/components/query/select_query_period3/select_query_period3.vue"))
import scoreItem from "project/caopan/components/score/score_item.vue";
const tablerow = defineAsyncComponent(() => import("project/caopan/pages/match/sale/component/tablerow/tablerow.vue"))
const rightInfo2 = defineAsyncComponent(() =>import("project/caopan/pages/match/sale/component/right_info2/right_info2.vue"))//设置赛事状态源
const moreQuery = defineAsyncComponent(() => import("project/caopan/components/query/match_left_query4/match_left_query4.vue"))//左侧搜索
import selectSportType from "project/saicheng/components/query/selectSportType/selectSportType1.vue";
import tableNoData from "project/caopan/components/table/tableNoData.vue"
import tableCellEllipsis from "project/caopan/components/table/table_cell_ellipsis/index.vue"//提示框
import presellLog from "app/project/caopan/pages/operation_set/pre_sale_set/component/presell_log/index.vue"//日志记录
import logTitle from "project/caopan/pages/operation_set/pre_sale_set/component/log_title/index.vue"//日志标题"
// import matchPeriod from "project/caopan/pages/operation_set/pre_sale_set/component/right_info_header/match_period/index.vue"
//操盘指派
import assign from "project/caopan/pages/trader/manage/module/assign/assign.vue"
// 当前的组件
import { project_caopan_pages_match_sale_componsable_variable_componsable_fn } from "project/caopan/pages/match/sale/componsable/variable.js"
import { project_caopan_pages_match_sale_componsable_index_componsable_fn } from "project/caopan/pages/match/sale/componsable/index.js"       
//mixin/windowResize      
// //salemixin
// import { project_caopan_pages_match_common_sale_variable_componsable_fn } from "project/caopan/pages/match/common/sale/componsable/variable.js"
import { project_caopan_pages_match_common_sale_componsable_fn } from "project/caopan/pages/match/common/sale/componsable/index.js"
//Tablequerymixin mixin  
import {project_caopan_componsable_table_query_componsable_fn} from "project/caopan/componsable/table_query/componsable/index.js"
import {project_caopan_componsable_style_table_col_width_componsable_fn,project_caopan_componsable_style_window_resize_componsable_fn} from "src/output/common/componsable-common.js";
import {business_dataSourceCode } from "src/output/common/project-common.js";
import {set_footer_fn,compute_scrollarea_style,} from "project/caopan/pages/match/sale/module/index.js"
import { init_tabledata as com_init_tabledata} from "project/caopan/pages/match/sale/api-request/init_tabledata.js"
import {  table_item_click_expanded} from "project/caopan/componsable/pages/match/match/module/click.js"
import { api_match } from "src/api/index.js";
import { Message, i18n_t } from "src/output/common/project-common";
//score_center mixin
import {project_caopan_pages_match_common_score_center_componsable_fn} from "project/caopan/pages/match/common/score_center/componsable/index.js"
//rightinfomixin  
import {project_caopan_pages_match_common_rightinfomixin_componsable_index_componsable_fn} from "project/caopan/pages/match/common/rightinfomixin/componsable/index.js"
import {compute_team_table_item_show_name} from "project/caopan/componsable/style/table_col_width/module/index.js";
import {project_caopan_componsable_websocket_componsable_fn} from "project/caopan/componsable/websocket/componsable/index.js"
import {project_caopan_componsable_pages_matchmanage_componsable_fn} from "project/caopan/componsable/pages/match/matchManage/componsable/index.js"
const base_state = project_caopan_pages_match_sale_componsable_variable_componsable_fn()
const {main_component_style} = base_state
const {compute_table_col_width,rebuild_tablecolumns_config} = project_caopan_componsable_style_table_col_width_componsable_fn({  ...base_state,})
const payload = {...base_state,rebuild_tablecolumns_config, params:{sportId:base_state.select_sportId},}
const {} = project_caopan_componsable_table_query_componsable_fn(payload)
project_caopan_componsable_style_window_resize_componsable_fn(payload)
project_caopan_pages_match_common_rightinfomixin_componsable_index_componsable_fn(payload)
const {compute_table_col_before_style} = project_caopan_componsable_pages_matchmanage_componsable_fn({...payload})
const {
  data_label,
  total,
  pagination,
  separator,
  tablecolumns, list_style,
  match_sell_status_arr,
  build_match_data,
  //当前选中的构建范特西赛事行数据
  build_select_row,
  //是否显示构建范特西赛事表单
  build_match_model,
  //是否开启不拒单 默认为开
  rejectStatus,
  //"是否可见字段"
  visibleColumns,
  //排序编辑状态
  sort_status,
  reopen_data,
  //手工重新开赛赛事状态源集合
  reopen_data_source_list,
  // 手工重新开赛赛事状态源
  reopen_data_source,
  //联赛名称
  league_name,
  //  日志开关
  show_presell_log,
  //   右侧日志详情
  right_detail_log,
  //重新开赛弹窗
  show_r,
  state,
  // 特殊关注组管理
  show_modal,
  left_keys,
  target_keys,
  play_data,
  pup_data,
  name_obj,
  //弹窗开关
  Pup_dialog,
  earlyTrading,
  matchStatus,
  right_component_name,
  show_edit_match_sale,
  queryform_form,
  show_more_query,
  default_select,
  queryform_module,
  scroll_area_height,
  show_right_detail,
  show_right_detail_obj,
  // 表格数据
  tabledata,
  // 表格数据备份
  tabledata_old,
  //20为自选赛事
  query_time,
  label_cache,
  cache_ws_command60,
  // 左侧查询联赛区域
  league_areaList,
  // 左侧查询赛事级别
  match_levels,
  operation_group,
  /**左侧查询结束 */
  //是否查询收藏赛事 0 不是 1 是
  isFavorite,
  //接口调用次数 用于区分是否是初次进去页面
  query_times,
  tabledata_loading,
  actived_matchInfoId,
  show_dialog,
  //数据源权重切换成功后弹窗
  show_dialog2,
  // MTS 切 PA
  change_mts_pa,
  change_mts_pa_obj,
  change_mts_pa_conform,
  source_weight_obj,
  // base_state_common
  pre_or_live,
  trading_sportId,
  caopan_sportId,
  assign_row,
  trader_model,
  rowIndex,
  show_table_no_data,
  select_sportId,
  is_export,

  odds_status_constant,
  query_module,
  showrightdetail,

    serverTime,
  // breadcrumbsPart3,
  sport_region_constant,
  sport_type_constant,
} = payload
// const Pup_dialog = ref(true)
const { websocket_connection_connect} =project_caopan_componsable_websocket_componsable_fn({...payload})
const { commit_match_manage_ws_submit_30000} = project_caopan_pages_match_common_score_center_componsable_fn({...payload,  websocket_connection_connect})
const {
  vieworAddTrader,
  close_trader_model,
  query_period_change,
  handle_query,
  show_match_reverse_sign,
  row_click,
  collect_num,
  r_click,
  cancel_click,
  toggle_modal,
  get_dsc_options,
  change,
  size_change,
  show_more_query_change,
  table_item_click,
  related_events,
  export_data,
  p_ups: p_ups_original,
  handle_change,
  rest_match,
  init_querydata,
  query_tournament_info,
  get_tab,
} = project_caopan_pages_match_common_sale_componsable_fn({...payload,set_footer_fn,compute_scrollarea_style,init_tabledata:com_init_tabledata})

// 重新定义 p_ups 方法，确保使用正确的 Pup_dialog 引用
const p_ups_local = (row) => {
  console.log('本地 p_ups 方法被调用，row:', row);
  console.log('本地 Pup_dialog 当前值:', Pup_dialog.value);

  name_obj.value = {};
  name_obj.value = {
    leagueName: row.leagueName,
    homeTeamNames: row.homeTeamNames,
    awayTeamNames: row.awayTeamNames,
  };

  let params = {
    marketType: pre_or_live.value, // PRE 早盘   LIVE 滚球
    matchInfoId: row.matchId || row.referenceId,
  };

  console.log('本地 API 请求参数:', params);

  // 调用 API 获取赔率数据
  api_match.post_getPlayOdds(params).then(({ data }) => {
    console.log('本地 API 响应数据:', data);

    if (data.code == 200) {
      pup_data.value = (data.data && init_data(data.data)) || [];
      console.log('本地处理后的 pup_data:', pup_data.value);

      if (pup_data.value.length > 0) {
        init_data();
        console.log('本地设置 Pup_dialog 为 true');
        Pup_dialog.value = true;
        console.log('本地设置后 Pup_dialog 值:', Pup_dialog.value);
      } else {
        console.log('本地 pup_data 为空，设置 Pup_dialog 为 false');
        Pup_dialog.value = false;
        Message.success(i18n_t("saleTable.sa_224")); //暂无赔率
      }
    } else {
      console.log('本地 API 返回错误，code:', data.code);
      Pup_dialog.value = false;
      Message.success(i18n_t("saleTable.sa_224")); //暂无赔率
    }
  }).catch(error => {
    console.error('本地 API 请求失败:', error);
    Pup_dialog.value = false;
  });
};

// 调试方法
const testPupDialog = () => {
  console.log('=== 调试 Pup_dialog ===');
  console.log('模板中的 Pup_dialog:', Pup_dialog);
  console.log('Pup_dialog.value:', Pup_dialog.value);
  console.log('Pup_dialog 是否为 ref:', Pup_dialog && Pup_dialog.__v_isRef);
  console.log('设置 Pup_dialog.value = true');
  Pup_dialog.value = true;
  console.log('设置后 Pup_dialog.value:', Pup_dialog.value);
};

const debugPupDialog = () => {
  console.log('=== 完整调试信息 ===');
  console.log('Pup_dialog 对象:', Pup_dialog);
  console.log('Pup_dialog 原型:', Object.getPrototypeOf(Pup_dialog));
  console.log('Pup_dialog 所有属性:', Object.getOwnPropertyNames(Pup_dialog));
  console.log('payload 中的 Pup_dialog:', payload.Pup_dialog);
  console.log('两个 Pup_dialog 是否相同:', Pup_dialog === payload.Pup_dialog);
};

const testQuasarDialog = () => {
  console.log('测试 Quasar $q.dialog');
  // 使用 Quasar 的程序化 API
  if (payload.$q && payload.$q.dialog) {
    payload.$q.dialog({
      title: '测试 Quasar Dialog',
      message: '这是使用 $q.dialog 创建的模态框',
      persistent: true
    });
  } else {
    console.log('$q.dialog 不可用');
    // 备用方案：直接设置 Pup_dialog
    console.log('使用备用方案设置 Pup_dialog');
    Pup_dialog.value = true;
  }
};

  const {
    change_pre_risk_manager_code_ok,
    change_weight,
    handle_yjks_mock,
    init_tabledata,
    mts_sold,
    save_build_match,
    sort_save,
    edit_sort_status,
    set_source_weight,
    edit_match_sale,
  remove_team,
  select_team,
  build_match_click,
  get_tablecolumns,
  change_source_weight,
  change_preRiskManagerCode,
  log_click,
  init_data,
  submit_success,
  close_right_info,
  } = project_caopan_pages_match_sale_componsable_index_componsable_fn({...payload,init_querydata,commit_match_manage_ws_submit_30000:commit_match_manage_ws_submit_30000,
    websocket_connection_connect,get_tab})
    provide('log_click', log_click)
provide('payload', payload)
</script>
<!-- <style lang="scss">
@import 'project/caopan/pages/match/common/sale.scss';
</style> -->
<style src="project/caopan/pages/match/common/sale.scss" lang="scss" scoped></style>
<style lang="scss" scoped>
  @import 'project/caopan/pages/match/sale/css/index.scss';

  .ivu-form-item-content {
    position: relative;
    line-height: 32px;
    font-size: 14px;
}

.ivu-form .ivu-form-item-label {
    color: var(--q-color-panda-text-light);
}
</style>