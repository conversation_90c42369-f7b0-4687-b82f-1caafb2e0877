/*
 * <AUTHOR> darwin
 * @Description    : 国际化  中文
 * @FilePath: /src/i18n-saicheng/zh-cn/index.js
 */
export default {
    "common": {
        "all": "全部",
        "league_link": "联赛官网",
        "export_1": "导出",
        "return": "返回",
        "cancel": "取消",
        "confirm": "确定",
        "close": "关闭",
        "clear": "清空",
        "add": "新增",
        "change": "修改",
        "select": "选择",
        "displayLeft": "展开左侧信息",
        "displayRight": "展开右侧信息",
        "hideLeft": "隐藏左侧信息",
        "hideRight": "隐藏右侧信息",
        "league": "联赛",
        "date": "日期",
        "save": "保存",
        "edit": "编辑",
        "yes": "是",
        "no": "否",
        "none": "无",
        "none1": "无",
        "level": "级",
        "mon": "周一",
        "tue": "周二",
        "wed": "周三",
        "thu": "周四",
        "fri": "周五",
        "sat": "周六",
        "sun": "周日",
        "year": "年",
        "month": "月",
        "day": "日",
        "white": "白色皮肤",
        "black": "黑色皮肤",
        "loginOut": "退出",
        "noDataText": "Sorry，没有找到您想要的结果，请更换查询条件！",
        "dataSourceCode_QT": "球探",
        "dataSourceCode_SB": "沙巴",
        "dataSourceCode_188": "188",
        "dataSourceCode_SBO": "SBO",
        "dataSourceCode_TS": "TS",
        "leagueArea": "请选择区域",
        "Keyword_search": "关键字搜索",
        "qsr_playField": "请输入比赛场地",
        "home": "主队",
        "away": "客队",
        "other": "其他",
        "remark": "备注",
        "total": "总计",
        "refresh": "刷新",
        "noMatchingData": "无匹配数据",
        "exportData": "导出数据",
        "search": "查询",
        "loading": "加载中",
        "waiting": "等待中",
        "copy": "已复制",
        "score": "比分",
        "booked": "已订阅，数据正常",
        "booked_no": "已订阅，无数据",
        "more": "",
        "summary": "",
        "current_page": "",
        "settled": "",
        "unsettled": "",
        "full_time": "全场",
        "league_relationship": "联赛关系",
        "over_time": "加",
        "penalty": "点",
        "succeed": "成功",
        "save_succeed": "保存成功",
        "fail": "失败",
        "input_file": "文件格式不正确",
        "Tokyo_2020_Olympic_Games": "2020东京奥运会",
        "Tokyo_2024_Olympic_Games": "2024巴黎奥运会",
        "fan": "反",
        "to_caopan": "请到操盘后台进行查看",
        "new_add": "新增区域",
        "wait_match": "待匹配",
        "matched": "匹配完成",
        "enable": "启用",
        "disable": "停用",
        "unrelated": "未关联",
        "related": "已关联",
        "third_info": "三方资讯",
        "related_info": "关联资讯",
        "eliminate": "淘汰赛",
        "club_team_flag": "是否为俱乐部",
        "team_settings": "球队设置",
        "season_id": "赛季ID",
        "team_news": "球队资讯",
        "league_news": "联赛资讯",
        "currently_total": "目前共",
        "teams": "支球队",
        "noTeamText": "未找到该关联球队",
        "team_info": "队伍资讯",

    },
    "menus": {
        "lm_0": "赛程",
        "lm_1": {
            "title": "赛程管理",
            "title_bfw": "联赛管理"
        },
        "lm_2": {
            "title": "预开售"
        },
        "lm_3": {
            "title": "事件审核"
        },
        "lm_4": {
            "title": "元数据",
            "children1": "联赛库",
            "children2": "球队库",
            "children3": "球员库"
        },
        "lm_5": {
            "title": "统计报表"
        },
        "lm_6": {
            "title": "审核管理"
        },
        "lm_7": {
            "title": "权限管理"
        },
        "lm_8": {
            "title": "操作日志"
        },
        "lm_9": {
            "title": "设置",
            "children1": "热词"
        },
        "lm_10": {
            "title": "虚拟体育",
            "children1": "联赛库",
            "children2": "球队库"
        },
        "lm_11": {
            "title": "电子竞技",
            "children1": "联赛库",
            "children2": "球队库"
        },
        "lm_12": {
            "title": "开售管理",
            "children1": "早盘赛事",
            "children2": "滚球赛事",
            "children3": "预开售设置",
        },
        "lm_13": {
            "title": "历史赛程",
            "children1": "历史赛程",
        },
        "lm_14": {
            "title": "冠军玩法",
        },
        "lm_15": {
            "title": "运营管理",
            "children1": "热推赛事",
            "children2": "联赛排序",
            "children3": "赛事排序",
            "children4": "奥运专题",
            "children5": "公告栏",
        },
        "lm_17": {
            "title": "补救工具",
            "children1": "赛程紧急补救工具",
            "children2": "结算紧急补救工具",
        },
    },
    "sports": {
        "sports_1": "足球",
        "sports_2": "篮球",
        "sports_3": "棒球",
        "sports_4": "冰球",
        "sports_5": "网球",
        "sports_6": "美式足球",
        "sports_7": "斯诺克",
        "sports_8": "乒乓球",
        "sports_9": "排球",
        "sports_10": "羽毛球",
        "sports_11": "手球",
        "sports_12": "拳击/格斗",
        "sports_13": "沙滩排球",
        "sports_14": "联合式橄榄球",
        "sports_15": "曲棍球",
        "sports_16": "水球",
        "sports_17": "田径",
        "sports_18": "娱乐",
        "sports_19": "游泳",
        "sports_20": "体操",
        "sports_21": "跳水",
        "sports_22": "射击",
        "sports_23": "举重",
        "sports_24": "射箭",
        "sports_25": "击剑",
        "sports_26": "冰壶",
        "sports_27": "跆拳道",
        "sports_28": "高尔夫",
        "sports_29": "自行车",
        "sports_30": "赛马",
        "sports_31": "帆船",
        "sports_32": "划船",
        "sports_33": "赛车运动",
        "sports_34": "柔道",
        "sports_35": "空手道",
        "sports_36": "摔跤",
        "sports_37": "板球",
        "sports_38": "飞镖",
        "sports_39": "沙滩足球",
        "sports_40": "其他",
        "sports_50": "趣味",
        "sports_1001": "虚拟足球",
        "sports_1002": "赛狗",
        "sports_1004": "虚拟篮球",
        "sports_1007": "泥地赛车",
        "sports_1008": "卡丁车",
        "sports_1009": "泥地摩托车",
        "sports_1010": "摩托车",
        "sports_1011": "赛马",
        "sports_1012": "虚拟马车赛",
        "sports_100": "英雄联盟",
        "sports_101": "Dota2",
    },
    "match_status": {
        "status_1": "全部",
        "status_2": "未开赛",
        "status_3": "滚球",
        "status_4": "暂停",
        "status_5": "结束",
        "status_6": "关闭",
        "status_7": "取消",
        "status_8": "放弃播报",
        "status_9": "延迟",
        "status_10": "未知",
        "status_11": "延期",
        "status_12": "比赛中断",
        "status_13": "比赛放弃",
        "status_14": "正在进行"
    },
    "manage_level": {
        "level_0": "联赛级展示",
        "level_1": "赛事级展示",
    },
    "match_status1": {
        "status_0": "未开赛",
        "status_1": "滚球",
        "status_2": "暂停",
        "status_3": "结束",
        "status_4": "关闭",
        "status_5": "取消",
        "status_6": "放弃播报",
        "status_7": "延迟",
        "status_8": "未知",
        "status_9": "延期",
        "status_10": "比赛中断",
        "status_11": "比赛放弃"
    },
    "manage_operation": {
        "operation1": "生成标准赛事",
        "operation2": "关联赛事",
        "operation3": "取消关联",
        "operation4": "直接生成标准赛事",
        "operation5": "关联选中并生成标准赛事",
        "operation6": "关闭",
        "operation7": "重置",
        "operation8": "对阵管理",
        "operation9": "移入预开售",
        "operation10": "需要关盘",
        "operation11": "赛事详情",
        "operation12": "操盘管理",
        "operation13": "标记相反",
        "operation14": "取消相反",
        "operation15": "赛果",
        "operation16": "仅解除赛事关联",
        "operation17": "解除球队关联并解除赛事关联",
        "operation18": "涉及的三方联赛",
        "operation19": "涉及的三方主队",
        "operation20": "涉及的三方客队",
        "operation21": "关联并生成标准赛事",
        "operation22": "关联选中并生成标准赛事",
        "operation23": "关闭",
        "operation24": "重置",
        "operation25": "对阵管理",
        "operation26": "移入预开售",
        "operation27": "需要关盘",
        "operation28": "共选中1条数据",
        "operation29": "关联",
        "operation30": "该比赛已生成标准赛事，是否要解除球队关联，请选择需要解除关联的球队",
        "operation31": "生成的标准赛事名称与以下赛事重复，确定将继续生成标准赛事",
        "operation40": "球队冲突",
        "operation41": "数据商下发主客相反信息",
        "operation42": "赛事下发",
        "operation43": "取消下发",
        "operation44": "取消批量下发",
        "operation45": "批量下发",
        "operation46": "联赛管理",
        "operation47": "取消映射",

    },
    "manage_alert": {
        "alert0": "操作成功!",
        "alert1": "操作失败!",
        "alert2": "处理",
        "alert3": "请设置联赛区域",
        "alert4": "赛事",
        "alert5": "请勾选需要取消关联的球队",
        "alert6": "赛事阶段",
        "alert7": "生成标准赛事将关联联赛和球队，确定继续生成标准赛事",
        "alert8": "该球队有相关受影响赛事,暂时不能取消关联,是否查看受影响赛事。",
        "alert9": "该赛程的联赛关联与联赛库已有关联关系不一致，需要修改联赛库后方可继续关联赛程，是否操作联赛修改",
        "alert10": "关联成功",
        "alert11": "关联失败",
        "alert12": "合并成功",
        "alert13": "合并失败",
        "alert14": "取消关联成功",
        "alert15": "取消关联失败",
        "alert16": "开盘成功",
        "alert17": "开盘失败",
        "alert18": "赛事关联需要准确无误，如错误关联将影响赛事无法正常开盘。",
        "alert19": "是否确定继续",
        "alert20": "联赛合并需要准确无误，非主体的标准联赛将从赛程后台，操盘后台移出，错误合并将影响联赛无法正常开盘。",
        "alert21": "本次赛事关联/合并存在三方联赛(或球队)属于不同的标准联赛(或球队)。",
        "alert22": "是否处理",
        "alert23": "确定取消关联数据源",
        "alert24": "您已选择取消操作",
        "alert25": "受影响赛事",
        "alert27": "不可同时勾选两条同数据源的数据，请修正后关联。",
        "alertNotBE": "BE数据源无法勾选。",
        "alert28": "该赛程的球队关联与球队库已有关联关系不一致，需要修改球队库后方可继续关联赛程，是否操作球队修改",
        "alert29": "关联推荐为空",
        "alert30": "已关联成功",
        "alert31": "联赛区域不可为空",
        "alert32": "联赛中文名称不可为空",
        "alert33": "联赛英文名称不可为空",
        "alert34": "图片压缩后仍超过10KB，不可上传，请重新选择图片",
        "alert35": "图片尺寸不可超过150*150",
        "alert36": "图片超过1M，不可上传，请重新选择图片",
        "alert37": "主客对调/取消对调操作后，会改变对阵/赔率/事件/赛果的下发，请确认是否继续操作！",
        "alert38": "标记相反操作后，会改变该三方数据源的赔率/事件/赛果的下发，请确认是否继续操作！",
        "alert39": "取消相反操作后，会改变该三方数据源的赔率/事件/赛果的下发，请确认是否继续操作！",
        "alert40": "球队中文名称不可为空",
        "alert41": "球队英文名称不可为空",
        "alert42": "两个标准赛事下面都有标反赛事时，不允许关联合并",
        "alert43": "比赛赛制不可为空",
        "alert44": "赛事关联需要准确无误，如错误关联将影响赛事无法正常开赛，",
        "alert45": "请确认是否继续？",
        "alert46": "该赛事在赛程后台生成标准赛事,请确认是否下发！下发后需在赛程后台手动关联映射关系。",
        "alert47": "确认是否下发该赛事！",
        "alert48": "赛季删除请确认并无赛程产生，如错误删除将影响赛事无法正常开赛，",
        "alert49": "请确认是否取消关联此队伍",
        "alert50": "是否取消下发,取消后将无法再次下发！",
        "alert51": "取消下发原因",
        "alert52": "请确认是否取消映射",
        "alert53": "请确认是否取消关联此队伍",
        "alert54": "请确认是否下发",
        "alert55": "联赛{language}名称不可为空",

    },
    "score_center": {
        "text_1": "SR数据源",
        "text_2": "BC数据源",
        "text_3": "BG数据源",
        "text_4": "TX数据源",
        "text_5": "RB数据源",
        "text_6": "无实时事件统计",
        "text_7": "无实时事件流",
        "text_8": "取消",
        "text_9": "加时赛",
        "text_10": "点球战",
        "text_11": "赛盘",
        "text_12": "局分",
        "text_13": "总分",
        "text_14": "局",
        "text_15": "盘",
        "text_16": "胜制",
        "sportId_1_1": "角球",
        "sportId_1_2": "红牌",
        "sportId_1_3": "黄牌",
        "sportId_1_4": "进球",
        "sportId_1_5": "进攻",
        "sportId_1_6": "危险进攻",
        "sportId_1_7": "控球率",
        "sportId_1_8": "越位",
        "sportId_1_9": "射正",
        "sportId_1_10": "射偏",
        "sportId_1_11": "换人",
        "sportId_1_12": "点球",
        "sportId_1_13": "任意球",
        "sportId_2_1": "比分",
        "sportId_2_2": "篮板",
        "sportId_2_3": "两分球",
        "sportId_2_4": "三分球",
        "sportId_2_5": "罚球命中次数",
        "sportId_2_6": "数据统计",
        "sportId_3_1": "投球数",
        "sportId_3_2": "好球数",
        "sportId_3_3": "保送",
        "sportId_3_4": "坏球数",
        "sportId_3_5": "触身球",
        "sportId_3_6": "安打",
        "sportId_3_7": "一垒安打",
        "sportId_3_8": "二垒安打",
        "sportId_3_9": "三垒安打",
        "sportId_3_10": "本垒打",
        "sportId_3_11": "得分",
        "sportId_3_12": "打者数",
        "sportId_3_13": "残垒数",
        "sportId_3_14": "上垒",
        "sportId_3_15": "得分",
        "sportId_3_16": "安打",
        "sportId_3_17": "失误",
        "sportId_3_18": "好球",
        "sportId_3_19": "坏球",
        "sportId_3_20": "出局",
        "sportId_3_21": "换人",
        "sportId_4_1": "全场比分",
        "sportId_4_2": "射门比分",
        "sportId_4_3": "大罚比分",
        "sportId_4_4": "小罚比分",
        "sportId_4_5": "以多打少",
        "sportId_4_6": "以少打多",
        "sportId_4_7": "进球",
        "sportId_4_8": "进球取消",
        "sportId_4_9": "罚停",
        "sportId_5_1": "盘比分",
        "sportId_5_2": "局比分",
        "sportId_5_3": "当前局比分",
        "sportId_5_4": "发球得分次数",
        "sportId_5_5": "发球失败次数",
        "sportId_5_6": "破发成功次数",
        "sportId_5_7": "破发点",
        "sportId_5_8": "破发率",
        "sportId_5_9": "保发",
        "sportId_5_10": "得分",
        "sportId_5_11": "双发失误",
        "sportId_5_14": "ACE发球",
        "sportId_5_15": "破发",
        "sportId_6_1": "比分",
        "sportId_6_2": "冲球数",
        "sportId_6_3": "射门比分",
        "sportId_6_4": "进攻比分",
        "sportId_6_5": "达阵比分",
        "sportId_6_6": "达阵",
        "sportId_6_7": "附加分",
        "sportId_6_8": "安全分",
        "sportId_6_9": "2分转换",
        "sportId_6_10": "射门",
        "sportId_6_11": "判罚",
        "sportId_6_12": "教练对抗",
        "sportId_7_1": "盘比分",
        "sportId_7_2": "局比分",
        "sportId_7_3": "犯规次数",
        "sportId_7_4": "单杆最高",
        "sportId_7_5": "当前杆得分",
        "sportId_7_6": "得分",
        "sportId_7_7": "犯规",
        "sportId_8_1": "盘比分",
        "sportId_8_2": "局比分",
        "sportId_8_3": "红牌",
        "sportId_8_4": "黄牌",
        "sportId_9_1": "全场比分",
        "sportId_9_2": "局比分",
        "sportId_9_3": "发球比分",
        "sportId_9_4": "发球失误次数",
        "sportId_10_1": "盘比分",
        "sportId_10_2": "局比分",
        "sportId_10_3": "得分次数",
        "sportId_10_4": "接收点得分次数",
        "sportId_10_5": "发球得分次数",
        "sportId_10_6": "黑牌"
    },
    "meatdata_operation": {
        "operation1": "重置",
        "operation2": "关联联赛",
        "operation3": "取消关联",
        "operation4": "生成联赛ID",
        "operation5": "生成球队ID",
        "operation6": "生成球员ID",
        "operation7": "编辑管理",
        "operation8": "关联球队",
        "operation9": "关联球员",
        "operation10": "已设为主体",
        "operation11": "设为关联主体",
        "operation12": "联赛主体设置完成",
        "operation13": "请先设置联赛主体",
        "operation14": "联赛归属",
        "operation15": "选择球队",
    },
    "matchPeriodId": {
        "id_1_OT": "加时上半场",
        "id_1_OT1": "加时下半场",
        "id_1_": "全部",
        "id_1_0": "未开赛",
        "id_1_6": "上半场",
        "id_1_7": "下半场",
        "id_1_31": "中场休息",
        "id_1_32": "即将加时",
        "id_1_100": "常规时间结束",
        "id_1_41": "加时赛上半场",
        "id_1_33": "加时中场休息",
        "id_1_42": "加时赛下半场",
        "id_1_110": "加时下半场结束",
        "id_1_34": "等待点球大战",
        "id_1_50": "点球大战",
        "id_1_120": "点球大战结束",
        "id_1_80": "比赛中断",
        "id_1_90": "比赛放弃",
        "id_1_999": "比赛结束",
        "id_2_": "全部",
        "id_2_0": "未开赛",
        "id_2_13": "第一节",
        "id_2_301": "第一节结束",
        "id_2_14": "第二节",
        "id_2_302": "第二节结束",
        "id_2_15": "第三节",
        "id_2_303": "第三节结束",
        "id_2_16": "第四节",
        "id_2_100": "第四节结束",
        "id_2_32": "等待加时",
        "id_2_40": "加时赛",
        "id_2_110": "加时赛结束",
        "id_2_61": "比赛推迟",
        "id_2_80": "比赛中断",
        "id_2_90": "比赛放弃",
        "id_2_1": "上半场",
        "id_2_2": "下半场",
        "id_2_31": "上半场结束",
        "id_2_999": "比赛结束",
        "id_5_": "全部",
        "id_5_0": "未开赛",
        "id_5_8": "第一盘",
        "id_5_9": "第二盘",
        "id_5_10": "第三盘",
        "id_5_11": "第四盘",
        "id_5_12": "第五盘",
        "id_5_100": "全场结束",
        "id_5_301": "第一盘结束",
        "id_5_302": "第二盘结束",
        "id_5_303": "第三盘结束",
        "id_5_304": "第四盘结束",
        "id_5_305": "第五盘结束",
        "id_5_93": "客队球员未参赛",
        "id_5_94": "主队球员未参赛",
        "id_5_95": "主队球员退赛",
        "id_5_96": "客队球员退赛",
        "id_5_97": "主队球员未到场",
        "id_5_98": "客队球员未到场",
        "id_5_800": "第一盘局结束",
        "id_5_900": "第二盘局结束",
        "id_5_1000": "第三盘局结束",
        "id_5_1100": "第四盘局结束",
        "id_5_1200": "第五盘局结束",
        "id_5_61": "比赛推迟",
        "id_5_80": "比赛中断",
        "id_5_90": "比赛取消",
        "id_5_999": "比赛结束",
        "id_3_": "全部",
        "id_3_0": "未开赛",
        "id_3_401": "第一局上",
        "id_3_421": "第一局中场休息",
        "id_3_402": "第一局下",
        "id_3_422": "第一，二局局间休息",
        "id_3_403": "第二局上",
        "id_3_423": "第二局中场休息",
        "id_3_404": "第二局下",
        "id_3_424": "第二，三局局间休息",
        "id_3_405": "第三局上",
        "id_3_425": "第三局中场休息",
        "id_3_406": "第三局下",
        "id_3_426": "第三，四局局间休息",
        "id_3_407": "第四局上",
        "id_3_427": "第四局中场休息",
        "id_3_408": "第四局下",
        "id_3_428": "第四，五局局间休息",
        "id_3_409": "第五局上",
        "id_3_429": "第五局中场休息",
        "id_3_410": "第五局下",
        "id_3_430": "第五，六局局间休息",
        "id_3_411": "第六局上",
        "id_3_431": "第六局中场休息",
        "id_3_412": "第六局下",
        "id_3_432": "第六，七局局间休息",
        "id_3_413": "第七局上",
        "id_3_433": "第七局中场休息",
        "id_3_414": "第七局下",
        "id_3_434": "第七，八局局间休息",
        "id_3_415": "第八局上",
        "id_3_435": "第八局中场休息",
        "id_3_416": "第八局下",
        "id_3_436": "第八，九局局间休息",
        "id_3_417": "第九局上",
        "id_3_437": "第九局中场休息",
        "id_3_418": "第九局下",
        "id_3_438": "等待加时赛",
        "id_3_43820": "等待加时赛",
        "id_3_419": "加时上",
        "id_3_439": "加时赛中场休息",
        "id_3_420": "加时下",
        "id_3_41910": "第十局上",
        "id_3_43910": "第十局中场休息",
        "id_3_42010": "第十局下",
        "id_3_43810": "第十，十一局局间休息",
        "id_3_41911": "第十一局上",
        "id_3_43911": "第十一局中场休息",
        "id_3_42011": "第十一局下",
        "id_3_43811": "第十一，十二局局间休息",
        "id_3_41912": "第十二局上",
        "id_3_43912": "第十二局中场休息",
        "id_3_42012": "第十二局下",
        "id_3_43812": "第十二，十三局局间休息",
        "id_3_41913": "第十三局上",
        "id_3_43913": "第十三局中场休息",
        "id_3_42013": "第十三局下",
        "id_3_43813": "第十三，十四局局间休息",
        "id_3_41914": "第十四局上",
        "id_3_43914": "第十四局中场休息",
        "id_3_42014": "第十四局下",
        "id_3_43814": "第十四，十五局局间休息",
        "id_3_41915": "第十五局上",
        "id_3_43915": "第十五局中场休息",
        "id_3_42015": "第十五局下",
        "id_3_43815": "第十五，十六局局间休息",
        "id_3_41916": "第十六局上",
        "id_3_43916": "第十六局中场休息",
        "id_3_42016": "第十六局下",
        "id_3_43816": "第十六，十七局局间休息",
        "id_3_41917": "第十七局上",
        "id_3_43917": "第十七局中场休息",
        "id_3_42017": "第十七局下",
        "id_3_43817": "第十七，十八局局间休息",
        "id_3_41918": "第十八局上",
        "id_3_43918": "第十八局中场休息",
        "id_3_42018": "第十八局下",
        "id_3_43818": "第十八，十九局局间休息",
        "id_3_41919": "第十九局上",
        "id_3_43919": "第十九局中场休息",
        "id_3_42019": "第十九局下",
        "id_3_43819": "第十九，二十局局间休息",
        "id_3_41920": "第二十局上",
        "id_3_43920": "第二十局中场休息",
        "id_3_42020": "第二十局下",
        "id_3_100": "赛事结束",
        "id_3_80": "比赛中断",
        "id_3_90": "比赛放弃",
        "id_3_999": "比赛结束",
        "id_9_": "全部",
        "id_9_0": "未开赛",
        "id_9_8": "第一局",
        "id_9_301": "第一局休息",
        "id_9_9": "第二局",
        "id_9_302": "第二局休息",
        "id_9_10": "第三局",
        "id_9_303": "第三局休息",
        "id_9_11": "第四局",
        "id_9_304": "第四局休息",
        "id_9_12": "第五局",
        "id_9_305": "第五局休息",
        "id_9_441": "第六局",
        "id_9_306": "第六局休息",
        "id_9_442": "第七局",
        "id_9_100": "全场结束",
        "id_9_17": "发放金牌",
        "id_9_37": "等待发放金牌",
        "id_9_130": "发放金牌结束",
        "id_9_93": "主队走场获胜",
        "id_9_94": "客队走场获胜",
        "id_9_95": "客队获胜，主队退赛",
        "id_9_96": "主队获胜，客队退赛",
        "id_9_61": "比赛推迟",
        "id_9_80": "比赛中断",
        "id_9_90": "比赛放弃",
        "id_9_999": "比赛结束",
        "id_8_": "全部",
        "id_8_0": "未开赛",
        "id_8_8": "第一局",
        "id_8_301": "第一局休息",
        "id_8_9": "第二局",
        "id_8_302": "第二局休息",
        "id_8_10": "第三局",
        "id_8_303": "第三局休息",
        "id_8_11": "第四局",
        "id_8_304": "第四局休息",
        "id_8_12": "第五局",
        "id_8_305": "第五局休息",
        "id_8_441": "第六局",
        "id_8_306": "第六局休息",
        "id_8_442": "第七局",
        "id_8_100": "全场结束",
        "id_8_94": "主队球员未参赛",
        "id_8_93": "客队球员未参赛",
        "id_8_95": "主队球员退赛",
        "id_8_96": "客队球员退赛",
        "id_8_97": "主队球员未到场",
        "id_8_98": "客队球员未到场",
        "id_8_61": "比赛推迟",
        "id_8_80": "比赛中断",
        "id_8_90": "比赛取消",
        "id_8_999": "比赛结束",
        "id_7_": "全部",
        "id_7_0": "未开赛",
        "id_7_21": "进行中",
        "id_7_30": "暂停",
        "id_7_445": "局间休息",
        "id_7_100": "全场结束",
        "id_7_94": "主队球员未参赛",
        "id_7_93": "客队球员未参赛",
        "id_7_95": "主队球员退赛",
        "id_7_96": "客队球员退赛",
        "id_7_97": "主队弃权",
        "id_7_98": "客队弃权",
        "id_7_61": "比赛推迟",
        "id_7_80": "比赛中断",
        "id_7_90": "比赛取消",
        "id_7_999": "比赛结束",
        "id_4_": "全部",
        "id_4_0": "未开赛",
        "id_4_1": "第一节",
        "id_4_301": "第一节休息",
        "id_4_2": "第二节",
        "id_4_302": "第二节休息",
        "id_4_3": "第三节",
        "id_4_100": "全场结束",
        "id_4_32": "等待加时",
        "id_4_40": "加时赛",
        "id_4_110": "加时赛结束",
        "id_4_34": "等待点球",
        "id_4_50": "点球大战",
        "id_4_120": "点球大战结束",
        "id_4_80": "比赛中断",
        "id_4_90": "比赛放弃",
        "id_4_999": "比赛结束",
        "id_10_": "全部",
        "id_10_0": "未开赛",
        "id_10_8": "SET1",
        "id_10_301": "第1局结束",
        "id_10_9": "SET2",
        "id_10_302": "第2局结束",
        "id_10_10": "SET3",
        "id_10_303": "第3局结束",
        "id_10_11": "SET4",
        "id_10_304": "第4局结束",
        "id_10_12": "SET5",
        "id_10_100": "全场结束",
        "id_10_94": "主队球员未参赛",
        "id_10_93": "客队球员未参赛",
        "id_10_95": "主队球员退赛",
        "id_10_96": "客队球员退赛",
        "id_10_97": "主队球员未到场",
        "id_10_98": "客队球员未到场",
        "id_10_61": "比赛推迟",
        "id_10_80": "比赛中断",
        "id_10_90": "比赛取消",
        "id_10_999": "比赛结束",
        "id_6_": "全部",
        "id_6_0": "未开赛",
        "id_6_13": "第一节",
        "id_6_301": "第一节休息",
        "id_6_14": "第二节",
        "id_6_302": "第二节休息",
        "id_6_15": "第三节",
        "id_6_303": "第三节休息",
        "id_6_16": "第四节",
        "id_6_100": "全场结束",
        "id_6_32": "等待加时",
        "id_6_40": "加时赛",
        "id_6_110": "加时赛结束",
        "id_6_80": "比赛中断",
        "id_6_90": "比赛放弃",
        "id_6_999": "比赛结束",
        "matchPeriodId14_60": "比赛暂停",
        "matchPeriodId14_443": "等待加时赛开始",
        "matchPeriodId14_440": "加时赛",
        "matchPeriodId14_444": "加时赛结束",
    },
    "ball3": {
        "w1": "第一局上",
        "w2": "第一局下",
        "w3": "第二局上",
        "w4": "第二局下",
        "w5": "第三局上",
        "w6": "第三局下",
        "w7": "第四局上",
        "w8": "第四局下",
        "w9": "第五局上",
        "w10": "第五局下",
        "w11": "第六局上",
        "w12": "第六局下",
        "w13": "第七局上",
        "w14": "第七局下",
        "w15": "第八局上",
        "w16": "第八局下",
        "w17": "第九局上",
        "w18": "第九局下",
        "w19": "第十局上",
        "w20": "第十局下",
        "w21": "第十一局上",
        "w22": "第十一局下",
        "w23": "第十二局上",
        "w24": "第十二局下",
        "w25": "第十三局上",
        "w26": "第十三局下",
        "w27": "第十四局上",
        "w28": "第十四局下",
        "w29": "第十五局上",
        "w30": "第十五局下",
        "w31": "第十六局上",
        "w32": "第十六局下",
        "w33": "第十七局上",
        "w34": "第十七局下",
        "w35": "第十八局上",
        "w36": "第十八局下",
        "w37": "第十九局上",
        "w38": "第十九局下",
        "w39": "第二十局上",
        "w40": "第二十局下"
    },
    "league_levels": {
        "all_levels": "全部级别",
        "level_": "全部",
        "level_0": "未评级",
        "level_1": "一级联赛",
        "level_2": "二级联赛",
        "level_3": "三级联赛",
        "level_4": "四级联赛",
        "level_5": "五级联赛",
        "level_6": "六级联赛",
        "level_7": "七级联赛",
        "level_8": "八级联赛",
        "level_9": "九级联赛",
        "level_10": "十级联赛",
        "level_11": "十一级联赛",
        "level_12": "十二级联赛",
        "level_13": "十三级联赛",
        "level_14": "十四级联赛",
        "level_15": "十五级联赛",
        "level_16": "十六级联赛",
        "level_17": "十七级联赛",
        "level_18": "十八级联赛",
        "level_19": "十九级联赛",
        "level_20": "二十级联赛"
    },
    "sport_team_type": {
        "type_": "全部",
        "type_1": "男子团体",
        "type_2": "男子单打",
        "type_3": "女子单打",
        "type_4": "男子双打",
        "type_5": "女子双打",
        "type_6": "混合双打",
        "type_7": "其他",
        "type_8": "女子团体",
        "type_9": "电子竞技"
    },
    "manage": {
        "all_league": "全部联赛",
        "search_1": "更多筛选条件",
        "search_2": "联赛名/球队名/赛事ID",
        "search_21": "联赛名",
        "search_22": "球队名",
        "search_23": "赛事ID",
        "search_3": "数据来源",
        "search_4": "已匹配",
        "search_5": "未匹配",
        "search_6": "赛事阶段",
        "search_7": "赛事状态",
        "search_8": "起始时间",
        "search_9": "结束时间",
        "search_10": "重置",
        "search_11": "确定",
        "search_12": "清空",
        "search_13": "全部",
        "search_14": "部分",
        "search_15": "是否展示锁",
        "search_19": "电子赛事",
        "search_20": "非电子赛事",
        "search_16": "轮次",
        "search_17": "轮次全部时间",
        "search_18": "输入轮次",
        "RobotRecommendation": "机器人推荐",
        "search_19": "开赛时间变更",
        "to_day": "今日",
        "tor_day": "明日",
        "league": "联赛",
        "today": "今日",
        "homeTeamNames": "主队名称",
        "homeTeamPlayer": "主队选手",
        "score": "比分",
        "awayTeamNames": "客队名称",
        "awayTeamPlayer": "客队选手",
        "neutralGround": "中立场",
        "dataSourceCode": "数据商",
        "relatedMatch": "竞品数据",
        "matchManageId": "赛事ID",
        "inPlay": "滚球",
        "action": "操作",
        "leagueNameEn": "联赛名称英文",
        "homeTeamNamesEn": "主队名称英文",
        "awayTeamNamesEn": "客队名称英文",
        "text_1": "可移入预开售",
        "text_2": "新推送赛事",
        "text_3": "全选",
        "text_4": "反选",
        "text_5": "创建赛事",
        "text_6": "审核通过",
        "text_7": "不通过",
        "text_8": "返回赛程列表",
        "text_9": "审核筛选",
        "text_10": "已审核",
        "text_11": "未审核",
        "text_12": "电竞已订阅",
        "text_13": "电竞未订阅",
        "modal_tips_1": "主客队名称相同队伍已在预开售",
        "modal_tips_2": "是否确认移入预开售？",
    },
    "round_type": {
        "type_1": "三盘二胜制",
        "type_2": "五盘三胜制",
        "type_3": "三局二胜制",
        "type_4": "五局三胜制",
        "type_5": "七局四胜制",
        "type_6": "四局三胜制",
    },
    "robot": {
        "text_1": "审核总计：",
        "text_2": "已审核：",
        "text_3": "完全通过率：",
        "text_4": "部分通过率：",
        "text_5": "匹配池总计：",
        "text_6": "已匹配：",
        "text_7": "百分百匹配率：",
        "text_8": "SR匹配率：",
        "text_9": "BC匹配率：",
        "text_10": "BG匹配率：",
        "text_11": "188匹配率：",
        "text_12": "SBA匹配率：",
        "text_13": "球探匹配率：",
        "text_14": "SBO匹配率：",
        "text_15": "商业数据源匹配率：",
        "text_16": "该操作将作为机器推荐的结果依据，请确定审核无误",
    },
    "selectPeriodTab": {
        "Tab_1": "月",
        "Tab_2": "日",
        "Tab_3": "滚球",
        "Tab_4": "今日",
        "Tab_5": "明日",
        "Tab_6": "其他早盘",
        "Tab_7": "历史赛程",
        "Tab_8": "进行中",
    },
    "pagination": {
        "total": "总数",
        "article": "条",
        "page": "页",
        "Jump": "跳转至",
        "itemPage": "条/页",
        "record": "条记录",
        "common": "共"
    },
    "right_info": {
        "right_tab1": "对阵信息",
        "right_tab2": "实时事件",
        "language_1": "中文简体",
        "language_2": "简称",
        "language_3": "中文繁体",
        "language_4": "英语",
        "language_5": "西班牙语",
        "language_6": "意大利语",
        "language_7": "德语",
        "language_8": "法语",
        "language_9": "葡萄牙语",
        "language_10": "俄语",
        "language_11": "日语",
        "language_12": "韩语",
        "language_13": "泰语",
        "language_14": "越南语",
        "language_15": "缅甸语",
        "language_16": "阿拉伯语",
        "language_17": "印地语",
        "language_3_1": "中文繁体-简称",
        "language_yc": "全称",
        "language_id": "语言ID",
        "edit_1": "关联球队",
        "edit_2": "赛季资料",

        "type_1": "联赛",
        "type_2": "主队",
        "type_3": "客队",
        "type_4": "球队",
        "type_5": "球员",
        "type_6": "三方球队"
    },
    "match_edit": {
        "edit_title1": "对阵管理",
        "edit_title2": "操作日志",
        "edit_title3": "重置",
        "edit_title4": "保存并应用",
        "edit_title5": "为必填项。",
        "edit_1": "账务日期",
        "edit_2": "比赛时间",
        "edit_3": "联赛名称",
        "edit_4": "联赛Logo",
        "edit_5": "对调",
        "edit_6": "上传",
        "edit_7": "联赛级别",
        "edit_8": "联赛区域",
        "edit_9": "比赛赛制",
        "edit_10": "类型",
        "edit_11": "中立场",
        "edit_12": "联赛官网",
        "edit_13": "联赛名",
        "edit_14": "主队",
        "edit_15": "客队",
        "edit_16": "开赛时间",
        "edit_17": "语言",
        "edit_18": "编辑历史",
        "edit_19": "更多",
        "edit_20": "比赛场地",
        "edit_21": "场地类型",
        "log_1": "操作日志",
        "log_2": "操作模块",
        "log_3": "操作内容",
        "log_4": "操作员",
        "log_5": "操作时间",
        "log_6": "备注",
        "log_7": "添加",
        "log_8": "请输入备注内容",
        "edit_22": "编辑管理",
        "edit_23": "联赛ID",
        "edit_24": "上传联赛Logo",
        "edit_25": "联赛",
        "edit_26": "球队ID",
        "edit_27": "球队Logo",
        "edit_28": "球队",
        "edit_29": "球队名称",
        "edit_30": "球员ID",
        "edit_31": "上传球员Logo",
        "edit_32": "球员",
        "edit_33": "球员名称",
        "edit_34": "球员姓名",
        "edit_35": "个人资料",
        "edit_36": "基础信息",
        "edit_37": "国籍",
        "edit_38": "出生日期",
        "edit_39": "性别",
        "edit_40": "身高",
        "edit_41": "球队对阵",
        "edit_42": "联赛临时名称设置",
        "edit_43": "体重",
        "edit_44": "请选择联赛",
        "edit_45": "变更联赛",
        "edit_46": "赛季",
        "edit_47": "轮次",
        "edit_48": "赛季开始时间",
        "edit_49": "赛季结束时间",
        "edit_51": "取消对调",
        "edit_52": "男",
        "edit_53": "女",
        "edit_54": "竞彩编号",
        "edit_55": "请输入竞彩编号",
        "edit_56": "请选择联赛区域",
        "edit_57": "请输入联赛官网",
        "edit_58": "注：可点击此按钮创建新球队",
        "edit_59": "请输入联赛名称或联赛ID",
        "edit_60": "请选择主队",
        "edit_61": "请选择客队",
        "edit_62": "请选择比赛赛制",
        "edit_63": "请选择中立场",
        "edit_64": "黑名单",
        "edit_65": "黑名单球队",
        "edit_66": "继续编辑",
        "edit_67": "稍后编辑",
        "edit_68": "编辑中",
        "edit_69": "本页面有其他人正在编辑，你编辑的数据有丢失的可能",
        "edit_70": "一键清除",
        "edit_71": "请输入轮次",
        "edit_72": "赛程类型",
        "edit_73": "晋级名额",
        "edit_74": "比赛周期",
        "edit_75": "本赛季",
        "edit_76": "历史赛季",
        "edit_77": "开始日期",
        "edit_78": "结束日期",
        "edit_79": "杯赛",
        "edit_80": "夏令时",
        "edit_81": "冬令时",
        "edit_82": "夏/冬令时",
        "edit_83": "暂无赛季资料",
        "edit_84": "降级名额",
        "edit_85": "联赛信息",
        "edit_86": "自建联赛",
        "edit_87": "三方球队名称",
        "edit_88": "尺寸: 72 x 72 px",
        "edit_89": "大小: Max 10 Mb",
        "check_1": "账务日期不可为空",
        "check_2": "比赛时间不可为空",
        "check_3": "联赛名称不可为空",
        "check_4": "联赛级别不可为空",
        "check_5": "联赛区域不可为空",
        "check_6": "比赛赛制不可为空",
        "check_7": "中立场不可为空",
        "check_8": "球队类型不可为空",
        "check_9": "主队名称不可为空",
        "check_10": "客队名称不可为空",
        "format_select": "赛制选择",

        "round_32": "32强赛",
        "round_16": "16强赛",
        "quarter_finals": "8强赛",
        "semi_finals": "半决赛",
        "final": "决赛",
        "group_no_empty": "组别不能为空",
    },
    "match_create_league": {
        "create_title1": "常规联赛",
        "create_title2": "电子赛事",
        "create_title3": "自建联赛",
        "create_title4": "早盘规则参数模版",
        "create_title5": "滚球规则参数模版",
        "create_title6": "晋级名额	",
        "create_title7": "降级名额",
        "create_title8": "联赛名称",
        "create_title9": "语言ID",
        "create_title10": "语言",
        "create_title11": "全称",
        "create_title12": "简称",

        "sj_placeholder": "输入前几名球队晋级",
        "jj_placeholder": "输入后几名球队降级",

        "sc_type_placeholder": "输入总轮次",


    },
    "match_match_cycle": {
        "match_cycle_title1": "比赛周期",
        "match_cycle_title2": "其他周期",
        "other_type_year": "年",
        "other_type_month": "月",
        "match_cycle_type1": "无赛季时程",
        "match_cycle_type2": "每年度",
        "match_cycle_type3": "跨年度",
        "match_cycle_type4": "四年度",
        "match_cycle_type5": "其他",

        "match_cycle_message1": "比赛周期不可为空",
        "match_cycle_message2": "比赛周期为其他时，其他周期不可为空",
        "match_cycle_message3": "请选择其他周期类型",


    },
    "match_create_season": {
        "season_title1": "赛季资料",
        "season_title2": "本赛季",
        "season_title3": "历史赛季",
        "season_title4": "赛季名称",
        "season_title5": "开始日期",
        "season_title6": "结束日期",
        "season_title7": "参赛队伍数",
        "season_title8": "支持冠军",
        "season_title9": "操作",
        "season_title10": "编辑",
        "season_title11": "关联球队",
        "season_title12": "删除",
        "season_title13": "联赛ID",
        "season_title14": "联赛名称	",
        "season_title15": "赛季年份	",
        "season_title16": "自动带入近一期球队资料",
        "season_title17": "比赛周期	",
        "season_title18": "日期选择	",
        "season_title19": "开始时间	",
        "season_title20": "结束时间	",
        "season_title21": "新增赛季",
        "season_title22": "编辑赛季	",
        "season_title23": "删除赛季	",
        "season_title24": "联赛资讯",
        "season_title25": "赛季资讯",
        "season_message1": "赛季年份不可为空",
        "season_message2": "请选择日期",



    },
    "match_season_date_selection": {
        "type1": "无",
        "type2": "年月日时分 (UTC+8)",
        "type3": "年月日 (UTC+8)",
        "type4": "年月",
        "type5": "年",

    },
    "match_team_jersey": {
        "jersey": '球衣',
        "add_jersey": '新增球衣',
        "edit_jersey": '编辑球衣',
        "jersey_name": '球衣名称',
        "season_schedule": "赛季时程",
        "jersey_type": "球衣类型",
        "jersey_img": "球衣图片",
        "jersey_img_size": "限制尺寸",
        "jersey_img_max": "大小",
        "jersey_style": "球衣样式",
        "color_style": "彩色样式",
        "color_0": "底色",
        "color_1": "领口色",
        "color_2": "袖口色",
        "color_3": "衣缝色",
        "color_4": "条纹色",
        "style": "样式",
        "jersey_name_not_null": '球衣名称不能为空',
        "jersey_default_not_null": '请选择球衣默认使用场合',
        "jersey_type_not_null": '请选择球衣类型',
        "jersey_img_not_null": '请上传球衣图片',
        "jersey_style_not_null": '请设置自定义球衣样式',
        "season_schedule_not_null": '请选择赛季时程',
        "jersey_module": "球衣资讯",
        "Custom_style": '自定义样式',
        "Upload_img": "上传图片"

    },
    "match_team": {
        "link_league_details": "关联联赛详情",
        "team_name": '球队名称',
        'unlinking': "取消关联",
        "full_name": "全称",
        "regions": "区域",
        "number": "参赛队伍数",
        "log": "操作日志",
        "is_it_a_club": "是否为俱乐部",
        "deactivation": "是否停用",
        "match_status": "匹配状态",
        "match": "匹配完成",
        "no_match": "待匹配",
        "language_jc": "简称"


    },

    "match_team_league_info": {
        "league_id": "联赛ID",
        "simplified": "中文简体",
        "english": "英文",
        "season_schedule": "賽季时程",
        "actions": "操作"
    },
    "meta_data": {
        "left_pla_1": "输入联赛ID或联赛名称",
        "left_pla_2": "输入球队ID或球队名称",
        "left_pla_3": "输入球员ID或球员名称",
        "left_pla_4": "输入关联球队ID",
        "left_pla_5": "输入三方球队ID或三方球队名称",
        "left_1": "联赛等级",
        "left_2": "区域",
        "left_3": "联赛ID状态",
        "left_4": "有联赛ID",
        "left_5": "无联赛ID",
        "left_6": "重置",
        "left_7": "确定",
        "left_10": "搜索",
        "left_8": "球队ID状态",
        "left_9": "球员ID状态",
        "left_id1": "有联赛ID",
        "left_id2": "无联赛ID",
        "left_id3": "有球队ID",
        "left_id4": "无球队ID",
        "left_id5": "有球员ID",
        "left_id6": "无球员ID",
        "head_1": "联赛ID",
        "tournament_source_id": "三方联赛ID",
        "head_2": "联赛等级",
        "head_3": "区域",
        "head_4": "中文简体",
        "head_5": "简称",
        "head_6": "英文名称",
        "head_7": "数据商",
        "head_8": "操作",
        "head_9": "球队ID",
        "head_10": "球员ID",
        "head_11": "类型",
        "head_12": "父联赛",
        "head_13": "创建联赛",
        "head_14": "联赛创建联赛",
        "head_15": "创建-联赛",
        "head_16": "创建球队",
        "head_17": "停用",
        "head_18": "恢复",
        "head_19": "三方球员ID",
        "head_20": "所属球队",
        "head_21": "请输入三方球员ID",
        "head_22": "常规球队",
        "head_23": "关联列表",
        "head_24": "俱乐部",
        "head_25": "球员总数",
        "head_26": "三方球队ID",
        "head_27": "关联球队库",
        "head_28": "关联球队ID",
        "head_29": "匹配状态",
        "head_30": "是否停用",
        "head_31": "关联状态",
        "head_32": "参赛队伍数",
        "head_33": "联赛信息",
        "head_34": "电子球队",
        "head_35": "球队名称(cn)",
        "head_36": "球队名称(en)",
    },
    "stats_rep": {
        "tab_1": "赛事数量",
        "tab_2": "赛程融合",
        "tab_3": "赛事覆盖",
        "head_1": "Panda总计",
        "head_2": "昨日",
        "head_3": "近7日",
        "head_4": "近30日",
        "head_5": "日期",
        "head_6": "赛程",
        "head_7": "滚球",
        "head_8": "融合统计",
        "head_9": "日期时间",
        "head_10": "联赛",
        "head_11": "主场球队",
        "head_12": "客场球队",
        "left_1": "查找日期",
        "left_2": "赛种"
    },
    "op_log": {
        "tab_1": "今日",
        "tab_2": "昨日",
        "head_1": "序号",
        "head_2": "操作账号",
        "head_3": "资源名称",
        "head_4": "操作时间",
        "head_5": "是否成功",
        "head_6": "操作类型",
        "head_7": "唯一ID",
        "head_8": "操作之前",
        "head_9": "操作之后",
        "head_10": "备注",
        "left_1": "输入操作账号/登录编号",
        "left_2": "资源名称",
        "left_3": "操作类型",
        "left_4": "操作开始时间",
        "left_5": "操作结束时间",
        "right_1": "日志详情",
        "right_2": "操作编号",
        "right_3": "登录编号",
        "right_4": "操作账号",
        "key_1": "sysModel",
        "key_2": "event",
        "alert_1": "操作日志数据为空请确认后再导出",
        "alert_2": "开始时间不能为空",
        "alert_3": "结束时间不能为空",
        "alert_4": "开始时间不能大于结束时间",
        "alert_5": "时间范围不能超过一个月",
    },
    "v_sports": {
        "head_1": "英文名(数据商)",
        "head_2": "联赛排序"
    },
    "olympic": {
        "year": "年份",
        "sort": "排序",
        "olympic_project": "奥运项目",
        "history": "历史",
        "olympic_project_name": "奥运项目名称",
        "logo": "LOGO",
        "project_introduce": "项目介绍",
        "rules_summary": "规则概要",
        "olympic_history": "奥林匹克历史",
        "olympic_project_association_home": "奥运项目关联到首页",
        "state": "状态",
        "ranking": "排名",
        "team": "队伍",
        "detailed": "详情",
        "gold_medal": "金牌",
        "silver_medal": "银牌",
        "bronz_medal": "铜牌",
        "total": "总数",
        "single_operation": "单项操作",
        "operation": "操作",
        "upload_image": "上传图片",
        "add_one_piece": "（可添加1张）",
        "max_img_volume": "图片不能超过10KB",
        "support": "支持",
        "add_year": "新增年份",
        "select_year": "请选择年份",
        "olympic_name": "奥运名称",
        "olympic_name_zh_no_empty": "奥运项目中文名称不能为空",
        "olympic_name_en_no_empty": "奥运项目英文名称不能为空",
        "select_enter_olympic_zh_name": "请选择或者输入中文奥运名称",
        "select_enter_olympic_en_name": "请选择或者输入英文奥运名称",
        "select_enter": "请选择或者输入",
        "select_enter_en_name": "请选择或者输入英文",
        "bing_init_data": "带入初始数据",
        "logo_no_empty": "LOGO不能为空",
        "year_no_empty": "年份不能为空",
        "olympic_name_no_empty": "奥运名称不能为空",
        "data_no_empty": "是否带入数据不能为空",
        "bind_champion_config": "绑定冠军玩法配置",
        "games_name": "玩法名称",
        "games_name_zh_no_empty": "中文玩法名称不能为空",
        "games_name_en_no_empty": "英文玩法名称不能为空",
        "please_enter": "请输入",
        "enter_games_name": "请输入中文",
        "zh_name_no_empty": "中文名称不能为空",
        "enter_en_games_name": "请输入英文",
        "enter_zh_games_name": "请输入中文",
        "en_name_no_empty": "英文名称不能为空",
        "bind_leagues_id": "绑定对应联赛ID",
        "bind_leagues_id_no_empty": "联赛ID不能为空",
        "enter_leagues_id": "请输入联赛ID",
        "handicap_id_no_empty": "盘口ID不能为空",
        "handicap_id": "统一盘口ID",
        "handicap_id1": "盘口ID",
        "enter_handicap_id": "请输入盘口ID",
        "handicap_name": "盘口名称",
        "handicap_type": "盘口类型",
        "delete_confirm": "删除确认",
        "sure_delete_information": "是否确认删除该配置信息",
        "add_team": "新增队伍",
        "details": "明细",
        "please_select": "请选择",
        "enter_amount": "请输入数量",
        "add": "新增",
        "confirm": "确认",
        "delet": "删除",
        "enable": "启用",
        "disable": "禁用",
        "is_change_enable": "是否更新该状态为【启用】？",
        "is_change_disable": "是否更新该状态为【停用】？",
        "add_olympic_project": "新增奥运项目",
        "olympic_project_name": "奥运项目名称",
        "project_introduce": "项目介绍",
        "enter_project_introduce": "请输入中文项目介绍",
        "enter_project_introduce_en": "请输入英文项目介绍",
        "project_introduce_zh_no_empty": "项目介绍中文名称不能为空",
        "project_introduce_en_no_empty": "项目介绍英文名称不能为空",
        "rules_summary": "规则概要",
        "enter_rules_summary": "请输入中文规则概要",
        "enter_rules_summary_en": "请输入英文规则概要",
        "rules_summary_zh_no_empty": "规则概要中文名称不能为空",
        "rules_summary_en_no_empty": "规则概要英文名称不能为空",
        "olympic_history": "奥林匹克历史",
        "enter_olympic_history": "请输入中文奥林匹克历史",
        "enter_olympic_history_en": "请输入英文奥林匹克历史",
        "olympic_history_zh_no_empty": "奥林匹克历史中文名称不能为空",
        "olympic_history_en_no_empty": "奥林匹克历史英文名称不能为空",
        "game_name": "玩法名称",
        "enter_game_name": "请输入中文玩法名称",
        "enter_game_name_en": "请输入英文玩法名称",
        "change_state": "更改状态",
        "search": "搜索",
        "add_medal_table": "新增奖牌榜",
        "medal_table": "奖牌榜"
    },
    "remediation_tools": {
        "tab": {
            "closing_process": "关盘处理",
            "modify_event_status": "修改赛事状态",
            "football_match_status": "处理足球赛事状态",
            "clear_cache_gameplay": "清除自动关盘缓存玩法",
            "modify_event_logo": "修改赛事滚球标识",
            "change_league": "变更标准联赛",
            "restart_event": "重开赛事",
            "change_championship_league": "变更冠军赛事联赛",
            "edit_information": "编辑球队信息",
            "change_team": "变更赛事球队",
            "l01_start_correction": "L01开始时间修正",
            "l01_league_update": "L01联赛更新",
            "odds_source_adjustment": "预开售早盘滚球历史赔率源调整",
            "event_state_clear": "赛事状态重置",
        },
        "chang_format_option": {
            "0": "0-2Hx45m(足球 90m) 10mx4Q(篮球40m)",
            "1": "1-2Hx20m(足球 40m)",
            "10": "10-2Hx15m(足球 30m)",
            "55": "55-2Hx3m(电子足球 6m)",
            "57": "57-2Hx2m(电子足球 4m)",
            "62": "62-2Hx6m(电子足球 12m)",
            "63": "63-2Hx5m(电子足球 10m)",
            "69": "69-2Hx10m(电子足球 20m)",
            "71": "71-2Hx4m(电子足球 8m)",
            "72": "72-2Hx15m(电子足球 30m)",
            "7": "7-12m x 4Q(篮球48m)",
            "17": "17-20m x 2H(篮球40m)",
            "68": "68-5m x 4Q(电子篮球20m)",
        },
        "operator": '操作人员',
        "operator_id": '操作人ID',
        "operator_id_not_empty": '操作人ID不能为空',
        "enter_operator_id": '操作人id，可编辑，生产可用：110',
        "code": "状态码",
        "return_msg": "返回信息",
        "handicap_source_id": "三方盘口源ID",
        "enter_handicap_source_id": "请输入三方盘口源ID",
        "handicap_source_id_not_empty": "三方盘口源ID不能为空",
        "standard_event_id": "标准赛事ID",
        "enter_standard_event_id": "请输入标准赛事ID",
        "standard_event_id_not_empty": "标准赛事ID不能为空",
        "event_manage_id": "赛事管理ID",
        "enter_event_manage_id": "请输入赛事管理ID",
        "event_manage_id_not_empty": "赛事管理ID不能为空",
        "standard_event_manage_id": "标准赛事管理ID",
        "enter_standard_event_manage_id": "请输入标准赛事管理ID",
        "standard_event_manage_id_not_empty": "标准赛事管理ID不能为空",
        "event_id": "赛事ID",
        "enter_event_id": "请输入赛事ID",
        "event_id_not_empty": "赛事ID不能为空",
        "team_manage_id": "球队管理ID",
        "team_id": "球队ID",
        "enter_team_manage_id": "请输入球队管理ID",
        "team_manage_id_not_empty": "球队管理ID不能为空",
        "shampionship_event_manage_id": "冠军赛事管理ID",
        "shampionship_event_manage_id_not_empty": "冠军赛事管理ID不能为空",
        "enter_shampionship_event_manage_id": "请输入冠军赛事管理ID",
        "league_manage_id": "联赛管理ID",
        "league_manage_id_not_empty": "联赛管理ID不能为空",
        "enter_league_manage_id": "请输入联赛管理ID",
        "event_status": "赛事状态",
        "standard_gameplay_id": "标准玩法ID",
        "gameplay_id": "玩法ID",
        "gameplay_name": "玩法名称",
        "enter_standard_gameplay_id": "请输入标准玩法ID",
        "standard_gameplay_id_not_empty": "标准玩法ID不能为空",
        "standard_manage_id": "赛事管理ID/标准赛事ID",
        "enter_standard_manage_id": "请输入赛事管理ID/标准赛事ID",
        "standard_manage_id_not_empty": "赛事管理ID/标准赛事ID不能为空",
        "reopening_time": "重开时间",
        "original_time": "原始时间",
        "update_time": "更正时间",
        "team_logos": "主客队标识",
        "team_home": "主场队",
        "team_away": "客场队",
        "data_sources_num": "关联数据源数量",
        "enter_data_sources_num": "请输入关联数据源数量",
        "data_sources_num_not_empty": "关联数据源数量不能为空",
        "data_sources_code_list": "关联数据源编码列表",
        "enter_data_sources_code_list": "请输入关联数据源编码列表",
        "data_sources_code_list_not_empty": "关联数据源编码列表不能为空",
        "data_sources_code": "数据来源编码",
        "enter_data_sources_code": "请输入数据来源编码",
        "data_sources_code_not_empty": "数据来源编码不能为空",
        "batch_processing": "批量处理，逗号隔开",
        "footerball_batch_processing": "针对足球，批量处理，逗号隔开",
        "source": "数据源",
        "odds_source": "赔率源",
        "enter_odds_source": "请输入赔率源",
        "odds_source_not_empty": "赔率源不能为空",
        "early": "早盘",
        "event_original_id": "三方赛事原始ID",
        "enter_event_original_id": "请输入三方赛事原始ID",
        "event_original_id_not_empty": "三方赛事原始ID不能为空",
        "early_or_rolling": "早盘或者滚球",
        "request_results": "请求结果",
        "single_time_limit": "单节时间限制",
        "enter_single_time_limit": "请输入限制时间",
        "single_time_limit_not_empty": "限制时间不能为空",
        "end_time_limit": "当前篮球即时结算的结束时间限制",
        "order_No": "订单号",
        "enter_order_No": "订单号，多个请使用,分割（每次最多200个订单号）",
        "enter_order_No2": "请输入订单号",
        "enter_order_No3": "订单号（单次只允许200单处理,多单请求,分割！）",
        "order_No_not_empty": "订单号不能为空",
        "confirm_close": "确定要紧急关盘处理吗？",
        "confirm_modify_league_id": "确定要修改联赛管理ID吗?",
        "confirm_modify_event_id": "确定要修改赛事球队ID吗?",
        "confirm_clear_cache": "确定要清除缓存吗?",
        "confirm_close_plate": "确定要紧急关盘处理吗？",
        "confirm_open_or_close_plate": "确定要紧急开/关盘吗?",
        "sure_send": "确定要下发吗?",
        "confirm_modify_team": "确定要修改球队吗?",
        "confirm_need_clear": "确定重制赛事状态到未开赛吗?",
        "confirm_modify_event_state": "确定要修改该赛事状态吗?",
        "confirm_submit": "确定要提交?",
        "confirm_change_event_logo": "确定要修改赛事滚球标识为赛前吗?",
        "confirm_adjust_odds_source": "确定要调整赔率源?",
        "confirm_modify_event_info": "确定要修改完赛赛事信息吗?",
        "confirm_correct_source": "确定要修正数据源吗?",
        "confirm_restart_game": "确定要重新开赛吗?",
        "confirm_use": "确定要使用吗?",
        "process_info": "处理信息",
        "confirm": "确认",
        "confirm_modify": "确认修正",
        "clear": "清除",
        "confirm_clear": "清理",
        "open_plate": "开盘",
        "close_plate": "关盘",
        "change_state": "更改的状态",
        "enter_request_result": "请输入请求结果",
        "request_result_not_empty": "请求结果不能为空",
        "set_start_time": "请设置开赛时间",
        "start_time": "比赛开始时间",
        "start_time_not_empty": "比赛开始时间不能为空",
        "standard_event_info": "标准赛事信息",
        "restart": "重新开赛",
        "modify_time": "修改时间",
        "modify_time_after": "修改后时间",
        "accounts_time": "账务时间",
        "event_status_source": "赛事状态源",
        "rolling_sales_status": "滚球开售状态",
        "rolling_sales_time": "滚球开售时间",
        "preMatch_sales_status": "赛前开售状态",
        "preMatch_sales_time": "赛前开售时间",
        "business_event_source_coding": "商业事件源编码",
        "is_support_rolling": "是否支持滚球",
        "rolling_data_service_provider": "滚球数据服务商",
        "preMatch_data_service_provider": "赛前数据服务商",
        "rolling_trading_platform": "滚球操盘平台",
        "preMatch_trading_platform": "赛前操盘平台",
        "sale_info": "开售信息",
        "rolling_odds_logos": "滚球赔率标识",
        "is_game_over": "比赛是否结束",
        "match_against_info": "赛事双方对阵信息",
        "competition_format1": "(网羽兵斯棒)赛制",
        "competition_format2": "(足篮冰美)赛制",
        "game_stage": "比赛阶段",
        "event_original_id": "第三方赛事原始ID",
        "data_encoding": "数据编码",
        "match_open_logos": "比赛开盘标识",
        "is_open_rolling": "是否开放滚球",
        "is_open_preMatch": "是否开放赛前盘",
        "game_time": "比赛进行时间",
        "third_party_contest_id": "第三方比赛ID",
        "standard_league_id": "标准联赛ID",
        "sports_types": "体育种类",
        "submit": "提交",
        "user": "用户名",
        "user_acount": "用户",
        "user_id": "用户id",
        "merchant": "商户",
        "odds": "赔率",
        "bet_amount": "投注金额(元)",
        "bet_amount1": "注单金额",
        "event_name": "赛事名称",
        "home_away_name": "主客队名称",
        "trader": "操盘方",
        "device_infor": "设备信息",
        "rejection_detail": "拒单详情",
        "rejection_time": "拒单时间",
        "rejection_reason": "拒单原因",
        "release_results": "订单一键结算-下发赛果",
        "release_results_tips": "【仅支持：普通、单串关、所有球种，一键下发赛果】",
        "event_number": "赛事编号",
        "beting_time": "下注时间",
        "sports_type_name": "运动种类名称",
        "event_type": "赛事类型",
        "bet_odds": "注单赔率",
        "final_handicap_type": "最终盘口类型",
        "handicap": "盘口值",
        "final_odds": "最终赔率",
        "is_auto_highest_odds": "是否自动接收最高赔率",
        "is_del": "是否删除",
        "json_format": "注单所有可能结果JSON格式",
        "settlement_event_stage_id": "结算赛事阶段id",
        "primary_secondary_logos": "主副盘标识",
        "preMatch_trading_source": "赛前操盘源",
        "rolling_trading_source": "赛前操盘源",
        "odds_source": "赔率数据源",
        "settlement_amount": "结算金额",
        "payout_status": "派彩状态",
        "settlement_type": "结算类型",
        "settlement_time": "结算时间",
        "creat_time": "创建时间",
        "manual_settlement": "手工结算",
        "auto_settlement": "自动结算",
        "settlement_rollback": "结算回滚",
        "settlement_rollback_again": "结算回滚之后再次结算（格式：3X，比如：31，再次结算第1次)",
        "bet_principal": "注单本金",
        "bet_principal1": "注单本金(欧洲赔率)",
        "net_profit": "净盈利",
        "original_net_profit": "原币净盈利",
        "billing_ip": "结算IP地址",
        "is_final_settlement_result": "是否为最终结算结果",
        "original_settlement_amount": "原币结算金额",
        "original_bet_amount": "原币注单本金",
        "current_account_balance": "当前账号余额",
        "change_amount": "变更金额",
        "change_type": "变更类型",
        "increase": "增加",
        "reduce": "减少",
        "account_change_type": "账户变更业务类型",
        "amount_before_transfer": "转帐前金额",
        "amount_after_transfer": "转帐后金额",
        "l01_roll_ball": "L01滚球(当出现滚球赔率延迟，或者断连时，请使用该功能！)",
        "l01_before_event": "L01赛前(当出现早盘赔率延迟，或者断连时，请使用该功能！)",
        "enter_leagues_name": "请输入联赛名称",
        "sports": "球种",
        "format_id": '赛制ID',
        "change_event_status": "更改为状态",
        "ordinary_event": "普通赛事",
        "change_time": "更改时间",
        "rolling_state_modify_stage": "滚球状态选择修改阶段",
        "open_0": "不开放",
        "open_1": "开放",
        "match_market_status": {
            "-1": "未开",
            "0": "开盘",
            "1": "封盘",
            "2": "关盘",
            "11": "锁盘",
            "12": "弃用",
            "13": "收盘"
        },
        "is_end": {
            "0": "是",
            "1": "否",
            "2": "临时状态",
        },
        "support_0": "不支持",
        "support_1": "支持",
        "Unsold": "未售",
        "Overdue_Unsold": "逾期未售",
        "Apply_Delay": "申请延期",
        "Sold": "开售",
        "Apply_Stop_Sold": "申请停售",
        "Stop_Sold": "停售",
        "Expected_End_Sold": "意外停售",
        "Cancel_Sold": "取消开售",
        "third_event_id": "三方盘口源ID/赛事ID",
        "open_close_process": "紧急开/关盘处理"
    },
    "against": {
        "modify_reason": "修改原因",
        "stage": "阶段",
        "group": "组别",
        "game_rounds": "比赛轮次",
        "group_stage": "小组赛",
        "select_begin_time": "请选择比赛时间",
        "select_change_reason": "请选择修改原因",
        "tip1": "延迟时间必须在原时间之后！",
        "tip2": "延迟时间必须与原时间是同一天！",
        "tip3": "延期时间必须在原时间之后！",
        "tip4": "延期时间不能与原时间是同一天！",
        "tip5": "提前时间必须在原时间之前！",
    },
    
  
  "match_resource_right_details": {
    
  
    "league_link": "联赛官网",
    "season_info": "赛季资料",
  },
    "match_resource": {

        "tab_normal": "常规联赛",
        "tab_related": "关联列表",
        "gen_league_id": "生成联赛ID",

        "tr_config_seasonNameJson": "赛季",


        "tr_config_tournamentManageId": "联赛ID",
        "tr_config_tournamentLevel": "联赛等级",

        "tr_config_thirdTournamentSourceId": "三方联赛ID",
        "tr_config_thirdTournamentSource": "三方联赛",

        "tr_config_match_type": "三方类型",
        "tr_config_match_type_relative": "关联联赛库",
        "tr_config_referenceId": "关联联赛ID",

        "tr_config_region_id": "联赛区域",
        "tr_config_en_name": "英文名称",
        "tr_config_zs_name": "简体名称",

        "action_league_unlink": "取消关联",
        "action_league_edit": "编辑管理",

        "status_relation_is_linked": "关联状态",
        "status_relation_linked": "已关联",
        "status_relation_unlinked": "未关联",

        "status_relation_match_statue": "匹配状态",
        "status_relation_is_match": "是否匹配",
        "status_relation_match": "已匹配",
        "status_relation_unmatch": "未匹配",
        "status_relation_pending": "待匹配",
        "status_relation_completed": "匹配完成",


        "status_operation_is_disable": "是否停用",
        "status_operation_status_enable": "启用",
        "status_operation_status_disable": "停用",

        "placeholder_enter_league_id": "输入联赛ID或联赛名称",
        "placeholder_enter_link_league_id": "输入关联联赛ID",



        "title_schedule_type": "赛程类型",
        "title_league_name": "联赛名称",
        "title_league_region": "联赛区域",
        "title_competition_format": "比赛赛制",
        "title_game_round": "总轮次",

        
        "input_trilateral_type_regular_season": "常规联赛",
        "input_trilateral_type_esports": "电子赛事",

        "input_title_data_source": "数据来源",

        "error_league_en_name_empty": "联赛英文名称不可为空",

        "error_league_zs_name_empty": "联赛中文名称不可为空",

        "error_need_unlink_id": "请选择需要取消关联的数据",

        "left_window_more_filter": "更多筛选条件",

        ///关联联赛弹框
        "dialog_associate_teams_dialog_title": "关联联赛",

        "dialog_associate_teams_dialog_league_id": "联赛ID",
        "dialog_associate_teams_dialog_league_type": "联赛类型",
        "dialog_associate_teams_dialog_league_name": "联赛名称",
        "dialog_associate_teams_dialog_league_region": "联赛区域",
        "dialog_associate_teams_dialog_league_error_multi_standard_id": "三方联赛只能关联在同一个标准，请先个别移除或重新选择",
     

        
        "dialog_associate_teams_dialog_league_type_regular": "常规联赛",
        "dialog_associate_teams_dialog_league_type_season": "电子联赛",

        "dialog_associate_teams_dialog_enter_league_manage_id": "请输入联赛管理ID",
        "dialog_associate_teams_dialog_league_not_found": "关联失败-找不到联赛",
        ///生成联赛ID弹框
        "dialog_gen_teams_dialog_league_type_missing": "请选择赛程类型",
        "dialog_gen_teams_dialog_league_length_missing": "请选择比赛赛制",   
        "dialog_gen_teams_dialog_league_round_missing": "请输入总轮次",



    }
}
