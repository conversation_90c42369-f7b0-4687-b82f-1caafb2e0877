import { onMounted, onUnmounted } from "vue"
import { compute_current_h_gmt_add_8 } from 'project/caopan/components/query/select_query_period/module/compute_current_h_gmt_add_8.js';
import { emit_query_period_change_not_tab_8 } from 'project/caopan/components/query/select_query_period/module/emit_query_period_change_not_tab_8.js';
import { handle_history_date_confirm } from 'project/caopan/components/query/select_query_period/module/emit_query_period_change_when_tab_8.js';
import { icon_click } from 'project/caopan/components/query/select_query_period/module/icon_click.js';
import { init_query_form_date_arr } from 'project/caopan/components/query/select_query_period/module/init_query_form_date_arr.js';
import { query_form_date_tab_click } from 'project/caopan/components/query/select_query_period/module/query_form_date_tab_click.js';
import { watch_date_and_change_query_form_date_arr } from 'project/caopan/components/query/select_query_period/module/watch_date_and_change_query_form_date_arr.js'
import { watcher_register } from "project/caopan/components/query/select_query_period/watch/index.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";
import { project_caopan_components_query_select_query_period_select_query_period_composable_variable_fn } from 'project/caopan/components/query/select_query_period/componsable/variable.js'
import {
    set_history_startTimeFrom
} from "src/output/common/store-common.js"
export const project_caopan_components_query_select_query_period_componsable_fn = (raw_payload) => {
    const base_state = project_caopan_components_query_select_query_period_select_query_period_composable_variable_fn()
    const { show_more_query, timer } = base_state
    const { props } = raw_payload
    let payload = {
        raw_payload,
        set_history_startTimeFrom,
        ...base_state
    }
    // 使用全局watch componsable处理watch相关事务
    src_componsable_util_watcher_register_componsable_fn(
        watcher_register(payload)
    );

    onMounted(() => {
        // 控制更多查询初始值
        show_more_query.value = props.more_query.value
        // this.timer = setInterval(this.compute_current_h_gmt_add_8,5000);
        timer.value = setInterval(compute_current_h_gmt_add_8(payload), 5000)
    })

    onUnmounted(() => {
        set_history_startTimeFrom(null);
        clearInterval(timer.value);
    })
    return {
        compute_current_h_gmt_add_8: () => compute_current_h_gmt_add_8(payload),
        emit_query_period_change_not_tab_8: () => emit_query_period_change_not_tab_8(payload),
        handle_history_date_confirm: () => handle_history_date_confirm(payload),
        icon_click: () => icon_click(payload),
        init_query_form_date_arr: () => init_query_form_date_arr(payload),
        query_form_date_tab_click: (value) => query_form_date_tab_click(payload, value),
        watch_date_and_change_query_form_date_arr: () => watch_date_and_change_query_form_date_arr(payload)
    }
}