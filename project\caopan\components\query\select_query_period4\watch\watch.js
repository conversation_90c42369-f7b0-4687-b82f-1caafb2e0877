import { watch } from "vue";



export const watcher_register = (payload) => {

  const {
    show_more_query,
    selected_date_tab,
    query_form_date_arr,
    query_form_date_arr1
  } = payload

  return [

    watch(() => show_more_query, () => {
      emit("show_more_query_change", show_more_query.value);
    }),

    watch(is_editing, (val) => {
      selected_date_tab.value = selected_date_tab.value == 9 ? 0 : selected_date_tab.value
      emit("selected_date_tab_online", selected_date_tab.value);
      query_form_date_arr.value = is_editing.value ? query_form_date_arr1.value.slice(1) : query_form_date_arr1.value
    }),
  ]








}
