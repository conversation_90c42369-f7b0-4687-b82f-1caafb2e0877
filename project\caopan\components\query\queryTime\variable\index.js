import { ref } from "vue";

export const  create_base_state_instance_fn=(payload)=>{
    const timeType_num = ref('1')  ;
    const times = ref([])  ;
    const optionsDate = ref({
        disabledDate: (date) => {
            const disabledDay = date.getTime()
            let endDate = new Date(get_before_time("d",{ type: "d", value: 0, }, true)).getTime()
            return disabledDay > endDate
          },
    })  ;

    return {
        timeType_num,
        times,
        optionsDate
    }
}