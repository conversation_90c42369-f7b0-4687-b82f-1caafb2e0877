/*
 * @Descripttion:
 * @Author: zada
 * @Date: 2021-01-19 17:48:04
 * @FilePath: /project/caopan/pages/operations_management/league_ranking/config/colums.js
 */
import { i18n_t } from "src/output/common/project-common.js";
export const table_columns_config = [
  {
    // 序号
    title: i18n_t("operations.league_ranking.c_1"),
    key: "sort",
    width: 100,
    slot: "sort",
  },
  {
    // 联赛名称
    title: i18n_t("leagueSet.table.th1"),
    key: "tournamentName",
    // minWidth: 140,
    slot: "tournamentName",
  },
  {
    // 联赛英文名称
    title: i18n_t("leagueSet.table.th2"),
    key: "tournamentEname",
    // minWidth: 120,
    slot: "tournamentEname",
  },
  {
    //联赛id
    title: i18n_t("leagueSet.table.th3"),
    key: "id",
    Width: 70,
    // render: (h, context) => {
    //   const {
    //     row: { id },
    //   } = context;
    //   return <span class={["RobotoBold1"]}>{id}</span>;
    // },
  },
  // {
  //   //置顶时段
  //   title: i18n_t("leagueSet.table.th12"),
  //   key: "topShowTime",
  //   width: 400,
  //   align: 'center',
  //   slot:"topShowTime"
  // },
  {
    //	新手版展示
    title: i18n_t("operations.league_ranking.c_2"),
    filters: [
      {
        label: i18n_t("operations.league_ranking.c_3"),
        // 展示
        value: 1,
      },
      {
        label: i18n_t("operations.league_ranking.c_4"),
        // 不展示
        value: 0,
      },
    ],
    filterMultiple: false,
    filterRemote(value, row) {
      params.value.versionNewStatus = value[0];
      params.value.start = 1;
      //更改后重新请求列表数据
      nextTick(() => {
        get_data_list();
      });
    },
    key: "web_show",
    slot: "web_show",
    sortType_c: "",
    width: 200,
  },
  {
    //操作
    title: i18n_t("common.operation"),
    slot: "data5",
    align: "center",
    key: "data5",
    width: 450,
  },
];

export const columnsChampion = [
  {
    // 序号
    title: i18n_t("operations.league_ranking.c_1"),
    key: "sort",
    width: 100,
    slot: "sort",
  },
  {
    // 联赛名称
    title: i18n_t("leagueSet.table.th1"),
    key: "tournamentName",
    // minWidth: 140,
    slot: "tournamentName",
  },
  {
    // 联赛英文名称
    title: i18n_t("leagueSet.table.th2"),
    key: "tournamentEname",
    // minWidth: 120,
    slot: "tournamentEname",
  },
  {
    //联赛id
    title: i18n_t("leagueSet.table.th3"),
    key: "id",
    Width: 70,
    // render: (h, context) => {
    //   const {
    //     row: { id },
    //   } = context;
    //   return <span class={["RobotoBold1"]}>{id}</span>;
    // },
  },
  {
    //操作
    title: i18n_t("common.operation"),
    slot: "data5",
    align: "center",
    key: "data5",
    width: 450,
  },
];
