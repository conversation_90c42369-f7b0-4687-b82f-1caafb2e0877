<!--
 * @FilePath: /project/caopan/pages/settlement_manage/settlement_v2/dialog/freeze_dialog.vue
 * @Description:
-->
<!-- 冻结弹窗 -->
<template>
  <q-dialog
    v-model="freeze_settle_data.show_cmp"
    :content-style="{ zIndex: 1000 }"
    persistent
  >
    <div
      class="event_dialog use-ivew q-py-lg"
      style="width: 600px"
      v-if="freeze_settle_data.show_cmp"
    >
      <div>
        <p class="title">
          <iIcon type="ios-close" @click="set_freeze_settle(2)" size="35" />
        </p>
        <div class="textdiv" style="padding-top: 70px">
          <div v-if="freeze_settle_data.freeze_time_status">
            <div class="row q-py-lg">
              <div class="col-4">{{ i18n_t("freeze.text7") }}：</div>
              <div
                v-for="item in quick_time"
                :key="item.value"
                @click="
                  (freeze_time_key = item.label),
                    (freeze_time.mins = item.value)
                "
                :class="
                  freeze_time_key == item.label ? 'ivu-page-item-active' : ''
                "
                class="q-px-sm q-color-picker__header-content q-mr-xs"
              >
                {{ item.label }}
              </div>
              <div
                @click="freeze_time_key = 'custome_freeze'"
                class="custom-input q-px-sm q-color-picker__header-content q-mr-xs"
                :class="
                  freeze_time_key == 'custome_freeze'
                    ? 'ivu-page-item-active'
                    : ''
                "
              >
                <input
                  v-model="custome_freeze_time"
                  :class="
                    freeze_time_key == 'custome_freeze'
                      ? 'ivu-page-item-active'
                      : ''
                  "
                  class="cst-input"
                />mins
              </div>
              <div
                @click="(freeze_time_key = ''), (freeze_time.mins = '')"
                :class="freeze_time_key == '' ? 'ivu-page-item-active' : ''"
                class="q-px-sm q-color-picker__header-content q-mr-xs"
              >
                全部
              </div>
            </div>
          </div>
          <!-- 提示内容 -->
          <div class="xuan_box q-py-lg">
            {{ freeze_settle_data.str_text }}
          </div>
        </div>
        <!-- btn 按钮 -->
        <p class="btnbox list_icon_box q-py-lg" style="text-align: center">
          <q-btn
            :label="i18n_t('settlement_v2.v_11')"
            no-caps
            @click="freeze_settle(freeze_settle_data)"
            color="secondary"
            class="panda-btn-light-dense sbtn"
          ></q-btn
          ><!-- 确定 -->
        </p>
      </div>
    </div>
  </q-dialog>
</template>
<script setup>
import { ref, reactive, nextTick } from "vue";
const props = defineProps({
  freeze_settle_data: Object,
});
const { freeze_settle_data } = toRefs(props);
const freeze_time_key = ref("");
const custome_freeze_time = ref("");
const freeze_time = reactive({
  mins: "", // 冻结快捷时间
  freezeTime: "", // 冻结选择时间 有 选择时间的情况下 快捷时间为空
});
const quick_time = [
  { value: "5", label: "5 mins" },
  { value: "10", label: "10 mins" },
  { value: "15", label: "15 mins" },
];

export const set_freeze_settle = (val) => {
  emit("set_freeze_settle", val);
};
export const freeze_settle = (freeze_settle_data) => {
  const { freezeTime, mins } = freeze_time;
  let obj = {
    freeze_settle_data,
    freeze_time: {
      freezeTime,
      mins:
        freeze_time_key.value === "custome_freeze"
          ? String(custome_freeze_time.value)
          : mins,
    },
  };
  emit("freeze_settle", obj);
  nextTick(() => {
    freeze_time_key.value = "";
    custome_freeze_time.value = "";
    freeze_time = {
      mins: "",
    };
  });
};
</script>

<style scoped lang="scss">
.custom-input {
  min-width: 50px;
  height: 24px;
  line-height: 24px;
  background-color: #fff;
  color: #333;
  font-size: 12px;
  padding: 0 10px;
  .cst-input {
    width: 14px;
    margin-right: 4px;
    outline: none;
    border: none;
  }
}
.q-color-picker__header-content {
  height: 24px;
  color: #333;
  line-height: 24px;
  font-size: 12px;
  cursor: pointer;
}
.event_dialog {
  min-width: 320px;
  min-height: 210px;
  padding: 10px 10px 20px 10px;
  font-size: 14px;

  .title {
    position: relative;

    i {
      position: absolute;
      right: 0px;
      top: 0px;
      z-index: 1;
      cursor: pointer;
    }
  }

  .textdiv {
    padding: 10px 30px;
    margin-bottom: 20px;

    .shua {
      display: flex;
      justify-content: left;
      align-items: center;
      margin-bottom: 10px;

      .icon_shua {
        display: inline-block;
        width: 18px;
        height: 18px;
        margin: 0 10px;
        cursor: pointer;
        background: url("/assets/settlement_v2_icon/refresh.svg")
          no-repeat 100%/100%;
      }

      .icon_shua:hover {
        background: url("/assets/settlement_v2_icon/refresh_a.svg")
          no-repeat 100%/100%;
      }
    }

    .textdiv {
      padding: 10px 30px;
      margin-bottom: 20px;

      .shua {
        display: flex;
        justify-content: left;
        align-items: center;
        margin-bottom: 10px;

        .icon_shua {
          display: inline-block;
          width: 18px;
          height: 18px;
          margin: 0 10px;
          cursor: pointer;
          background: url("/assets/settlement_v2_icon/refresh.svg") no-repeat
            100%/100%;
        }

        .icon_shua:hover {
          background: url("/assets/settlement_v2_icon/refresh_a.svg") no-repeat
            100%/100%;
        }
      }

      .xuan_box {
        > span {
          display: inline-block;
          width: 80px;
        }
      }
    }
  }
}
</style>
