import {onMounted,onBeforeUnmount } from "vue";
import { watcher_register } from "project/caopan/components/query/select_query_period4/watch/watch.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";
import {handle_history_date_confirm,icon_click} from "project/caopan/components/query/select_query_period4/module/index.js"
import { toggle_showrightdetail } from "project/caopan/componsable/layout/rightinfo/module/index.js";

export const project_caopan_components_query_select_query_period4_componsable_fn = (payload) => {
    onMounted(()=>{
      init_query_form_date_arr();

      // 控制更多查询初始值
      show_more_query.value = more_query.value;
      init_serverTime();
      // current_.value.then(res => {});
    });



    onBeforeUnmount(() => {
      set_history_startTimeFrom(null);
      clearInterval(timer.value);
    });

    src_componsable_util_watcher_register_componsable_fn(
        watcher_register(payload)
      );

    return {

      handle_history_date_confirm : (val)=>handle_history_date_confirm(payload,val),
      icon_click : ()=>icon_click(payload),
      toggle_showrightdetail : ()=>toggle_showrightdetail(payload),
    }
};