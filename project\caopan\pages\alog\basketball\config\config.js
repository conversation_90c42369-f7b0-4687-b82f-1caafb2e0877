/*
 * @FilePath: /project/caopan/pages/alog/config/config.js
 * @Description: 
 */
export const defaultPlayIdListConfig = {
  11001: [ // 第一节的时候展示FT HT Q1 Q3   //第四节展示 FT
    [[11001],[11003],[11002],[11005],[11006]],
    [[11007],[11009],[11008],[11011],[11012]],
    [[11019],[11021],[11020],[11023],[11024]],
    [[11031],[11033],[11032],[11035],[11036]],
  ], 
  11002: [ // 第二节展示FT HT Q3
    [[11001],[11003],[11002],[11005],[11006]],
    [[11007],[11009],[11008],[11011],[11012]],
    [[11031],[11033],[11032],[11035],[11036]],
  ], 
  11003: [ // 第三节展示FT Q3 
    [[11001],[11003],[11002],[11005],[11006]],
    [[11031],[11033],[11032],[11035],[11036]],
  ], 
}
export const categorySetIds = {
  //全场
  11001: {code: 'b_goal', class: 'panda_set1'}, 
  //上半场
  11002: {code: 'b_goal', class:'panda_set11'},
  //第一节
  11003: {code: 'b_goal', class:'panda_set2'},
}

export const categorySetIds_ = [11001, 11007, 11019, 11025, 11031, 11037]

// 篮球模式选择
export const modeConfig = [{mode: '412', value: '4Q/12mins'},{mode: '410', value: '4Q/10mins'}, {mode: '220', value: '2Q/20mins'}]

//篮球模式参数对应字段
export const modeCode = [{mode: '412',quarters:4,quarterMin:12,shotClock:24},{mode: '410',quarters:4,quarterMin:10,shotClock:24},{mode: '220',quarters:2,quarterMin:20,shotClock:24}]

export const quarters = ['FT','HT','Q3']
export const dataSorce = [
  {
    label: "L01",
    value: "LS",
  },
  {
    label: "G01",
    value: "BG",
  },
  {
    label: "PD",
    value: "PD",
  },
  {
    label: "F01",
    value: "F01",
  },
  {
    label: "T01",
    value: "TX",
  },
  {
    label: "N01",
    value: "N01",
  },
  {
    label: "N02",
    value: "N02",
  },
] 
export const match_timer_status= {
  // 0 为比分中心，1为手动比分
  isManualSocre: 0,
  // 结算时间分钟
  periodTimer: "0",
  // 阶段 0 早 1滚
  period: 0,
  checked_value: 0,
}