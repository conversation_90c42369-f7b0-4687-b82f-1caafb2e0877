

    export const onInitData = (payload) => {
      let tabs = [
        {
          name: "Regular",
          label: "Regular",
          active: true,
          badge: false,
        },
        {
          name: "Extra",
          label: "Extra",
          active: true,
          badge: false,
        },
        {
          name: "spec_event",
          label: "Spec Event",
          active: true,
          badge: false,
        },
        {
          name: "liner_margrn",
          label: "Liner Margin",
          active: true,
          badge: false,
        },
        {
          name: "auto_open",
          label: "Auto Open",
          active: check_auto_open_visible(),
          badge: false,
        },
      ];
      tabs.value = tabs.filter((tab) => tab.active);
    }
    export const ao_show = (payload,ao) => {
      tab_change.value = "Regular";
      emit("show_ao_of", ao);
    }
    export const updata_ao = (payload,param) => {
      emit("updata_ao_of", param);
    }
    export const check_auto_open_visible = (payload) => {
      const live_type = route.query.liveType==1 || route.query.matchType==0 ; // 滚球 为 '1'
      const btn_permissions = btn_permissions("auto_open_tab:view"); // 是否有查看页面权限

      return live_type && btn_permissions;
    }
