<template>
  <div class="p-f1 p-df p-fd-cl use-ivew" style="
      height: calc(100% - 42px);
      background: var(--q-color-panda-base-dark);
    ">
    <!-- <iSplit v-model="split" v-if="is_data" mode="vertical" @on-move-end="moveend"> -->
      <div #top>
        <!-- 上面列表 -->
        <div class="p-df p-fd-cl p-fg1 p-pr nodatabg" :style="{ height: height1 }">
          <div class="football">
            <!-- 比分面板组件 -->
            <football-score
              :class="['football_box football_left', !is_show ? 'football_left_show' : 'p-f1',]"
              :btn_is_show="is_show"
              :thirdMatchId="params_thirdMatchId"
              :game_data.sync="game_data"
              :current_event_status="current_event_status"
              @success="get_data">
              <div #icon class="icon_btn" @click="is_show = !is_show;">
                <iIcon type="ios-arrow-back" :class="['kai', !is_show && 'guan']" size="18" />
              </div>
            </football-score>
            <!-- PA足球报球 样式 -->
            <ul class="football_box football_right">
              <!-- 比赛时间 -->
              <li class="football_li">
                <p class="title">{{ i18n_t("PA_to_the_bat.f_t2") }}</p>
                <div class="football_li_top">
                  <!-- 取消结束 -->
                  <q-btn v-if="current_event_status == 999" style="padding: 0 5px" @click="show_cancel_over = true" class="cancel-over-btn btn"
                    :label="i18n_t('common.cancel') + i18n_t('common.Ended')" />
                  <q-btn v-else outline style="padding: 0 5px" @click="game_over" class="Manualrefresh frbtn" no-caps
                    :label="i18n_t('settlements.freeze.w7')" /><!-- 比赛结束 -->
                </div>
                <div class="football_li_bottom">
                  <div class="por">
                    <template v-if="is_kaisai">
                      <div class="btn_bd" @click="is_allowance = true" v-if="current_generation">
                        {{ current_generation.name }}
                      </div>
                      <i-input v-model="time_value" readonly @input="get_the_start_time"
                        @on-enter="set_the_start_time()" style="width: 70px; height: 28px; padding: 4px 5px">
                      </i-input>
                      <div class="btn_bd" @click="football_phase_change(current_generation)" v-if="
                        current_generation && current_generation.next_name
                      ">
                        {{ current_generation.next_name }}
                      </div>
                    </template>
                    <template v-else>
                      <div style="text-align: center">
                        <q-btn @click="open_ball('home')" outline style="padding: 0 5px; display: block"
                          class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t64')" /><!-- 主队开球 -->
                        <q-btn @click="open_ball('away')" outline style="
                            padding: 0 5px;
                            margin-top: 10px;
                            display: block;
                          " class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t65')" /><!-- 客队开球 -->
                        <q-btn @click="open_ball('none')" outline style="
                            padding: 0 15px;
                            margin-top: 10px;
                            display: block;
                          " class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t23')" /><!-- 开球 -->
                      </div>
                    </template>
                  </div>
                </div>
              </li>
              <!-- 赛事状况 -->
              <li class="football_li">
                <p class="title">{{ i18n_t("PA_to_the_bat.f_t3") }}</p>
                <div class="football_li_top"></div>
                <div :class="[
                  'football_li_bottom',
                  !is_safety ? 'bg_l' : 'bg_r',
                  is_var && 'bg_h',
                ]">
                  <div class="por">
                    <div class="text_li_top text_li" v-show="text_li_top_show">
                      {{ text_li_top }}
                    </div>
                    <div class="text_li_mid text_li" v-show="text_li_mid_show">
                      {{ text_li_mid }}
                    </div>
                    <div class="text_li_bottom text_li" v-show="text_li_bottom_show">
                      {{ text_li_bottom }}&nbsp; {{ count_down }}
                    </div>
                  </div>
                </div>
              </li>
              <!-- 赛事对阵 -->
              <li class="football_li">
                <p class="title">{{ i18n_t("PA_to_the_bat.f_t4") }}</p>
                <div class="football_li_top"></div>
                <div class="football_li_bottom">
                  <div class="football_li_team">
                    <p class="name">{{ get_name(obj.homeTeamNames) }}</p>
                    <p class="icon_img_list p-w100p">
                      <span>
                        <span v-show="[1, 2, 3].includes(hasPeriod)" class="p-fs--md vatb panda_new_cp_icon_1"></span>
                        <img v-show="[2, 3].includes(hasPeriod)" :src="require('public/PA_icon/jia.svg')"
                          style="margin-left: 5px" alt="jia" />
                        <img v-show="[3].includes(hasPeriod)" :src="require('public/PA_icon/dian.svg')"
                          style="margin-left: 5px" alt="dian" />
                      </span>
                      <q-btn v-if="res_retakePen == 'home'" outline @click="on_football_kickOff('home')" class="Manualrefresh frbtn" no-caps
                        style="padding: 2px 8px; font-size: 12px; background: #ec5852 !important;" :label="i18n_t('PA_to_the_bat.f_t23')" />
                    </p>
                  </div>
                  <div class="football_li_team">
                    <p class="name">{{ get_name(obj.awayTeamNames) }}</p>
                    <p class="icon_img_list p-w100p">
                      <span>
                        <span v-show="[1, 2, 3].includes(hasPeriod)" class="p-fs--md vatb panda_new_cp_icon_1"></span>
                        <img v-show="[2, 3].includes(hasPeriod)" :src="require('public/PA_icon/jia.svg')"
                          style="margin-left: 5px" alt="jia" />
                        <img v-show="[3].includes(hasPeriod)" :src="require('public/PA_icon/dian.svg')"
                          style="margin-left: 5px" alt="dian" />
                      </span>
                      <q-btn v-if="res_retakePen == 'away'" outline @click="on_football_kickOff('away')" class="Manualrefresh frbtn" no-caps
                        style="padding: 2px 8px; font-size: 12px; background: #67c16e !important;" :label="i18n_t('PA_to_the_bat.f_t23')" />
                    </p>
                  </div>
                </div>
              </li>
              <!-- 安全/危险 -->
              <li class="football_li">
                <p class="title">{{ i18n_t("PA_to_the_bat.f_t5") }}</p>
                <div class="football_li_top">
                  <!-- 0开   2关   1封   11锁   13收 -->
                  <span @click="update_match_status(0)" :class="[
                    is_kaisai && match_status == 0 ? '' : 'dl',
                    !is_kaisai && 'dis',
                  ]" class="vatb Match mt5x mr10x panda_icon_caopan_7"></span><!--设置开 -->
                  <span @click="update_match_status(2)" :class="[
                    is_kaisai && match_status == 2 ? '' : 'dl',
                    !is_kaisai && 'dis',
                  ]" class="vatb Match mt5x mr10x panda_icon_caopan_8"></span><!--设置关 -->
                  <span @click="update_match_status(1)" :class="[
                    is_kaisai && match_status == 1 ? '' : 'dl',
                    !is_kaisai && 'dis',
                  ]" class="vatb Match mt5x mr10x panda_icon_caopan_10"></span><!--设置封 -->
                  <span @click="update_match_status(13)" :class="[
                    is_kaisai && match_status == 13 ? '' : 'dl',
                    !is_kaisai && 'dis',
                  ]" class="vatb Match mt5x mr5x panda_icon_settle_status_1 cursor-pointer"></span><!--设置收 -->
                </div>
                <div :class="['football_li_bottom', 'football_li_bottom_top']">
                  <div class="box_btn bg_l">
                    <q-btn outline style="padding: 2px 30px" class="Manualrefresh frbtn" @click="set_is_actuality(0)"
                      :disable="!is_kaisai" no-caps :label="i18n_t('PA_to_the_bat.f_t19')" /><!-- 危险   -->
                  </div>
                  <div class="box_btn bg_r">
                    <q-btn outline style="padding: 2px 30px; margin-top: 10px" class="Manualrefresh frbtn"
                      @click="set_is_actuality(1)" :disable="!is_kaisai" no-caps
                      :label="i18n_t('PA_to_the_bat.f_t18')" /><!--   安全 -->
                  </div>
                  <div class="list_btn_an">
                    <div @click="add_var_event" :class="['btn_var', !is_kaisai && 'btn_d']">
                      VAR
                    </div>
                    <div @click="set_matchFreezeSettle" :class="[
                      'btn_an',
                      settlement_status == 0 ? 'btn_red' : 'btn_yellow',
                      !is_kaisai && 'btn_d',
                    ]">
                      {{settlement_status == 0? i18n_t("PA_to_the_bat.f_t87"): i18n_t("PA_to_the_bat.f_t88")}}
                    </div>
                  </div>
                </div>
              </li>
              <!-- 进球 -->
              <li class="football_li">
                <p class="title">{{ i18n_t("PA_to_the_bat.f_t89") }}</p>
                <div class="football_li_top" v-if="game_data">
                  <span class="score_num">{{
                    game_data && game_data.periodGoal
                    ? game_data.periodGoal.home
                    : 0
                  }}</span>&nbsp;-&nbsp;<span class="score_num">{{game_data && game_data.periodGoal? game_data.periodGoal.away: 0 }}</span>
                </div>
                <div class="football_li_bottom">
                  <div class="por">
                    <div class="football_li_team football_li_team_c" style="padding: 5px 10px">
                      <template v-if="is_team_type.hasHomeGoal">
                        <!-- 进球确认 -->
                        <q-btn outline @click="confirm_event_entry('goal', 'home')" :disable="!is_kaisai || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t67')" />
                        <!-- 进球取消 -->
                        <q-btn ref="jinqiuQuxiao" outline @click="cancel_event_entry('canceled_goal', 'home')" :disable="!is_kaisai"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t68')" />
                      </template>
                      <template v-else-if="is_team_type.hasHomePenalty">
                        <!-- 进球确认 -->
                        <q-btn outline @click="confirm_event_entry('penalty_goal', 'home')" :disable="!is_kaisai  || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t67')" />
                        <!-- 点球未进 -->
                        <q-btn outline @click="cancel_event_entry('penalty_missed', 'home')" :disable="!is_kaisai"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t69')" />
                        <!-- 没有点球 -->
                        <q-btn outline @click="cancel_event_entry('canceled_penalty', 'home')" :disable="!is_kaisai" 
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t70')" />
                      </template>
                      <template v-else>
                        <!-- 进球 -->
                        <q-btn outline @click="possible_event_entry('possible_goal', 'home')" :disable="!is_kaisai || maybe_btn_show.home.possible_goal"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t6')" />
                        <!-- 点球 -->
                        <q-btn outline @click="
                          possible_event_entry('possible_penalty', 'home')
                        " :disable="!is_kaisai || maybe_btn_show.home.possible_penalty" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t11')" />
                      </template>
                    </div>
                    <div class="football_li_team football_li_team_c" style="padding: 5px 10px">
                      <template v-if="is_team_type.hasAwayGoal">
                        <!-- 进球确认 -->
                        <q-btn outline @click="confirm_event_entry('goal', 'away')" :disable="!is_kaisai || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t67')" />
                        <!-- 进球取消 -->
                        <q-btn outline @click="cancel_event_entry('canceled_goal', 'away')" :disable="!is_kaisai"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t68')" />
                      </template>
                      <template v-else-if="is_team_type.hasAwayPenalty">
                        <!-- 进球确认 -->
                        <q-btn outline @click="confirm_event_entry('penalty_goal', 'away')" :disable="!is_kaisai  || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t67')" />
                        <!-- 点球未进 -->
                        <q-btn outline @click="cancel_event_entry('penalty_missed', 'away')" :disable="!is_kaisai"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t69')" />
                        <!-- 没有点球 -->
                        <q-btn outline @click="cancel_event_entry('canceled_penalty', 'away')" :disable="!is_kaisai" 
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t70')" />
                      </template>
                      <template v-else>
                        <!-- 进球 -->
                        <q-btn outline @click="possible_event_entry('possible_goal', 'away')" :disable="!is_kaisai || maybe_btn_show.away.possible_goal"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t6')" />
                        <!-- 点球 -->
                        <q-btn outline @click="
                          possible_event_entry('possible_penalty', 'away')
                        " :disable="!is_kaisai || maybe_btn_show.away.possible_penalty" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t11')" />
                      </template>
                    </div>
                  </div>
                </div>
              </li>
              <!-- 角球 -->
              <li class="football_li">
                <p class="title">{{ i18n_t("PA_to_the_bat.f_t7") }}</p>
                <div class="football_li_top" v-if="game_data">
                  <span class="score_num">{{
                    game_data && game_data.periodCorner
                    ? game_data.periodCorner.home
                    : 0
                  }}</span>&nbsp;-&nbsp;<span class="score_num">{{
                      game_data && game_data.periodCorner
                      ? game_data.periodCorner.away
                      : 0
                    }}</span>
                </div>
                <div class="football_li_bottom">
                  <div class="por">
                    <div class="football_li_team football_li_team_c" style="padding: 5px 10px">
                      <template v-if="is_team_type.hasHomeCorner">
                        <!-- 角球确认 -->
                        <q-btn outline @click="confirm_event_entry('corner', 'home')" :disable="!is_kaisai || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t72')" />
                        <!-- 角球取消 -->
                        <q-btn outline @click="cancel_event_entry('canceled_corner', 'home')" :disable="!is_kaisai"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t73')" />
                      </template>
                      <template v-else>
                        <!-- 角球 -->
                        <q-btn outline @click="
                          possible_event_entry('possible_corner', 'home')
                        " :disable="!is_kaisai || maybe_btn_show.home.possible_corner" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t7')" />
                      </template>
                    </div>
                    <div class="football_li_team football_li_team_c" style="padding: 5px 10px">
                      <template v-if="is_team_type.hasAwayCorner">
                        <!-- 角球确认 -->
                        <q-btn outline @click="confirm_event_entry('corner', 'away')" :disable="!is_kaisai || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t72')" />
                        <!-- 角球取消 -->
                        <q-btn outline @click="cancel_event_entry('canceled_corner', 'away')" :disable="!is_kaisai"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t73')" />
                      </template>
                      <template v-else>
                        <!-- 角球 -->
                        <q-btn outline @click="
                          possible_event_entry('possible_corner', 'away')
                        " :disable="!is_kaisai || maybe_btn_show.away.possible_corner" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t7')" />
                      </template>
                    </div>
                  </div>
                </div>
              </li>
              <!-- 罚牌 -->
              <li class="football_li">
                <p class="title">{{ i18n_t("PA_to_the_bat.f_t8") }}</p>
                <div class="football_li_top" v-if="game_data">
                  <span class="score_num">{{
                    game_data && game_data.periodFaCard
                    ? game_data.periodFaCard.home
                    : 0
                  }}</span>&nbsp;-&nbsp;
                  <span class="score_num">{{
                    game_data && game_data.periodFaCard
                    ? game_data.periodFaCard.away
                    : 0
                  }}</span>
                </div>
                <div class="football_li_bottom">
                  <div class="por">
                    <div class="football_li_team football_li_team_c" style="padding: 5px 10px">
                      <template v-if="is_team_type.hasHomeYellowCard">
                        <!-- 黄牌确认 -->
                        <q-btn outline @click="confirm_event_entry('yellow_card', 'home')" :disable="!is_kaisai || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t75')" />
                        <!-- 没有黄牌 -->
                        <q-btn outline @click="
                          cancel_event_entry('canceled_yellow_card', 'home')
                        " :disable="!is_kaisai" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t76')" />
                      </template>
                      <template v-else-if="is_team_type.hasHomeRedCard">
                        <!-- 红牌确认 -->
                        <q-btn outline @click="confirm_event_entry('red_card', 'home')" :disable="!is_kaisai  || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t78')" />
                        <!-- 没有红牌 -->
                        <q-btn outline @click="
                          cancel_event_entry('canceled_red_card', 'home')
                        " :disable="!is_kaisai" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t79')" />
                      </template>
                      <template v-else>
                        <!-- 黄牌 -->
                        <q-btn outline @click="
                          possible_event_entry('possible_yellow_card', 'home')
                        " :disable="!is_kaisai || maybe_btn_show.home.possible_yellow_card" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t12')" />
                        <!-- 红牌 -->
                        <q-btn outline @click="
                          possible_event_entry('possible_red_card', 'home')
                        " :disable="!is_kaisai || maybe_btn_show.home.possible_red_card" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t13')" />
                      </template>
                      <template v-if="1 == 4">
                        <!-- 红黄牌确认 -->
                        <q-btn outline :disable="!is_kaisai || !queren" style="padding: 2px 8px; font-size: 12px"
                          class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t81')" />
                        <!-- 没有红黄牌 -->
                        <q-btn outline :disable="!is_kaisai" style="padding: 2px 8px; font-size: 12px"
                          class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t82')" />
                      </template>
                    </div>
                    <div class="football_li_team football_li_team_c" style="padding: 8px 10px">
                      <template v-if="is_team_type.hasAwayYellowCard">
                        <!-- 黄牌确认 -->
                        <q-btn outline @click="confirm_event_entry('yellow_card', 'away')" :disable="!is_kaisai || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t75')" />
                        <!-- 没有黄牌 -->
                        <q-btn outline @click="
                          cancel_event_entry('canceled_yellow_card', 'away')
                        " :disable="!is_kaisai" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t76')" />
                      </template>
                      <template v-else-if="is_team_type.hasAwayRedCard">
                        <!-- 红牌确认 -->
                        <q-btn outline @click="confirm_event_entry('red_card', 'away')" :disable="!is_kaisai || !queren"
                          style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn" no-caps
                          :label="i18n_t('PA_to_the_bat.f_t78')" />
                        <!-- 没有红牌 -->
                        <q-btn outline @click="
                          cancel_event_entry('canceled_red_card', 'away')
                        " :disable="!is_kaisai" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t79')" />
                      </template>
                      <template v-else>
                        <!-- 黄牌 -->
                        <q-btn outline @click="
                          possible_event_entry('possible_yellow_card', 'away')
                        " :disable="!is_kaisai || maybe_btn_show.away.possible_yellow_card" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t12')" />
                        <!-- 红牌 -->
                        <q-btn outline @click="
                          possible_event_entry('possible_red_card', 'away')
                        " :disable="!is_kaisai || maybe_btn_show.away.possible_red_card" style="padding: 2px 8px; font-size: 12px" class="Manualrefresh frbtn"
                          no-caps :label="i18n_t('PA_to_the_bat.f_t13')" />
                        <!-- <q-btn outline  :disable="!is_kaisai" style="padding: 2px 5px;font-size: 12px;" class="Manualrefresh  frbtn"  no-caps :label="i18n_t('PA_to_the_bat.f_t14')"/> --><!-- 红黄牌 -->
                      </template>
                      <template v-if="1 == 5">
                        <!-- 红黄牌确认 -->
                        <q-btn outline :disable="!is_kaisai" style="padding: 2px 8px; font-size: 12px"
                          class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t81')" />
                        <!-- 没有红黄牌 -->
                        <q-btn outline :disable="!is_kaisai" style="padding: 2px 8px; font-size: 12px"
                          class="Manualrefresh frbtn" no-caps :label="i18n_t('PA_to_the_bat.f_t82')" />
                      </template>
                    </div>
                  </div>
                </div>
              </li>
              <!-- 暂时取消 该展示 -->
              <li class="football_li" v-show="false">
                <p class="title">{{ i18n_t("PA_to_the_bat.f_t9") }}</p>
                <div class="football_li_top" v-if="game_data">
                  <span class="score_num">{{
                    game_data && game_data.periodKickOff
                    ? game_data.periodKickOff.home
                    : 0
                  }}</span>&nbsp;-&nbsp;
                  <span class="score_num">{{
                    game_data && game_data.periodKickOff
                    ? game_data.periodKickOff.away
                    : 0
                  }}</span>
                </div>
                <div class="football_li_bottom" v-if="game_data && game_data.periodKickOff">
                  <div class="input_box">
                    <i-input v-model="game_data.periodKickOff.home" :disabled="!is_kaisai" @input="change_search_diff"
                      @on-enter="set_open_ball()" :maxlength="1" style="width: 50px; height: 28px; padding: 4px 5px" />
                    -
                    <i-input v-model="game_data.periodKickOff.away" :disabled="!is_kaisai" @input="change_search_diff"
                      @on-enter="set_open_ball()" :maxlength="1" style="width: 50px; height: 28px; padding: 4px 5px" />
                  </div>
                  <div class="btn_icon_img">
                    <img :class="[!is_kaisai && 'de']" @click="set_open_ball" src="~app/public/PA_icon/gou.svg"
                      alt="gou" />
                    <img :class="['icon', !is_kaisai && 'de']" @click="close_open_ball"
                      src="~app/public/PA_icon/close.svg" alt="close" />
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <iSpin size="large" fix v-if="right_loading"></iSpin>
        </div>
        <!-- 15 分钟 玩法-->
        <div id="middleList" :class="[!tab&&'full-height']" ref="middleListRef" class=" mt30x  game-15-container" style="margin-top:5px;"  v-if="
          current_generation &&
          ![32, 41, 33, 42, 110, 34, 50, 120].includes(current_generation.code)&&[0,46].includes(matchLength)
        ">
          <q-list
            dark
            class="rounded-borders q-mb-sm"
            style="display: flex; flex-wrap: wrap;align-items: left; justify-content: space-between;"
          >
            <footBallRuleAction
            :current_generation="current_generation"
            :editState="editState"
            @expandStateChange="expandStateChange"
            :footBallScoreData="[footBallGoal15Score,footBallCorner15Score,footBallYellowCard15Vo,footBallRedCard15Vo]"
            @edit_click="edit_click"
            @submit_15_data="submit_15_data"
            @cancel_15_data="cancel_15_data" />
          </q-list>
          <q-list dark class="rounded-borders" style="margin-top:-10px;">
            <q-expansion-item icon="drafts" header-class="expanHeaderClass" style="width: 100%" v-model="expanded5Gobl">
              <template v-slot:header>
                <q-item-section>
                  <span >
                    <i :class="fiveEdit?'time-tag':'text-tag'">{{ i18n_t(`settlement_v2.v_307`) }}</i>
                    <span :title="i18n_t('settlement_v2.v_5')" @click.stop="edit_click('5goal')" class="edit q-ml-sm"
                      v-if="!fiveEdit&&current_generation.code!=999"></span>
                    <span v-else-if="fiveEdit&&current_generation.code!=999">
                      <img class="imgicon q-ml-sm" src="/assets/PA_icon/gou.svg" alt="gou"
                        @click.stop="submit_5_data()">
                      <img class="imgicon q-ml-sm" src="/assets/PA_icon/close.svg"
                        @click.stop="cancel_15_data('5goal')" alt="close">
                    </span>
                  </span>
                </q-item-section>
              </template>
              <q-card class="bg-grey-9" style="
                  justify-content: flex-start;
                  background: rgb(27, 33, 44) !important;
                ">
                <q-card-section>
                  <ul class="scores15" :class="fiveEdit?'edit-form':''">
                    <li>
                      <div v-for="(item, index) in footBallGoal5Score" :key="index">
                        <div v-if="['goal5','goal10','goal15','goal20','goal25','goal30',].includes(index)"  class="scores15-class">
                          <span class="stage_label q-ml-sm" style="line-height: 1">{{
                          i18n_t(`PA_to_the_bat.stage1.${[index]}`) }}</span>
                          <i-input v-model="item.home" :readonly="(item.home>=0) ? !fiveEdit: true" :class="(item.home>=0)?'':'not-edit-form'"
                            :placeholder="i18n_t('abnormal_dialog.abnormal_10')" style="width: 40px"></i-input>
                          <i-input v-model="item.away" :readonly="(item.home>=0) ? !fiveEdit: true" :class="(item.home>=0)?'':'not-edit-form'"
                            :placeholder="i18n_t('abnormal_dialog.abnormal_11')" style="width: 40px"></i-input>
                        </div>
                      </div>
                    </li>
                    <li>
                      <div v-for="(item, index) in footBallGoal5Score" :key="index" style="margin-left:2px;">
                        <div v-if="['goal35','goal40','goal45','goal49','goal50','goal55',].includes(index)"  class="scores15-class">
                          <span class="stage_label q-ml-sm" style="line-height: 1">{{
                          i18n_t(`PA_to_the_bat.stage1.${[index]}`) }}</span>
                          <i-input v-model="item.home" :readonly="(item.home>=0) ? !fiveEdit: true" :class="(item.home>=0)?'':'not-edit-form'"
                            :placeholder="i18n_t('abnormal_dialog.abnormal_10')" style="width: 40px"></i-input>
                          <i-input v-model="item.away" :readonly="(item.home>=0) ? !fiveEdit: true" :class="(item.home>=0)?'':'not-edit-form'"
                            :placeholder="i18n_t('abnormal_dialog.abnormal_11')" style="width: 40px"></i-input>
                        </div>
                      </div>
                    </li>

                    <li>
                      <div v-for="(item, index) in footBallGoal5Score" :key="index" style="margin-left:5px;">
                        <div v-if="['goal60','goal65','goal70','goal75','goal80','goal85'].includes(index)" class="scores15-class" >
                          <span class="stage_label q-ml-sm" style="line-height: 1">
                            {{i18n_t(`PA_to_the_bat.stage1.${[index]}`) }}
                           </span>
                          <i-input v-model="item.home" :readonly="(item.home>=0) ? !fiveEdit: true" :class="(item.home>=0)?'':'not-edit-form'"
                            :placeholder="i18n_t('abnormal_dialog.abnormal_10')" style="width: 40px"></i-input>
                          <i-input v-model="item.away" :readonly="(item.home>=0) ? !fiveEdit: true" :class="(item.home>=0)?'':'not-edit-form'"
                            :placeholder="i18n_t('abnormal_dialog.abnormal_11')" style="width: 40px"></i-input>
                        </div>
                      </div>
                    </li>
                    <li>
                      <div v-for="(item, index) in footBallGoal5Score" :key="index" style="margin-left:2px;">
                        <div v-if="['goal90','goal99'].includes(index)" class="scores15-class" >
                          <span class="stage_label q-ml-sm" style="line-height: 1">
                            {{i18n_t(`PA_to_the_bat.stage1.${[index]}`) }}
                           </span>
                          <i-input v-model="item.home" :readonly="(item.home>=0) ? !fiveEdit: true" :class="(item.home>=0)?'':'not-edit-form'"
                            :placeholder="i18n_t('abnormal_dialog.abnormal_10')" style="width: 40px"></i-input>
                          <i-input v-model="item.away" :readonly="(item.home>=0) ? !fiveEdit: true" :class="(item.home>=0)?'':'not-edit-form'"
                            :placeholder="i18n_t('abnormal_dialog.abnormal_11')" style="width: 40px"></i-input>
                        </div>
                      </div>
                    </li>
                  </ul>
                </q-card-section>
              </q-card>
            </q-expansion-item>
          </q-list>
        </div>
        <!-- 点球大战 -->
        <div class="p-df p-fd-cl p-fg1 p-pr mt10x nodatabg dqtable" v-if="
          current_generation &&
          [34, 50, 120].includes(current_generation.code)
        " :style="{ marginLeft: table_marginleft }">
          <ul class="scores" :style="{ width: table_width }">
            <li class="lihead">
              <span class="btns but" :class="item.value == cur_num ? 'cur' : ''" v-for="item in per_list"
                :key="'count' + item.value" @click="changenum(item.value)">{{ item.value }}</span>
              <span class="jia" @click="addround">+</span>
              <span class="btns w150">{{ i18n_t("PA_to_the_bat.f_t101") }}</span><!-- 前1-5轮点球大战 -->
              <span class="btns w150">{{
              i18n_t("matchPeriodId.matchPeriodId1_50")
              }}</span><!-- 点球大战 -->
            </li>
            <li class="licont">
              <span style="width: 100px">{{ i18n_t("common.home") }}</span><!-- 主 -->
              <span class="w80" v-for="(item, index) in footBallPenaltyScore.home" :key="'zhu' + item.id"
                @mouseover="canshow(item.id, 'home', true)" @mouseleave="canshow(item.id, 'home', false)">
                <div style="display: inline-block" v-if="!item.edit">
                  {{ item.value != null ? item.value : "-" }}
                </div>
                <img v-show="item.imgshow && !item.edit" @click="show_input_box(index, 'home')"
                  src="/assets/settlement_v2_icon/edit_a.svg" class="editicon icons" />
                <div v-if="item.edit" style="display: inline-block; height: 100%">
                  <i-input v-model.number="item.value" @input="vali" :maxlength="1" style="width: 50px; height: 40px"
                    class="inp"></i-input>
                  <div class="img_list">
                    <img @click="submit(item, 'home')" style="margin-right: 8px" src="/assets/PA_icon/gou.svg"
                      alt="gou" />
                    <img @click="set_close(index, 'home')" class="icon" src="/assets/PA_icon/close.svg"
                      alt="close" />
                  </div>
                </div>
              </span>
              <span class="yword">{{ footBallPenaltyScore.s5home }}</span>
              <span class="yword" style="border: none">{{
              footBallPenaltyScore.allhome
              }}</span>
            </li>
            <li class="licont" style="border-bottom: none">
              <span style="width: 100px">{{ i18n_t("common.away") }}</span><!-- 客 -->
              <span class="w80" v-for="(item, index) in footBallPenaltyScore.away" :key="'ke' + item.id"
                @mouseover="canshow(item.id, 'away', true)" @mouseleave="canshow(item.id, 'away', false)">
                <div style="display: inline-block" v-if="!item.edit">
                  {{ item.value != null ? item.value : "-" }}
                </div>
                <img v-show="item.imgshow && !item.edit" @click="show_input_box(index, 'away')"
                  src="/assets/settlement_v2_icon/edit_a.svg" class="editicon icons" />
                <div v-if="item.edit" style="display: inline-block; height: 100%">
                  <i-input v-model.number="item.value" @input="vali" :maxlength="1" style="width: 50px; height: 40px"
                    class="inp"></i-input>
                  <div class="img_list">
                    <img @click="submit(item, 'away')" style="margin-right: 8px" src="/assets/PA_icon/gou.svg"
                      alt="gou" />
                    <img @click="set_close(index, 'away')" class="icon" src="/assets/PA_icon/close.svg"
                      alt="close" />
                  </div>
                </div>
              </span>
              <span class="yword">{{ footBallPenaltyScore.s5away }}</span>
              <span class="yword" style="border: none">{{
              footBallPenaltyScore.allaway
              }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div #bottom class="col">
        <!-- 下面标签切换 -->
        <div class="p-df p-fd-cl " id="bottomList" ref="bottomListRef" :style="{ height: height2 }">
          <header class="p-df p-jcsb p-aic border-top-black tabheader">
            <q-tabs v-model="tab" align="left" dense active-bg-color="panda-secondary" active-color="panda-text-primary"
              class="text-panda-text-dark">
              <!-- 赛事消息" -->
              <q-tab no-caps name="result" :label="i18n_t('event.center.w15')" class="border-right-black" :disable="!isbottom" />
              <q-tab no-caps name="result_msg" :label="i18n_t('event.center.w9')" class="border-right-black" :disable="!isbottom" /><!-- 事件消息 -->
              <q-tab v-show="false" no-caps name="note_the_single" :label="i18n_t('event.center.w16')"
                class="border-right-black" :disable="isbottom" /><!-- 注单 -->
              <q-tab no-caps name="opt_log" @click="getLog" :label="i18n_t('champion_plays.text42')" class="border-right-black"
                :disable="!isbottom" /><!-- 操作日志 -->
            </q-tabs>
            <span class="panda-right-info-toogle-button mr-10 p-tr90" :class="isbottom ? '' : 'active'" @click="toggle_result_info">
              <q-tooltip anchor="bottom middle" self="top middle">
                {{ bottom_info_map[isbottom] }}
              </q-tooltip>
              <span class="panda-right-info-toogle-button-inner"></span>
            </span>
          </header>
          <q-tab-panels v-model="tab" class="match-result__tab-panels_bc p-fg1 football-tab-content">
            <!-- 赛事消息 -->
            <q-tab-panel name="result" class="nodatabg">
              <ul class="list_news text-panda-text-light">
                <li class="list_news_li" key="title00001">
                  <div :class="['list_news_letf', 'bg_h']">
                    <span class="count">{{ get_name(obj.homeTeamNames) }}</span>
                    <span class="btn_shan"></span>
                    <span class="time">{{ i18n_t("PA_to_the_bat.f_t49") }}</span><!-- 当前时间 -->
                  </div>
                  <div class="list_news_mid">
                    {{ i18n_t("PA_to_the_bat.f_t50") }}
                  </div>
                  <!-- 赛事情况 -->
                  <div :class="['list_news_right', 'bg_h']">
                    <span class="time">{{ i18n_t("PA_to_the_bat.f_t51") }}</span><!-- 球赛时间 -->
                    <span class="btn_shan"></span>
                    <span class="count">{{ get_name(obj.awayTeamNames) }}</span>
                  </div>
                </li>
                <template v-if="event_list.length > 0">
                  <li class="list_news_li" v-for="(item, index) in event_list" :key="index">
                    <div :class="[
                      'list_news_letf',
                      item.homeAway == 'home' && 'bg_h',
                    ]">
                      <span v-if="item.homeAway == 'home'" class="count">
                        <span class="shan" v-show="item.addition10 == 1">{{ i18n_t("PA_to_the_bat.f_t91") }} &nbsp;</span>
                        {{
                        item.t1 || item.t2 || item.t1 == 0 || item.t2 == 0
                        ? `${ item.t1 ? item.t1 : 0 } - ${item.t2 ? item.t2 : 0}, `
                        : ""
                        }}{{ ` ${ item.event_name && item.event_name.name } ` }}
                      </span>
                      <span v-if="item.homeAway == 'all'" class="count">
                        <!-- 0进球 1点球 2红牌 -->
                          {{ item.all_name ? item.all_name : '' }}
                      </span>
                      <span class="btn_shan">
                        <template v-if="
                          item.addition10 != 1 &&
                          item.matchPeriodId != 50 &&
                          item.homeAway == 'home' &&
                          [
                            'corner',
                            'goal',
                            'red_card',
                            'yellow_card',
                            'kick_off',
                          ].includes(item.eventCode)
                        ">
                          <small @click="set_cmp_show_box(item, 2)" class="btn_i">{{ i18n_t("PA_to_the_bat.f_t53")
                          }}</small><!-- 删除 -->
                        </template>
                      </span>
                      <span class="time">{{ get_date(item.eventTime) }}</span>
                    </div>
                    <div class="list_news_mid">
                      <!--  f_t18 危险   f_t19 安全-->
                      <span :class="[
                        'btn',
                        item.danger && 'btn_r',
                        isVAR(item.eventCode) && 'btn_h',
                      ]">{{ isVAR(item.eventCode) ? "VAR" : item.danger ? i18n_t("PA_to_the_bat.f_t18") : i18n_t("PA_to_the_bat.f_t19") }}</span>
                    </div>
                    <div :class="[
                      'list_news_right',
                      item.homeAway == 'away' && 'bg_h',
                    ]">
                      <!-- 展示当前赛事时间 -->
                      <span class="time">{{
                      get_time(Number(item.secondsFromStart || 0) * 1000)
                      }}</span>
                      <span class="btn_shan">
                        <template v-if="
                          item.addition10 != 1 &&
                          item.matchPeriodId != 50 &&
                          item.homeAway == 'away' &&
                          [
                            'corner',
                            'goal',
                            'red_card',
                            'yellow_card',
                            'kick_off',
                          ].includes(item.eventCode)
                        ">
                          <small @click="set_cmp_show_box(item, 2)" class="btn_i">{{ i18n_t("PA_to_the_bat.f_t53")
                          }}</small>
                          <!-- 删除 -->
                        </template>
                      </span>
                      <span v-if="item.homeAway == 'away'" class="count">
                        <span class="shan" v-show="item.addition10 == 1">{{ i18n_t("PA_to_the_bat.f_t91") }} &nbsp;</span>
                        {{
                        item.t1 || item.t2 || item.t1 == 0 || item.t2 == 0
                        ? `${ item.t1 ? item.t1 : 0 } - ${ item.t2 ? item.t2 : 0}, `
                        : ""
                        }}{{ ` ${ item.event_name && item.event_name.name } ` }}
                      </span>
                      <span v-if="item.homeAway == 'all'" class="count">
                        <!-- 0进球 1点球 2红牌 -->
                          {{ item.all_name ? item.all_name : '' }}
                      </span>
                    </div>
                  </li>
                </template>
              </ul>
              <iSpin size="large" fix v-if="right_loading"></iSpin>
            </q-tab-panel>
            <!-- 事件消息列表 -->
            <q-tab-panel name="result_msg" class="nodatabg">
              <ul>
                <template v-for="(item, index) in list2">
                  <li class="p-df p-jcsb p-aic p-h--mx text-panda-text-light"
                    v-if="!(item.templateFormat.indexOf('QT') != -1)" :key="index">
                    <div class="p-df p-ml--xxxs">
                      <span class="p-dot bg-panda-primary mt5x"></span>
                      <span class="ml10x">
                        <span>{{ item.templateFormat }}</span>
                      </span>
                    </div>
                    <div class="p-mr--xxs">
                      <span>{{ item.eventTime }}</span>
                    </div>
                  </li>
                </template>
              </ul>
              <iSpin size="large" fix v-if="right_loading"></iSpin>
            </q-tab-panel>
            <!-- 注单 -->
            <q-tab-panel name="note_the_single" class="nodatabg">
              <div>注单</div>
              <iSpin size="large" fix v-if="right_loading"></iSpin>
            </q-tab-panel>
            <!-- 操作日志列表 -->
            <q-tab-panel name="opt_log" class="nodatabg">
              <q-virtual-scroll class="p-h--full text-panda-text-light" :items="list3" >
                <template v-slot="{ item, index }">
                  <li class="p-df p-jcsb p-aic p-fb100p" :class="
                    [2, 3].includes(item.operateType)
                      ? 'panda-text-orange'
                      : ''
                  " :key="index" v-if="item.templateText" style="height: 40px">
                    <div class="p-df p-aic p-ml--xxxs" style="width: 85%">
                      <span class="p-dot bg-panda-primary"></span>
                      <p style="margin-left: 6px">
                        <span class="p-mx--xxxxxs">{{ item.operateId }}</span>
                        <span>{{
                        item.operateName ? item.operateName + ", " : ""
                        }}</span>
                        <span v-if="item.canceled == 1">{{
                        i18n_t("event.center.w14")
                        }}</span><!-- 删除 -->
                        <span v-html="`【${ $language_value(item.templateText) }】`"></span>
                      </p>
                    </div>
                    <div class="p-mr--xxs">
                      <span>{{ item.createTime }}</span>
                    </div>
                  </li>
                </template>
              </q-virtual-scroll>
              <iSpin size="large" fix v-if="right_loading"></iSpin>
            </q-tab-panel>
          </q-tab-panels>
        </div>
      </div>
    <!-- </iSplit> -->
    <!-- 补时弹窗 -->
    <q-dialog v-model="is_allowance" persistent>
      <div class="ow_dia">
        <ul class="list_btn"></ul>
        <div class="input_box">
          <i-input v-model="time_data.m" @input="change_search_diff" @on-enter="set_the_start_time()" :maxlength="3"
            style="width: 50px; height: 28px; padding: 4px 5px" />
          {{ i18n_t("PA_to_the_bat.f_t42")
          }}<!-- 分 -->
          <i-input v-model="time_data.s" @input="change_search_diff" @on-enter="set_the_start_time()" :maxlength="3"
            style="width: 50px; height: 28px; padding: 4px 5px" />
          {{ i18n_t("PA_to_the_bat.f_t43")
          }}<!-- 秒 -->
        </div>
        <p style="margin-top: 16px; text-align: center">
          <q-btn :label="i18n_t('settlements.btn.w14')" @click="get_submit" color="secondary" style="margin-right: 10px"
            class="panda-btn-light-dense sbtn"></q-btn><!-- 确认 -->
          <q-btn :label="i18n_t('settlements.btn.w9')" class="panda-btn-dark-dense cbtn"
            @click="is_allowance = false" /><!-- 取消 -->
        </p>
      </div>
    </q-dialog>
    <!-- 修改比分   删除事件 -->
    <q-dialog v-model="cmp_show" persistent>
      <div class="cmp_show_box" v-if="cmp_show">
        <div class="title">
          <iIcon type="ios-alert" size="18" style="color: #f7a217; margin-right: 10px" />
          <!-- 确认修改比分 1    确定删除此比分事件？ 2 -->
          {{
          is_cmp == 1 ? i18n_t("PA_to_the_bat.f_t54") : i18n_t("PA_to_the_bat.f_t55")
          }}
        </div>
        <div class="input_box" v-if="is_cmp == 1">
          <i-input v-model="score_input.t1" @input="change_search_diff" :maxlength="3"
            style="width: 50px; height: 30px; padding: 4px 5px" />
          &nbsp;&nbsp;-&nbsp;&nbsp;
          <i-input v-model="score_input.t2" @input="change_search_diff" :maxlength="3"
            style="width: 50px; height: 30px; padding: 4px 5px" />
        </div>
        <p style="margin-top: 16px; text-align: center">
          <q-btn :label="i18n_t('settlements.btn.w9')" class="panda-btn-dark-dense cbtn" style="margin-right: 10px"
            @click="cmp_show = false" /><!-- 取消 -->
          <q-btn :label="i18n_t('settlements.btn.w14')"  :disable="!queren" @click="set_cmp_show" color="secondary"
            class="panda-btn-light-dense sbtn"></q-btn><!-- 确认 -->
        </p>
      </div>
    </q-dialog>
    <football-cancel-over-dialog 
      :show.sync="show_cancel_over"
      :thirdMatchId="params_thirdMatchId"
      :dataSourceCode="dataSourceCode"
      @success="get_data"
    ></football-cancel-over-dialog>
  </div>
</template>

<script src="./football/football.js"></script>

<style lang="scss" scoped>
@import './football/football.scss';
</style>
