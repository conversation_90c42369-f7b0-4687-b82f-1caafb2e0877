import {
  src_componsable_global_variable_componsable_fn,
  // project_saicheng_componsable_table_base_config_variable_componsable_fn,
  // project_saicheng_componsable_table_table_query_variable_componsable_fn,
  // project_saicheng_componsable_constant_query_variable_componsable_fn,
  // project_saicheng_componsable_pages_match_selected_table_item_variable_componsable_fn,
  // project_saicheng_componsable_router_cache_handler_variable_componsable_fn,
  // project_saicheng_componsable_pages_match_match2_variable_componsable_fn,
  // project_saicheng_componsable_style_window_resize2_variable_componsable_fn,
  // project_saicheng_componsable_layout_rightinfo_variable_componsable_fn,
} from 'src/output/common/componsable-common.js';
import { create_base_state_instance_fn } from 'project/caopan/components/query/select_query_period3/variable/index.js';

export const project_caopan_components_query_select_query_period3_select_query_period3_variable_componsable_fn = () => {
  const global_obj = src_componsable_global_variable_componsable_fn();
  const base_state = create_base_state_instance_fn();

  // ---------- 以下是 共用逻辑的variable，有用到在加------------

  // const table_base_config_state = project_saicheng_componsable_table_base_config_variable_componsable_fn();
  // // 查询常量
  // const constant_query_state = project_saicheng_componsable_constant_query_variable_componsable_fn();
  // const match_selected_table_item_state = project_saicheng_componsable_pages_match_selected_table_item_variable_componsable_fn();
  // // 表格查询页面的通用参数生成  逻辑组合
  // const table_query_state = project_saicheng_componsable_table_table_query_variable_componsable_fn();
  // const pages_match_match2_state = project_saicheng_componsable_pages_match_match2_variable_componsable_fn();
  // const router_cache_state = project_saicheng_componsable_router_cache_handler_variable_componsable_fn();
  // const style_window_resize_state = project_saicheng_componsable_style_window_resize2_variable_componsable_fn();
  // const layout_rightinfo_state = project_saicheng_componsable_layout_rightinfo_variable_componsable_fn();
  // ---------- 以上是 共用逻辑的variable，有用到在加------------

  const payload = {
    ...base_state,
    ...global_obj,
    // 一下 其他按照template需求引入
    // tablecolumns_config,
    // status_arr,
    // current_match_level_constant,
    // rightdetail_tooltip,
    // affiliation_list,
    // ...table_base_config_state,
    // ...table_query_state,
    // ...constant_query_state,
    // ...match_selected_table_item_state,
    // ...pages_match_match2_state,
    // ...router_cache_state,
    // ...style_window_resize_state,
    // ...layout_rightinfo_state
  };

  return {
    ...global_obj,
    ...base_state,
  };
};
