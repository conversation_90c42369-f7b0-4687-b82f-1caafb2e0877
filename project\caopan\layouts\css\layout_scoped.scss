.q-header{
    z-index: 100;
  }
  .red-back {
    top: 0;
    right: 0;
    background-color: #f44336;
    padding: 3px;
    border-radius: 10px;
    width: 30px;
    height: 15px;
    line-height: 10px
  }
  .seamless-warp {
    height:40px;
    overflow: hidden;
  }
  .line-height-40px{
    height:40px;
    line-height:40px;
  }
  // 语言切换 go
  // 赔率 go
   :deep( .q-field__control){
    padding: 0 12px
  }
   :deep( .q-field__control:hover){
    border: 1px solid var(--q-color-panda-border-primary);
  }
   :deep( .q-field--outlined .q-field__control:after){
    display: none
  }
   :deep( .q-field--outlined .q-field__control:before){
    display: none
  }
   :deep( .q-field--filled .q-field__control:hover:before){
    opacity: 0
  }
   :deep( .q-field--focused .q-field__control:hover:before){
    opacity: 0
  }
  :deep( .q-scrollarea__thumb){
    display: none
  }
  // 语言切换 end
  // 赔率 end
  .overflow-hide{
    width:60px;
    display:inline-block;
    overflow:hidden;
  }
  .panda_icon_them_btn{
    display: inline-block;
    position :relative;
    font-size: 24px;
    width: 24px;
    text-align: center;
    height: 24px;
    line-height: 24px;
    background:#1b212c !important;
  }
  .check-dropdown .panda_icon_them_btn {
    background:#1b212c !important;
  }
  .panda_icon_them_btn:hover,.login-user-info:hover{
    border-radius: 2px;
    background: #28303F !important;
  }
  .login-user-info{
    color:#788299
  }
  .sport_img{
    width :14px;
    height :41px;
    line-height :46px;
    margin-left: 10px;
  }
  .bg-panda-linear-black{
    background-image: linear-gradient(#25EDD7, #4EA1FF);
  }
  .fixed-menu {
    position: fixed;
    // border-right: 1px solid rgba(0, 0, 0, 0.12);
    left: 0;
    display: block;
    top: 0;
    bottom: 0;
    background: #fff;
    z-index: 1000;
  }
  
  .fixed-menu-2 {
    position: fixed;
    // border-right: 1px solid rgba(0, 0, 0, 0.12);
  
    left:60px;
    display: block;
    z-index: 100000;
  }
  
  .panda-wifi-online {
    color: green;
    -webkit-text-fill-color: green;
  }
  
  .panda-q-item-level-one {
    border-right: 2px solid $panda-base-light;
    border-right: 2px solid var(--q-color-panda-border-color-primary);
    border-left: 2px solid $panda-base-light;
    border-left: 2px solid var(--q-color-panda-border-color-primary);
  }
  
  .panda-wifi-offline {
    color: grey;
    -webkit-text-fill-color: gray;
  }
  
  .panda-head-span {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #788299;
    float: right;
    margin: 10px 3px;
  }
   :deep( .q-item:hover ){
    background-color: $panda-color-secondary;
    background-color: var(--q-color-panda-secondary);
  }
  
  body.desktop .layout__menu-main.q-hoverable:hover {
     :deep( .q-focus-helper ){
      opacity: 0;
    }
  
     :deep( [class^='panda_']:before ){
      color: $panda-color-primary;
      color: red;
      -webkit-text-fill-color: $panda-color-primary;
      -webkit-text-fill-color: var(--q-color-panda-primary);
    }
  
    :deep( .panda-icon-base ){
      color: $panda-color-primary;
      color: var(--q-color-panda-primary);
      -webkit-text-fill-color: $panda-color-primary;
      -webkit-text-fill-color: var(--q-color-panda-primary);
    }
  }
  .menu-sub{
    line-height: 14px;
    padding: 13px 0 13px 10px;
    }
  .menu-sub:hover {
     :deep( .q-focus-helper ){
      opacity: 0 !important;
    }
  
    :deep( .q-item__section) {
      [class^='panda_'] {
        color: $panda-color-primary;
        color: var(--q-color-panda-primary);
        -webkit-text-fill-color: $panda-color-primary;
        -webkit-text-fill-color: var(--q-color-panda-primary);
      }
    }
    .panda-level-label{
      &:after {
          background: $panda-color-primary;
          background :var(--q-color-panda-primary);
        }
    }
  }
  .check-dropdown  :deep(.ivu-select-dropdown){
    text-align: right;
    background: #28303F !important;
  }
  
  .check-dropdown .ivu-dropdown-item{
    color : var(--q-color-panda-them--788299)
  }
  .ivu-icon-ios-checkmark{
    font-size : 16px
  }
  .login-user .ivu-dropdown-item{
    line-height : 100%
    color var(--q-color-panda-them--788299)
  }
  .ivu-dropdown-item{
    font-size :12px !important
  }
   :deep( .ivu-dropdown-item:hover){
    background-color: rgba(0,170,152,0.1) !important
  }
  .login-user  :deep( .ivu-select-dropdown){
       background: #242b38 !important;
  }
   :deep( .q-field--filled.q-field--rounded .q-field__control){
    height: 26px
  }
   :deep( .change_font.q-field--filled.q-field--rounded .q-field__control){
   border-color:#404758;background: #28303f;
   .q-field__native{
   color: #d5dee7;
   }
  }
  @media screen and (max-height: 750px) {
    .setting {
      display: none
    }
  }
  
  .myok_2{
          animation:myfirst 1s infinite linear;
  }
  @keyframes myfirst{
    from {opacity:1;}
    to {opacity:0.1;}
  }
    .seamless{
      li{
        max-width :700px
      }
      .text{
        display :inline-block;
        height: 25px;
        max-width :620px;
        overflow :hidden;
        text-overflow: ellipsis;
        white-space:nowrap;
      }
    }
  .menu-dot{
    vertical-align: top;
  }
  .panda-level-label{
    position relative
    &:after {
        content :'';
        position :absolute;
        top:5px;
        left: 0px;
        height:4px;
        width:4px;
        background :#ffffff;
        border-radius:2px;
      }
  }
   :deep( .q-drawer ){
    background: var(--q-color-panda-base-light)!important;
  }
  .mode {
    padding-right: 10px;
  }