import { api_user_report } from "src/api/index.js";
import { Message, lodash } from "src/output/common/project-common.js";
const get_user_report_ok = (payload, res) => {
  let { code, data, msg } = lodash.get(res, "data");
  if (code == 200) {
    let userTags = data.map((item) => {
      return Object.assign({
        ...item,
        ...{
          label: item.tagName,
          value: item.id,
        },
      });
    });
    userTags.value = userTags;
  } else {
    Message.error(`${msg}`);
  }
};

//获取标签数据。不分页的
export const get_user_report = async (payload) => {
  try {
    let res = await api_user_report.get_listByType({ tagType: 2 });
    get_user_report_ok(payload, res);
  } catch (error) {
    console.log(error);
    Message.error(`${error}`);
  }
};
