<!--
 * @FilePath: /project/caopan/pages/trader/manage/module/table_row/module/common/match_info.vue
 * @Description: 赛事信息（主客队名，比分，等）
 * @Author: tony
-->

<template>
  <div
    v-if="props.row != null"
    :class="props.row.memoIds ? 'headerBox' : ''"
    class="full-height"
  >
    <matchInfoHeader
      :props="props"
      :col_width="col_width"
      :is_special="true"
      v-if="specialHeaderVisible"
    ></matchInfoHeader>
    <matchInfoHeader :props="props" :col_width="col_width"></matchInfoHeader>
    <div class="padding">
      <div class="padding2">
        <!-- 主客队名-->
        <div :style="{ maxWidth: col_width }" class="first-line">
          <div class="mr10x">
            <p
              class="auto home_color Roboto team_wrap"
              style="display: flex; align-items: center"
              v-for="(item, index) in props.row.teamList"
              :key="`team_list_${index}`"
              @click.stop="copy(props.row.matchManageId)"
            >
              <!-- 发球方 -->
              <span
                class="score two_line"
                :class="
                  props.row.servesFirst == item.matchPosition ? 'green' : ''
                "
                v-if="props.row.servesFirst == item.matchPosition"
              >
                <img
                  style="display: inline-block; vertical-align: middle"
                  width="10"
                  height="10"
                  src="/image/sport/panda_img_sport_pq.svg"
                  alt="volleyball"
                />
              </span>
              <span class="score opacity" v-else>●</span>
              <!-- 主客队名称 -->
              <span :class="let_the_ball(index)" class="pointer ellipsis">
                {{
                  computed_team_list_name({
                    item,
                    show_which: props.row.neutralGround,
                  }).tite
                }}
                <q-tooltip
                  anchor="top middle"
                  self="top middle"
                  v-if="
                    computed_team_list_name({
                      item,
                      show_which: props.row.neutralGround,
                    }).sizi > 6
                  "
                  >{{
                    computed_team_list_name({
                      item,
                      show_which: props.row.neutralGround,
                    }).tite
                  }}</q-tooltip
                >
              </span>
              <span class="denger_number"  v-if="getDangerTermLevel(index) > 0">
                {{getDangerTermLevel(index)}}
                <q-tooltip>
                  {{
                    i18n_t("football_the_detail.football_danger_team_hint", {
                      level: getDangerTermLevel(index),
                    })
                  }}
                </q-tooltip>
              </span>
            </p>
          </div>

          <div class="scores">
            <!-- 局比分 setScore -->
            <template v-for="(item, index) in computed_set_by_id(props.row)">
              <div
                class="home_color Roboto"
                :class="[computed_set_by_id(props.row).length == 1 ? 'pr' : '']"
                v-if="computed_props_row_scoreVos(props.row)"
                :key="'full' + index"
              >
                <div style="width: 25px">
                  <!-- 主队局比分逻辑 -->
                  <div class="two_line match_score" style="text-align: right">
                    <span class="score_bottom">
                      {{
                        computed_score_set(item) != "0:0" &&
                        computed_score_set(item) != null
                          ? $lodash.split(computed_score_set(item), ":", 3)[0]
                          : "--"
                      }}
                    </span>
                  </div>
                  <!-- 客队局比分逻辑 -->
                  <p
                    class="two_line match_score position-relative"
                    style="text-align: right"
                  >
                    <span class="score_bottom">
                      {{
                        computed_score_set(item) != "0:0" &&
                        computed_score_set(item) != null
                          ? $lodash.split(computed_score_set(item), ":", 3)[1]
                          : "--"
                      }}
                    </span>
                  </p>
                </div>
              </div>
            </template>
          </div>

          <!-- 盘比分  matchScore -->
          <div
            class="inline-block home_color Roboto pr"
            v-if="
              computed_props_row_scoreVos(props.row) &&
              computed_set_score_show(props.row)
            "
            style="text-align: right"
          >
            <div style="width: 10px">
              <p class="two_line set_score">
                {{ $lodash.split(props.row.score, ":", 3)[0] || "0" }}
              </p>
              <p class="two_line set_score">
                {{ $lodash.split(props.row.score, ":", 3)[1] || "0" }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <p :style="{ maxWidth: col_width }" class="auto block height22">
      <!--收藏/热门-->
      <template v-if="props.row.categorySetId == main_categoryset_id">
        <span
          class="icons panda_new_cp_icon_h verticalTB mr10x ml10x"
          v-if="props.row.traderStatus"
        ></span>
        <span
          v-else
          class="pointer icons verticalTB mr10x"
          :class="
            props.row.matchCollectStatus
              ? 'panda_icon_sc_active'
              : 'panda_icon_sc'
          "
          @click.stop="collection(props.row, props)"
        ></span>
      </template>

      <!--+盘口-->
      <template v-if="props.row.setInfos && props.row.setInfos.length > 0">
        <iTooltip
          class="mr10x"
          v-if="props.row.categorySetId == main_categoryset_id"
          placement="bottom-start"
        >
          <span
            class="border_text"
            :class="text_color2(props.row.categorySetId)"
            >+{{ i18n_t("expectations.text9") }}</span
          >
          <ul class="api" #content>
            <li
              v-for="(item, index) in props.row.setInfos"
              :key="`setInfos_${index}`"
            >
              <q-checkbox
                :value="categoryset_show_arr.indexOf(item.unid) != -1"
                @input="togglePlay({ unid: item.unid })"
              ></q-checkbox>
              <span>{{ stage_obj[item.catgorySetId] }}</span>
            </li>
          </ul>
        </iTooltip>
      </template>
      <span
        v-else
        class="border_text mr10x"
        :class="text_color2(props.row.categorySetId)"
      >
        +{{ i18n_t("expectations.text9") }}
      </span>
      <!--玩法个数-->
      <span
        v-if="props.row.categorySetId == main_categoryset_id"
        class="border_text"
        :class="text_color2(props.row.categorySetId)"
        @click.stop="all_play(props.row)"
      >
        +{{ props.row.categoryCount || 0 }}
      </span>
      <!--隐藏-->
      <span
        v-if="props.row.categorySetId != main_categoryset_id"
        class="border_text mr10x ml10x"
        :class="text_color2(props.row.catgorySetId)"
        @click.stop="togglePlay({ unid: props.row.unid })"
        >{{ i18n_t("traderTable.text100") }}</span
      >
      <!-- 比分合计版 -->
      <span
        v-if="computed_props_row_scoreVos(props.row)"
        class="inline-block text-panda-text-white fr mr15x"
      >
        <span>{{ computed_props_row_scoreVos_score(1) }}</span>
        <span class="ml10x"
          >{{ i18n_t("expectations.diff") }}(<span class="panda-text-red">{{
            computed_props_row_scoreVos_score(2)
          }}</span
          >)</span
        >
        <span class="ml10x"
          >{{ i18n_t("expectations.total") }}(<span class="panda-text-red">{{
            computed_props_row_scoreVos_score(3)
          }}</span
          >)</span
        >
      </span>
    </p>
  </div>
</template>
<script setup>
    import { defineAsyncComponent } from "vue";
    const matchInfoHeader = defineAsyncComponent(() => {
        import("project/caopan/pages/trader/manage/module/table_row/module/common/match_info_header.vue")
    })
    const props = defineProps({
        props: {
            type: Object,
            default() {
                return {};
            },
        },
        col_width: {
            type: String,
            default: "100%",
        },
    })
</script>

<!-- <script>
import { mapGetters, mapMutations, mapActions } from "vuex";

import { SessionStorage } from "quasar";
let i18n2 = SessionStorage.getItem("i18n") || "zs";
import alllanguage from "project/caopan/mixins/language/alllanguage.js";
import { api_trader } from "src/api/index.js";
import { i18n } from "src/boot/common/i18n";
import formartmixin from "project/caopan/mixins/module/formartmixin.js";

import { set_periods_map } from "project/caopan/pages/trader/manage/config/match_config.js";
import matchInfoHeader from "project/caopan/pages/trader/manage/module/table_row/module/common/match_info_header.vue";
import {sportId_tab_name} from 'project/caopan/pages/sets/config/config.js'

export default {
  components: { matchInfoHeader },
  mixins: [alllanguage, formartmixin],
  props: {
    props: {
      type: Object,
      default() {
        return {};
      },
    },
    col_width: {
      type: String,
      default: "100%",
    },
  },
  computed: {
    ...mapGetters([
      "main_categoryset_id",
      "categoryset_show_arr",
      "stage_obj",
      "liveOddBusiness",
      "get_market_odds_kind",
      "get_volume_switch_status"
    ]),
    export const specialHeaderVisible = (payload) => {
      const { categorySetId, matchId, sportId } = props.value.row;

      // 玩法集显隐数组是该玩法集合的uuid(matchId+'-'+categorySetId)组成的，因为所有赛事的玩法集合都存储在这里数组里面，
      // 所以需要根据matchId做筛选到当前赛事的玩法集合显隐
      let cur_categoryset_show_arr = categoryset_show_arr.value.filter((item) =>
        item.includes(matchId)
      );

      // 副玩法集展开个数大于0并且是第一个副玩法集
      if (cur_categoryset_show_arr.length > 0) {
        cur_categoryset_show_arr.sort();
        const first_categoryset_id = cur_categoryset_show_arr[0].split("-")[1];
        if (first_categoryset_id == categorySetId) {
          return true;
        }
      }

      return false;
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    ...mapMutations(["set_categoryset_show_arr"]),
    // 获取危险球队联赛等级
    export const getDangerTermLevel = (payload,index) => {
      const { teamList } = props.value.row;
      if (teamList.length >= 2) {
        const team = teamList[index];
        if (team.dangerTeam && team.dangerTeam.status == 1) {
          return team.dangerTeam.riskLevel ? team.dangerTeam.riskLevel : 0;
        }
      }
      return 0;
    },
    export const all_play = (payload,row) => {
      //所有玩法弹窗
      // console.log('row-----------------',row)
      let targetTabs = sportId_tab_name[row.sportId][liveOddBusiness.value == 1 ? 0 : 1]
      console.log(2333, row.sportId, liveOddBusiness.value, targetTabs);
      let ciyao = 0
      for (let i = 0; i < targetTabs.length; i++) {
        let tab = targetTabs[i]
        console.log(2333, tab);
        if (btn_permissions('sets_page:'+tab) || ((tab.includes('Play') || tab.includes('play')) && btn_permissions('sets_page:secondaryPlay'))) {
          ciyao = ciyao + 1
        } else {
          console.log(2333, 302, tab, '无次要');
        }
      }
      if (!ciyao) return
      
      let obj_language = {
        tournamentNames: rebuild_to_language_obj(row.tournamentNames),
      };
      let name = compute_table_item_show_name(
        obj_language,
        i18n2,
        "tournamentNames"
      );
      const { href } = router.resolve({
        name: "sets",
        path: "/sets",
        query: {
          // 公用参数
          sportId: row.sportId,
          matchId: row.matchId, //【比分矩阵、事件、结算审核】
          // 及时注单参数--------
          // 玩法ID
          playIds: "",
          // row.categoryIds.join(','),
          type: 2,
          // 盘口类型 1 滚球 否则为早盘
          liveType: liveOddBusiness.value == 1 ? 1 : "",
          // 事件页面参数-----
          teamList: JSON.stringify(row.teamList),
          // 足球次要玩法参数添加
          // matchVolumeId: queryform_form.value.quantity_type,
          set_market_odds_kind: get_market_odds_kind.value,
          standardTournamentId: row.standardTournamentId, //结算审核用到的参数
          matchType: liveOddBusiness.value == 1 ? 2 : 1,
          matchSnapshot: row.matchSnapshot,
          tournamentId: row.standardTournamentId, //比分矩阵用到的参数
          name, //比分矩阵用到的参数
          matchDate: row.matchStartDate, //比分矩阵用到的参数
          matchManageId: row.matchManageId, //比分矩阵用到的参数
          categorySetId: row.categorySetId, //赛事阶段id
          score: row.score, //比分
          // index: odd_index.value, //位置
          index: 1,
          neutralGround: row.neutralGround, //是否为中立场。取值为 0  和1  。  1:是中立场，0:非中立场。操盘人员可手动处理
          period: row.period, // 当前赛事阶段 篮球专用，足球则没有
          secondsMatchStart: row.secondsMatchStart, // 备忘录专用
          // dataSourceCode: row.dataSourceMap['37'] || row.dataSourceMap['38'] // 数据源
          volume_switch_status: get_volume_switch_status.value, // 是否藏单
        },
      });
      window.open(
        href,
        "_blank",
        "height=700, width=1900, top=100,left=250, toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no"
      );
    },
    export const rebuild_to_language_obj = (payload,arg) => {
      let return_obj = {};
      if (arg && Array.isArray(arg)) {
        arg.forEach((x) => {
          x.languageType && (return_obj[x.languageType] = x.text);
        });
      }
      if (arg && typeof arg == "object") {
        let keys = Object.keys(arg);
        keys.forEach((x) => {
          let item = arg[x];
          item.languageType && (return_obj[item.languageType] = item.text);
        });
      }
      return return_obj;
    },
    /**
     * @description 盘比分只显示在全场比分这里
     * @return {undefined} undefined
     */
    export const computed_set_score_show = (payload,row) => {
      const num_of_set = row.categorySetId % 10000; // 第几局
      if (num_of_set == 1) {
        return true;
      } else {
        return false;
      }
    },
    /**
     * @description 根据categorySetId返回不同的阶段数组
     * @param  {Object} row 表格行数据对象
     * @return {Array} 返回不同的阶段数组
     */
    export const computed_set_by_id = (payload,row) => {
      const { sportId, categorySetId, roundType } = row;
      const set_periods = set_periods_map[sportId] || [];
      const num_of_set = categorySetId % 10000; // 第几局
      if (num_of_set == 1) {
        return set_periods.slice(0, roundType);
      } else {
        return [set_periods[num_of_set - 2]];
      }
    },
    /**
     * @description 根据盘数取对应的比分
     * @return {undefined} undefined
     */
    export const computed_score_set = (payload,period) => {
      const { scoreVos } = props.value.row;
      if (scoreVos) {
        const item = scoreVos.find((item) => item.period == period);
        if (item) {
          return item.setScore;
        }
      }
    },
    export const computed_props_row_scoreVos = (payload,row) => {
      if (
        row.scoreVos &&
        liveOddBusiness.value == 1 &&
        row.matchSnapshot === 0 &&
        row.period > 0
      ) {
        return true;
      } else {
        return false;
      }
    },
    /**
     * @description 合计行比分计算
     * @param  {Array} scoreVos 各个阶段的比分
     * @param  {Number} type 总比分 差 总
     * @return {String} 9-8 1 17
     */
    export const computed_props_row_scoreVos_score = (payload,type) => {
      const { categorySetId, sportId, scoreVos } = props.value.row;
      const num_of_set = categorySetId % 10000; // 第几局
      const set_periods = set_periods_map[sportId] || [];
      const period = set_periods[num_of_set - 2];
      let scoreVosList = [];
      if (num_of_set == 1) {
        scoreVosList = scoreVos;
        // console.log("scoreVosList", scoreVosList);
      } else {
        scoreVosList = scoreVos.filter((item) => item.period == period);
      }

      let sum1 = 0,
        sum2 = 0;

      scoreVosList.forEach((item) => {
        const [score1, score2] = !item.setScore
          ? [0, 0]
          : lodash.split(item.setScore, ":", 3);
        sum1 += Number(score1);
        sum2 += Number(score2);
      });
      if (type == 1) {
        return `${sum1}-${sum2}`;
      } else if (type == 2) {
        return Math.abs(sum1 - sum2);
      } else {
        return sum1 + sum2;
      }
    },
    // 控制玩法集显隐
    export const togglePlay = (payload,{ unid }) => {
      const mArr = [...categoryset_show_arr.value];
      let index = mArr.indexOf(unid);
      if (index == -1) {
        mArr.push(unid);
      } else {
        mArr.splice(index, 1);
      }
      set_categoryset_show_arr(mArr);
      // sessionStorage.setItem("categoryset_show_arr", JSON.stringify(mArr));
    },
    // 收藏
    export const collection = (payload,row, type) => {
      if (row.matchCollectStatus) {
        let data = {
          sportId: select_sportId.value,
          matchId: row.matchId,
          userId: 1,
          status: 0,
          type: 1,
        };
        update_collect(data, false); //删除我的收藏
      }
    },

    //收藏请求
    export const update_collect = (payload,data, Status) => {
      api_trader.updateCollect(data).then((res) => {
        let code = lodash.get(res, "data.code");
        if (code === 200) {
          Message.success(
            Status
              ? `${i18n_t("traderTable.text101")}${res.data.msg}`
              : i18n_t("traderTable.text102")
          );
          //   props.value.row.matchCollectStatus = Status;
          emit("update_collect", Status, data, 3); //删除收藏的联赛
        } else {
          Message.error(`${res.data.data}`);
        }
      });
    },

    export const let_the_ball = (payload,index) => {
      let handicapIds = [172, 176]; // 让球ids
      //让球方显示红色字体
      let matchMarketVoList = [];
      handicapIds.forEach((id) => {
        const item = props.value.row[id];
        if (item) {
          matchMarketVoList = item.matchMarketVoList;
        }
      });
      if (matchMarketVoList.length == 0) return null;
      let data;
      if (
        matchMarketVoList &&
        matchMarketVoList.length &&
        matchMarketVoList[0].oddsFieldsList
      ) {
        for (var i = 0; i <= matchMarketVoList[0].oddsFieldsList.length; i++) {
          if (matchMarketVoList[0].oddsFieldsList[index]) {
            data =
              matchMarketVoList[0].oddsFieldsList[index].nameExpressionValue;
            if (Number(data) < -0.1) {
              return "let_the_ball";
            }
          }
        }
      }
    },

    export const copy = (payload,tiem) => {
      //复制日期单号
      let oInput = document.createElement("input");
      oInput.value = tiem;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;
      document.execCommand("Copy");
      Message.success(`${i18n_t('champion_plays.text69')}${tiem}`);
      oInput.remove();
    },

    export const computed_team_list_name = (payload,arg) => {
      const { item, show_which } = arg;
      let suffix = "";
      if (item && typeof item == "object" && item.matchPosition === "home") {
        if (show_which === 1) {
          //  中立场
          suffix = `(${i18n_t("traderTable.text103")})`;
        } else {
          // 主
          suffix = `(${i18n_t("common.home")})`;
        }
      }
      let names = item.names;
      let name = item.names;
      if (names && typeof names == "object") {
        name = compute_table_item_show_name(item, i18n2, "names");
      }
      name = name || "";
      return {
        tite: name + suffix,
        sizi: (name + suffix).length,
      };
    },

    export const text_color2 = (payload,categorySetId) => {
      //字体颜色
      if (categorySetId == main_categoryset_id.value) {
        return "";
      } else {
        return "yellow";
      }
    },
    export const text_color = (payload,categorySetId) => {
      //字体颜色
      if (categorySetId == main_categoryset_id.value) {
        return "blue_color";
      } else {
        return "yellow_color";
      }
    },
    export const text_bg = (payload,categorySetId) => {
      //文案背景色
      if (categorySetId == main_categoryset_id.value) {
        return "blue_bg";
      } else {
        return "yellow_bg";
      }
    },
    export const text_bg2 = (payload,categorySetId) => {
      //文案背景色
      if (categorySetId == main_categoryset_id.value) {
        return "blue_bg2";
      } else {
        return "yellow_bg2";
      }
    },

    //操盘模式图标
    //返回操盘方式 图标
    // panda_new_cp_icon_11  0,null   A   自动操盘
    // panda_new_cp_icon_10  1        M	  手动操盘
    // panda_type6	         2	      A+	自动加强操盘
    // panda_new_cp_icon_13  0,1      M+A
    // panda_type1	         0,2	    A+A+
    // panda_type3	         1,2	    M+A+
    // panda_type2	         0,1,2	  M+A+A+
    export const get_icon = (payload,tradeType) => {
      if (String(tradeType) == "null" || String(tradeType) == "undefined") {
        return "panda_new_cp_icon_11";
      }
      if (typeof tradeType === "number") {
        switch (tradeType) {
          case 0:
            return "panda_new_cp_icon_11";
          case 1:
            return "panda_new_cp_icon_10";
          case 2:
            return "panda_type6";
        }
      }
      if (Array.isArray(tradeType)) {
        if (tradeType.length == 1) {
          switch (tradeType[0]) {
            case 0:
              return "panda_new_cp_icon_11";
            case 1:
              return "panda_new_cp_icon_10";
            case 2:
              return "panda_type6";
          }
        } else if (tradeType.length == 3) {
          return "panda_type2";
        } else {
          if (tradeType.indexOf(0) > -1) {
            if (tradeType.indexOf(1) > -1) {
              return "panda_new_cp_icon_13";
            } else {
              return "panda_type1";
            }
          } else {
            return "panda_type3";
          }
        }
      }
    },
  },
};
</script> -->
<style lang="scss" scoped>
@import url("project/caopan/pages/trader/manage/volleyball/component/match_info/css/index.scss")
</style>

