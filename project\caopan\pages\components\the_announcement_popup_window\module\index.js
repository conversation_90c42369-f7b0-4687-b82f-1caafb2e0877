
import { nextTick } from 'vue'
import { lodash, Message } from "src/output/common/project-common.js"


export const home_away_change = (payload, val) => {
  const { language_type, home_away, default_language_type } = payload
  language_type.value = lodash.cloneDeep(default_language_type)
  home_away.value = val
  change_template()
}
// 切换模板
export const change_template = (payload) => {
  const {
    query,
    league_factor,
    league_team,
    language_type,
    default_language_type,
    obj_settlement_v2,
    is_show_time,
    is_from_C01,
    list_select_type,
    auto_notice_config,
    hide_time_type_list,
    auto_notice_config_1,
    C01_hide_time_type_list,
  } = payload
  query.value.stage_type = ''
  query.value.game_type = ''
  query.value.reschedule_time = null
  query.value.error_home_result = ''
  query.value.error_away_result = ''
  query.value.home_result = ''
  query.value.away_result = ''
  query.value.team_type = false
  league_factor.value = false
  league_team.value = false
  language_type.value = lodash.cloneDeep(default_language_type)
  if (obj_settlement_v2.value.v2) {
    set_list_data_v2(payload)
    set_stage_games_template()
    return
  }
  // 产品要求改成14场景模板都需要时间
  is_show_time.value = auto_notice_config.includes(Number(props.row?.cancel_num)) && !hide_time_type_list.value.includes(list_select_type.value)
  set_reset(payload)
  set_show_type(payload)
  set_list_data(payload)
  if (is_from_C01.value) {
    set_list_data_1(payload)
    is_show_time.value = auto_notice_config_1.value.includes(Number(props.row?.cancel_num)) && C01_hide_time_type_list.value.includes(list_select_type.value)
    console.log(is_show_time.value, 'is_show_time');

    // 需求3915产品pooh要求C01只抓取5个
    if (['han_you', 'incorrect_score_amidithion'].includes(list_select_type.value)) {
      // 固定选中5个主玩法 318870全场独赢 318868全场大小 318864全场让球 318844半场大小 318842半场让球
      let index = [6, 1, 7, 3, 2] // 固定写死下标选中
      index.forEach(item => {
        action_selected_playid(0, item);
      })
    }
  }
}
// 控制中间部分的显示 和隐藏
export const set_show_type = (payload) => {
  const {
    home_away,
    show_type,
    list_select_type,
  } = payload
  Object.keys(show_type.value).forEach(item => {
    show_type.value[item] = false
  })
  if ([36, 39].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('correct_team') > -1) || (list_select_type.value.indexOf('home_away') > -1)) {
      show_type.value.incorrect_team = true
      home_away.value = ''
    }
  }
  if ([38].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('incorrect_score') > -1)) {
      show_type.value.incorrect_score = true
    }
  }
  if ([37].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('incorrect_league') > -1)) {
      show_type.value.incorrect_league = true
    }
  }
  if ([33].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('com_rules') > -1)) {
      show_type.value.com_rules = true
    }
  }
  if ([40].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('course_') > -1)) {
      show_type.value.course_info = true
    }
  }
  if ([41].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('event_') > -1)) {
      show_type.value.event_info = true
    }
  }
  if ([28].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('evmidway_forgo') > -1)) {
      show_type.value.evmidway_forgo = true
    }
  }
  if ([30].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('statistics_') > -1)) {
      show_type.value.statistics_info = true
    }
  }
  if ([23, 25].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('unable_result') > -1) || (list_select_type.value.indexOf('player_lost') > -1)) {
      show_type.value.unable_result = true
    }
  }
  if ([31].includes(Number(props.row.cancel_num))) {
    if ((list_select_type.value.indexOf('pitcher_change') > -1)) {
      show_type.value.pitcher_change = true
    }
  }
}
// 填写项数据重置
export const set_reset = (payload) => {
  const { query } = payload
  query.value = {
    home_score: "", // 主队比分
    away_score: '', //客队比分
    cause: "", // 取消原因
    the_right_team: "",  //正确队伍资讯
    start_time: '',  //查询开始时间 含时间区间
    end_time: '', // 查询结束时间  含时间区间
    play_name: "", // 玩法名称
    session_info: '', // 场次资讯
    score_figure: "", // 比分数字
    league_info: '', // 联赛资讯
    the_right_league: "",  //正确联赛资讯
    home_away: "", // 主客场资讯
    com_rules_info: "", //赛制资讯
    course_info: "", // 赛程资讯
    event_info: '', // 事件资讯
    player_name: "", // 选手名称
    evmidway_forgo_info: "", // 弃赛原因
    statistics_info: '', //统计资讯
    factor: "", // 取消因素
    pitcher_before: "", // 变更前投手名称
    pitcher_after: "", // 变更后投手名称
  }
}

export const set_list_data = (payload) => {
  const { auto_notice_config, props, language_type, cancel_data, list_saishi, list_data, marketname, list_data_copy } = payload
  if (auto_notice_config.includes(props.row.cancel_num)) {
    language_type.value.forEach(item => {
      const cancelCause = cancel_data[props.row.cancel_num][item.value]
      let type = list_saishi.value[props.row.sportId][item.value]
      list_data.value[item.value] = {
        title: `【${type}】${cancelCause}`,
        content: set_notice(item.value, cancelCause),
        marketname: marketname.value ? marketname.value[item.value] : ''
      }
    })
    list_data_copy.value = { ...list_data.value }
  } else {
    // 生成 时间数据 1 生成 2取消
    generate_template(payload, 1)
  }
}
// 现实编辑输入框
export const set_show_input = (payload, bool) => {
  const { no_show_input } = payload
  no_show_input.value = bool
}
// 关闭弹窗
export const close_cmp = (payload) => {
  const { emit } = payload
  emit('set_close', false)
}
// 取消编辑
export const cancel_alter = (payload) => {
  const { list_data, list_data_copy } = payload
  list_data.value = { ...list_data_copy.value }
  set_show_input(payload, true)
}
// 设置生成模板
export const generate_template = (payload, type = 1) => {
  const { list_select_type, times, list_select_value, props } = payload
  // 是指取消生成带时间公告
  if (type == 2) {
    // 取消模板时，默认清空时间
    times.value = {
      val1: '', // 开始时间
      val2: '', // 结束时间
      val3: '', // 滚球时间
    }
    set_title_and_text(payload,list_select_type.value, props.row.cancel_num, props.row.sportId)
    return
  }
  // 这里时生成对应模板 判断是否设置时间
  if (list_select_value.value == "tong_sai" ? times.value.val3 && times.value.val1 && times.value.val2 : times.value.val1 && times.value.val2) {
    // 获取选中时间模板对应值
    let str = get_select_value(payload)
    if (!str) return
    set_title_and_text(payload, str, props.row.cancel_num, props.row.sportId)
  } else {
    // 生成没有时间的模板
    set_title_and_text(payload, list_select_type.value, props.row.cancel_num, props.row.sportId)
  }
}
// 设置选中模板
export const set_select = (payload, val) => {
  const { is_show_time, times, no_start_time_type_list } = payload
  // 赛事提前没有开始结束时间，只有滚球时间
  is_show_time.value = !no_start_time_type_list.includes(val)
  // 选择对应模板时，默认清空时间
  times.value = {
    val1: '', // 开始时间
    val2: '', // 结束时间
    val3: '', // 滚球时间
  }
  set_title_and_text(payload, val, props.row.cancel_num, props.row.sportId)
}

export const get_sport_id = (payload) => {
  const { props, route } = payload
  let res_sport_id = ''
  const { name, query } = route
  if (name == 'settlements') { // 结算1.0 结算审核
    res_sport_id = query.sportId
  } else if (name == 'event_review') { // 结算1.0 事件审核
    res_sport_id = query.spid
  } else {
    res_sport_id = props.row.sportId || '' // 结算2.0  冠军玩法 取消注单等
  }
  if (res_sport_id == '') {
    console.error('注意: 赛种id丢失');
    Message.error('注意: 赛种id丢失')
  }
  return res_sport_id
}

// 设置对应多语言标题和内容
export const set_title_and_text = (payload, str_key = 'tong_wu', cancel_num = 7, saishi_num = 1) => {
  const { list_data, cancel_data, other_data, allocation, marketname, list_data_copy,list_saishi,data_text, props } = payload
  // 统一配置且是字符串，需要用eval把改字符串运行并返回最后字符串
  list_data.value = {
    zs: { // 【${list_saishi.value[saishi_num].zs}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.zs : `${cancel_data[cancel_num].zs}`,
      content: allocation[str_key]?.obj?.zs,  //eval把改字符串运行并返回最后字符串
      market: marketname.value?.zs || '' // 取出 玩法名称 中英文
    },
    en: { // 【${list_saishi.value[saishi_num].en}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.en : `${cancel_data[cancel_num].en}`,
      content: allocation[str_key]?.obj?.en,  //eval把改字符串运行并返回最后字符串
      market: marketname.value?.en || '' // 取出 玩法名称 中英文
    },
    yn: { // 【${list_saishi.value[saishi_num].yn}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.yn : `${cancel_data[cancel_num].yn}`,
      content: allocation[str_key]?.obj?.yn,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.yn || marketname.value?.en // 取出 玩法名称 中英文
    },
    zh: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.zh : `${cancel_data[cancel_num].zh}`,
      content: allocation[str_key]?.obj?.zh,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.zh || marketname.value?.zs // 取出 玩法名称 中英文
    },
    th: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.th : `${cancel_data[cancel_num].th}`,
      content: allocation[str_key]?.obj?.th,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.th || marketname.value?.en // 取出 玩法名称 中英文
    },
    bm: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.bm : `${cancel_data[cancel_num].bm}`,
      content: allocation[str_key]?.obj?.bm,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bm || marketname.value?.en // 取出 玩法名称 中英文
    },
    bi: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.bi : `${cancel_data[cancel_num].bi}`,
      content: allocation[str_key]?.obj?.bi,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bi || marketname.value?.en // 取出 玩法名称 中英文
    },
    hin: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.hin : `${cancel_data[cancel_num].hin}`,
      content: allocation[str_key]?.obj?.bi,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bi || marketname.value?.en // 取出 玩法名称 中英文
    },
    ru: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.ru : `${cancel_data[cancel_num].ru}`,
      content: allocation[str_key]?.obj?.bi,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bi || marketname.value?.en // 取出 玩法名称 中英文
    },
    ara: { // 【${list_saishi.value[saishi_num].zh}】
      title: cancel_num == 20 ? other_data[props.row.customRemarkIndex]?.ara : `${cancel_data[cancel_num].ara}`,
      content: allocation[str_key]?.obj?.bi,//eval把改字符串运行并返回最后字符串
      market: marketname.value?.bi || marketname.value?.en // 取出 玩法名称 中英文
    },
  }
  // 备份多语言数据
  list_data_copy.value = JSON.parse(JSON.stringify(list_data.value))
}
// 时间变化 含时间和不含时间的数据 输出
export const time_change_value = (payload, obj) => {
  const {center_time} = payload
  let time = check_time(obj);
  let text = time
    ? center_time[obj.language].replace("$P$M$", time)
    : center_time_no[obj.language];
  return text;
}
