import {
  close_drop_down,
  open_drop_down,
  handle_click,
} from "project/caopan/components/query/search/module/index.js";
export const project_caopan_components_query_search_componsable_fn = (raw_payload) => {
  // payload传递
  const payload = {
    ...raw_payload,
  };

  // 返回所需函数
  return {
    handle_click: (type) => handle_click(payload, type),
    close_drop_down: () => close_drop_down(payload),
    open_drop_down: () => open_drop_down(payload)
  };
};
