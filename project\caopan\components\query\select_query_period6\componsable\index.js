import { onMounted, onBeforeUnmount } from "vue";

import { set_history_startTimeFrom} from "project/caopan/components/query/select_query_period6/module/index.js";
export const project_caopan_components_query_select_query_period6_select_query_period6_composable_fn =
  (payload) => {
    onMounted(() => {
      const {
        more_query,
        show_more_query,
        compute_current_h_gmt_add_8,
        timer,
      } = payload;
      show_more_query.value = more_query.value;
      timer.value = setInterval(compute_current_h_gmt_add_8.value, 5000);
    });

    onBeforeUnmount(() => {
        const {
          timer,
        } = payload;
      set_history_startTimeFrom(null);
      clearInterval(timer.value);
    });

    return {
      ///这边预期会放置@方法体， 和 computed复杂的方法体（需要payload，或高耦合的代码）
      ///  marketId_pop_m : ()=>marketId_pop_m(payload),
    };
  };
