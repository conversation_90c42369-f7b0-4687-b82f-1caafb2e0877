<template>
  <div>
    <span class="hidden">{{ select_hidden }}</span>
    <span :class="title_class">{{ title }}</span>
    <div class="p-gutter-mr--sm">
      <q-checkbox
        v-for="(item, index) in options"
        :key="index"
        color="primary"
        v-model="select"
        :label="item.label"
        :val="item.value"
      />
    </div>
  </div>
</template>

<script setup>



const props = defineProps({

  valueee: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
       default: i18n_t('saleTable.sa_87')//数据来源
    },
    title_class: {
      type: [Array, String, Object],
      default: "p1-fc--1 p1-mx--1"
    },
    options: {
      type: Array,
      default: () => []
    }
})


///引入 variable—composable-fn
import { project_caopan_components_query_data_source_code1_data_source_code1_variable_composable_fn } from "project/caopan/components/query/data_source_code1/componsable/variable.js";

//最基础的payload
const base_payload =
project_caopan_components_query_data_source_code1_data_source_code1_variable_composable_fn();

///解构出 参数，符合 template 所需要的
const {
  item,title_class
} = base_payload;

import { project_caopan_components_query_data_source_code1_data_source_code1_composable_fn } from "project/caopan/components/query/data_source_code1/componsable/index.js";

const {
  ///输出 template中@事件需要使用到的方法
} = project_caopan_components_query_data_source_code1_data_source_code1_composable_fn({
  ...base_payload,
  props
  });


</script>
