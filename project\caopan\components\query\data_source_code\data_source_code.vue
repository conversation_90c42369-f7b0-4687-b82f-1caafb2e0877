<!--
 * @FilePath: /project/caopan/components/query/data_source_code.vue
 * @Description:
-->
<template>
  <div
    class="row text-panda-text-light w100p justify-between border-left border-panda-dark-dark"
    :class="bgclass"
  >
    <div class="p-ml--xxs">
      <span class="line-height-34px text-panda-text-dark" style="    margin-left: 0px; ">{{ title }}</span>
      <q-checkbox
        class="line-height-34px p-ml--xxs"
        color="primary"
        v-model="queryform_form.dataSourceCode_all"
        :disable="disable"
        :label="`${i18n_t('common.all')}`"
        @input="queryform_form_dataSourceCode_all_change"
      />
      <q-checkbox
        class="line-height-34px p-ml--sm"
        color="primary"
        :disable="disable"
        v-model="queryform_form.dataSourceCode"
        v-for="(item,index) in filter_data_sources_constant"
        :label="item[show_name]"
        :val="item.code"
        :key="`dataSource_${index}`"
        @input="queryform_form_dataSourceCode_change(item,index)"
      />
    </div>

    <div
      v-if="show_toggle"
      style="     margin-top: 8px;"
      @click="toggle_showrightdetail"
      class="show_toggle p-mr--xxs"
    >
      <span class="panda_icon_switch panda-icon-mini">
        <q-tooltip :content-style="{ width: '95px' }">{{i18n_t('common.displayRight')}}</q-tooltip>
        <!-- 显示右侧信息 -->
      </span>
      <!-- <span class="panda-right-info-toogle-button ">
          <q-tooltip :content-style="{ width: '90px' }" >显示右侧信息</q-tooltip>
        <span class="panda-right-info-toogle-button-inner"></span>
      </span>-->
    </div>
  </div>
</template>

<script setup>
/**
 * 改版后 的接口 需要返回两个数组
 * 为了以后统一管理  此组件 之对外 输出 一个对象  通过结构 直接 用于查询
 *   "dataSourceCode": [
    "string"
  ],
  "id": 0,
  "matchList": [
    "string"
  ],

  目标 输出

    {
    dataSourceCode:[],
    matchList:[],
  }

 *
 *
 */

import { i18n_t } from "src/output/common/project-common.js"
import { emti_generator } from "./emits/emits";
import { project_caopan_components_query_data_source_code_composable_variable_fn  } from "./composable/variable";
import { project_caopan_components_query_data_source_code_composable_fn } from "./composable/index";
const props = defineProps({
    disable: {
      type: Boolean,
      default: false
    },
    show_name: {
      type: String,
      default: "fullName"
    },
    title: {
      type: String,
      default: i18n_t('saleTable.sa_87')//数据来源
    },
    show_toggle: false,
    bgclass: "bg-panda-dark-dark",
    not_pa: {
      default: 0
    } 
})

const emits = defineEmits(['change'])

const all_emits = emti_generator(emits)

const base_state = project_caopan_components_query_data_source_code_composable_variable_fn()

const {
  queryform_form,
  queryform_module,
 } =  base_state

const { 
  init_queryform_form_dataSourceCode,
  emit_dataSourceCode_change,
  comoute_showrightdetail_by_router,
  queryform_form_dataSourceCode_all_change,
  queryform_form_dataSourceCode_change,
  filter_data_sources_constant,
  reset_query  
} =  project_caopan_components_query_data_source_code_composable_fn({...base_state, prop})

</script>
