import { api_operate } from "src/api/index.js";
import { Message, lodash } from "src/output/common/project-common.js";
/**
 * @description: 模糊查询-下拉菜单查询数据
 * @param {*}
 * @return {*}
 */
export const get_tournament_name_data = (payload, value) => {
  const { tournament_loading, params, tournament_data } = payload;
  if (!value) {
    return false;
  } //为空就不
  tournament_loading.value = true;
  let param = {
    sportId: params.value.sportId,
    tournamentName: value.trim(),
    // 去除首位空格
  };
  api_operate
    .getTournamentList(param)
    .then((res) => {
      let code = lodash.get(res, "data.code");
      let data = lodash.get(res, "data.data");
      let msg = lodash.get(res, "data.msg");
      if (code == 200 || code == "0000000") {
        tournament_data.value = data.records || [{}];
        tournament_loading.value = false;
      } else {
        Message.error(`${msg}`);
      }
    })
    .catch((err) => {
      Message.error(`${err}`);
    });
};
