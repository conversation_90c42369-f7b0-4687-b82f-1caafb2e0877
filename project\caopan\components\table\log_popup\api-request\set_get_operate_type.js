
   import {
    api_events_analyse,
    api_risk_control,
    api_order,
    api_settlement_center,
  } from "src/api/index.js";
  import { Message, lodash, language_value } from "src/output/common/project-common";
   // 联赛参数设置操作类型
    export const Sett_getOperateType = (payload,param) => {

      const { config } = payload

      let par = {
        sportId: param.sportId,
        operatePageCode: param.operatePageCode,
        operateModule: param.operateModule,
      };
      api_settlement_center
        .SettleCentre_getOperateType(par)
        .then((res) => {
          let { code, data, msg } = lodash.get(res, "data");
          if (code == 200) {
            data.forEach((el) => {
              el.value = language_value(el.names);
              el.label = language_value(el.names);
            });
            config.value.operateTypeList = data;
          } else {
            Message.error(res?.data?.msg);
          }
        })
        .catch((e) => {
          Message.error(e);
        })
        .finally(() => {});
    }
