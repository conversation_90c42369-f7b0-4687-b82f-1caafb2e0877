import {
  lodash,
  sport_permission_has
} from "src/output/common/project-common.js"

import {
  sport_type_click,

} from "project/caopan/components/query/select_sport_type/select_sport_type1/module/index.js";

import {
  get_sportType_array,
  //  get_router_cache_value_by_path
} from "src/output/common/store-common.js"
import { api_trader } from "src/api/index.js";
// 计算赛事数量
const request_before = (payload) => {
  const { sport_type_constant_copy, route, sport_type_constant_lt_8 } = payload
  let sport_type_constant = get_sportType_array()
  // 混入对象共享，exclude改变了原始值，先进行深拷贝
  sport_type_constant_copy.value = lodash.cloneDeep(sport_type_constant.value);

  // 需要展示赛事数量的路由：  早盘赛事  滚球赛事  开售玩法  仅早盘开售
  let show_match_num_route = ['match_sale', 'match_live', 'match_play_tabs', 'unsold_match'];
  let copy_exclude_arr = lodash.cloneDeep(sport_type_constant_copy.value);
  let params
  if (show_match_num_route.includes(route.name)) {
    params = {
      isFavorite: 0,
      marketType: route.name == 'match_live' ? 'LIVE' : 'PRE',
    };
  }
  return { params, copy_exclude_arr } 
}
export const rebuild_sport_type_constant = async (payload) => {
  
  const { sport_type_constant_lt_8 } = payload
  try {
    const { params , copy_exclude_arr }  = request_before(payload)
    let res = api_trader.getMarketSellNumberByType(params)
    let data = lodash.get(res, "data.data");
    if (res.data && res.data.code == 200) {
      data && data.forEach(x => {
        copy_exclude_arr.forEach(k => {
          if (x.sport_id == k.id) {
            k.numb = x.number
          }
        })
      })
    }
    sport_type_constant_lt_8.value = copy_exclude_arr
    // forceUpdate();
    find_default_item(payload);
  } catch (error) {
    console.error(error)
  }

}
export const find_default_item = (payload) => {
  const { route, findValidSport, sport_type_constant_copy, only_show_virtual,set_router_cache_by_path_and_value,get_router_cache_value_by_path } = payload
  let menu_ids = []
  sport_type_constant_copy.value.map(x => menu_ids.push(x.id))
  let id = get_router_cache_value_by_path(payload, "sportId");
  let cur_sportId = route.query.cur_sportId; // 接受  选中数据id
  if (cur_sportId) {
    id = cur_sportId;
  }
  if (!id) {
    // get_router_cache_value_by_path此处可能取不到值
    id = route.path.includes('settlement_manage') ? 1 : findValidSport(payload);
    set_router_cache_by_path_and_value("sportId", id);
  }
  // 冠军玩法默认足球
  if (route.name == 'champion_plays') {
    id = sport_permission_has(1) ? 1 : findValidSport(payload);
    set_router_cache_by_path_and_value("sportId", id);
  }
  //玩法管理页选中全部和娱乐 后跳到早盘、滚球赛事 重置选中id为1
  let change_id = ['match_sale', 'match_live'];
  if (change_id.indexOf(route.name) > -1 && (id < 0 || id == 105)) {
    id = sport_permission_has(1) ? 1 : findValidSport(payload);
    set_router_cache_by_path_and_value("sportId", id);
  }
  // 虚拟 体育 玩法管理------ 虚拟体育玩法集合 管理
  if (only_show_virtual.value.includes(route.name)) {
    // id=   sport_type_constant_copy.value[0]['id']


  }
  // 如果当前ID 不在菜单数组内  切换到第一个
  if (!menu_ids.includes(id)) {
    id = menu_ids[0]
  }
  // 找到 球类 菜单 对象
  let obj = lodash.find(sport_type_constant_copy.value, function (o) {
    return o.id == id;
  });


  sport_type_click(payload, obj);
}