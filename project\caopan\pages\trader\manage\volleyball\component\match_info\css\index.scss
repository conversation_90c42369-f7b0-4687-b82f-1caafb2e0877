.pr {
    position: absolute;
    right: 10px;
  }
  .scores {
    width: 170px;
    display: flex;
    justify-content: flex-end;
    margin-right: 30px;
  }
  .first-line {
    display: flex;
  }
  :deep( .ivu-dropdown-item ){
    color: var(--q-color-panda-text-light);
  }
  :deep( .ivu-dropdown-item ){
    padding: 0;
  
    span {
      display: inline-block;
      padding: 7px 16px;
    }
  }
  .set_score {
    color: #ffba72;
  }
  .current_score {
    color: #0091ff;
    font-weight: bold;
    text-align: center;
  }
  .block :deep( .ivu-tooltip-inner ){
    background-color: var(--q-color-panda-field-grey);
    color: var(--q-color-panda-text-light);
  }
  .blue_bg {
    background-color: #0091ff;
    color: #ffffff;
  }
  .green_bg {
    background-color: #00a274;
    color: #ffffff;
  }
  .yellow_bg {
    background-color: #d1883a;
    color: #ffffff;
  }
  .blue_bg2 {
    background-color: var(--q-color-panda-blue_bg);
  }
  .green_bg2 {
    background-color: var(--q-color-panda-green_bg);
  }
  .yellow_bg2 {
    background-color: var(--q-color-panda-yellow_bg);
  }
  .headerBox {
    position: absolute;
    width: 100%;
    background: rgba(68, 38, 47, 0.5);
    height: 100%;
    animation: fade 1000ms infinite;
  }
  .team_wrap {
      position: relative;
      .denger_number {
        width: 18px;
        height: 18px;
        border-radius: 18px;
        line-height: 20px;
        position: absolute;
        background: #f44336;
        top: 5px;
        right: -20px;
        text-align: center;
      }
  
    }
  @-webkit-keyframes fade {
    from {
      background: rgba(68, 38, 47, 1);
    }
    50% {
      background: rgba(68, 38, 47, 0.5);
    }
  
    to {
      background: rgba(68, 38, 47, 1);
    }
  }
  
  @-moz-keyframes fade {
    from {
      background: rgba(68, 38, 47, 1);
    }
    50% {
      background: rgba(68, 38, 47, 0.5);
    }
  
    to {
      background: rgba(68, 38, 47, 1);
    }
  }
  
  @-ms-keyframes fade {
    from {
      background: rgba(68, 38, 47, 1);
    }
    50% {
      background: rgba(68, 38, 47, 0.5);
    }
  
    to {
      background: rgba(68, 38, 47, 1);
    }
  }
  
  @-o-keyframes fade {
    from {
      background: rgba(68, 38, 47, 1);
    }
    50% {
      background: rgba(68, 38, 47, 0.5);
    }
  
    to {
      background: rgba(68, 38, 47, 1);
    }
  }
  
  @keyframes fade {
    from {
      background: rgba(68, 38, 47, 1);
    }
    50% {
      background: rgba(68, 38, 47, 0.5);
    }
  
    to {
      background: rgba(68, 38, 47, 1);
    }
  }
  .blue_color,
  .blue_color.panda_icon_MTS_2:before,
  .blue_color.panda_MTS_2:before,
  .blue_color.panda_icon_a_2:before,
  .blue_color.panda_icon_m_2:before,
  .blue_color.panda_new_cp_icon_4_2:before,
  .blue_color.panda_type1:before,
  .blue_color.panda_MTS:before,
  .blue_color.panda_cp5:before,
  .blue_color.panda_type2:before,
  .blue_color.panda_type3:before,
  .blue_color.panda_type5:before,
  .blue_color.panda_type6:before,
  .blue_color.panda_new_cp_icon_10:before,
  .blue_color.panda_new_cp_icon_11:before,
  .blue_color.panda_new_cp_icon_14_2:before,
  .blue_color.panda_new_cp_icon_13:before {
    color: #0091ff;
  }
  .green_color,
  .green_color.panda_icon_MTS_2:before,
  .green_color.panda_MTS_2:before,
  .green_color.panda_icon_a_2:before,
  .green_color.panda_icon_m_2:before,
  .green_color.panda_new_cp_icon_4_2:before,
  .green_color.panda_type1:before,
  .green_color.panda_MTS:before,
  .green_color.panda_cp5:before,
  .green_color.panda_type2:before,
  .green_color.panda_type3:before,
  .green_color.panda_type5:before,
  .green_color.panda_type6:before,
  .green_color.panda_new_cp_icon_10:before,
  .green_color.panda_new_cp_icon_11:before,
  .green_color.panda_new_cp_icon_14_2:before,
  .green_color.panda_new_cp_icon_13:before {
    color: #00a274;
  }
  .yellow_color,
  .yellow_color.panda_icon_MTS_2:before,
  .yellow_color.panda_MTS_2:before,
  .yellow_color.panda_icon_a_2:before,
  .yellow_color.panda_icon_m_2:before,
  .yellow_color.panda_new_cp_icon_4_2:before,
  .yellow_color.panda_type1:before,
  .yellow_color.panda_MTS:before,
  .yellow_color.panda_cp5:before,
  .yellow_color.panda_type2:before,
  .yellow_color.panda_type3:before,
  .yellow_color.panda_type5:before,
  .yellow_color.panda_type6:before,
  .yellow_color.panda_new_cp_icon_10:before,
  .yellow_color.panda_new_cp_icon_11:before,
  .yellow_color.panda_new_cp_icon_14_2:before,
  .yellow_color.panda_new_cp_icon_13:before {
    color: #d1883a;
  }
  .fr {
    float: right;
  }
  .gray_bg2 {
    background: var(--q-color-panda-gray_bg) !important;
    z-index: 1000;
  }
  .panda-sticky-header-table td:first-child,
  .panda-sticky-header-table th:first-child {
    position: relative;
  }
  .panda-sticky-header-table td:first-child {
    z-index: initial;
  }
  .border_text {
    display: inline-block;
    height: 18px;
    line-height: 18px;
    padding: 0 4px;
    border: 1px solid #0091ff;
    border-radius: 2px;
    color: #0091ff;
    cursor: pointer;
    position: relative;
  }
  .panda_new_cp_icon_1:before {
    color: #0091ff;
  }
  .border_text_lv {
    border: 1px solid #269f27;
    color: #269f27;
  }
  .home_color {
    color: var(--q-color-panda-text-light) !important;
    text-indent: 0;
  
  }
  .panda-border {
    border: solid 1px;
    cursor: pointer;
  }
  .q-table--horizontal-separator tbody tr td {
    border-right: 1px solid var(--q-color-panda-border-color-secondary5);
    border-bottom: 1px solid var(--q-color-panda-border-color-secondary5);
    vertical-align: top;
  }
  .q-table--horizontal-separator tbody tr td:nth-child(1) {
    border-left: 0;
  }
  .p-bg-dark {
    background-color: #2d374a;
  }
  .let_the_ball {
    color: #ff6600 !important;
  }
  .text-panda_hover:hover {
    background: var(--q-color-panda-them--28303F) !important;
  }
  .green {
    border-color: #00a274;
    color: #00a274;
  }
  .yellow {
    border-color: #d1883a;
    color: #d1883a;
  }
  .api {
    margin-left: -6px;
    color: var(--q-color-panda-text-light);
  
    li {
      margin: 4px 0;
      > span {
        margin-left: 6px;
        vertical-align: middle;
      }
    }
  }
  .opacity {
    opacity: 0;
  }
  .two_line {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: normal;
    font-size: 14px;
  }
  .ellipsis {
    overflow: hidden;
    font-size: 14px;
    text-overflow: ellipsis;
    width: 95px;
    white-space: nowrap;
    display: inline-block;
  }
  :deep( .q-table td.panda-table:first-child ){
    border-left: 0;
  }
  .score {
    float: left;
    min-width: 12px;
    padding: 0px 8px;
  }
  .gray_bg {
    background: var(--q-color-panda-them--28303F) !important;
  }
  .gray_bg2 {
    background: var(--q-color-panda-gray_bg) !important;
  }
  .blue_bg {
    background-color: #0091ff;
    color: #ffffff;
  }
  .green_bg {
    background-color: #00a274;
    color: #ffffff;
  }
  .yellow_bg {
    background-color: #d1883a;
    color: #ffffff;
  }
  .blue_bg2 {
    background-color: var(--q-color-panda-blue_bg);
  }
  .green_bg2 {
    background-color: var(--q-color-panda-green_bg);
  }
  .yellow_bg2 {
    background-color: var(--q-color-panda-yellow_bg);
  }
  .block {
    text-indent: 0;
  }
  .pointer {
    cursor: pointer;
  }
  .height22 {
    height: 22px;
    line-height: 22px;
  }
  .icons {
    margin-top: 3px;
  }
  .auto {
    margin: 0 auto;
  }
  .icons2 {
    font-size: 14px;
  }
  .bg-panda-base-line {
    height: 100%;
  }
  .padding {
    position: relative;
  }
  .padding2 {
    padding: 5px 0px;
    height: 100%;
  }
  .verticalB {
    vertical-align: sub;
  }
  .no_select {
    user-select: none;
  }
  .bold {
    font-weight: bold;
  }
  .height94 {
    height: 94px;
  }
  .textRight {
    text-align: right;
  }
  .verticalTB {
    vertical-align: text-bottom;
  }
  .dataSource {
    float: right;
    width: 36px;
    height: 28px;
    text-align: right;
    color: #4186d6;
    cursor: pointer;
  }
  .bottom10 {
    padding-bottom: 10px;
  }
  .num {
    cursor: pointer;
    color: #0091ff;
  }
  .num1 {
    cursor: pointer;
    color: #dca339;
  }
  .table_row_pingpang_td {
    border-right: 1px solid var(--q-color-panda-border-color-secondary5) !important;
  }
  .ivu-dropdown .ivu-select-dropdown {
    background: var(--q-color-panda-base-light) !important;
  }
  
  .bg-panda-hover .ivu-dropdown-item:hover {
    background: rgba(0, 170, 152, 0.1);
  }