import { ref, computed, watch, reactive, useTemplateRef, onMounted } from "vue";
import { watcher_register } from "project/caopan/components/query/queryTime/watch/index.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";
export const project_caopan_components_query_query_time_componsable_fn = (raw_payload) => {
 
  console.log("==================raw_payload",raw_payload)
  const payload = {
    ...raw_payload,
  };
  src_componsable_util_watcher_register_componsable_fn(
    watcher_register(payload)
  );

  return {
    time_on_change: () => time_on_change(payload),

  };
};
