import {
  get_market_odds_kind,
  get_trader_tab_status,
  set_market_odds_kind,
} from "src/output/common/store-common.js";
import { src_componsable_global_variable_componsable_fn } from "src/output/common/componsable-common.js";
import { create_base_state_instance_fn } from "project/caopan/components/select/select_odds/variable/index.js";

export const project_caopan_components_select_select_odds_componsable_variable_componsable_fn =
  () => {
    const market_odds_kind = get_market_odds_kind();

    const trader_tab_status = get_trader_tab_status();

    const base_state_instance = create_base_state_instance_fn();

    const global_obj = src_componsable_global_variable_componsable_fn();
    const { route } = global_obj;
    
    return {
      market_odds_kind,
      trader_tab_status,
      ...base_state_instance,
      route
    };
  };
