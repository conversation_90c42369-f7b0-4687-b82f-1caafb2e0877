import { ref } from 'vue'



export const create_base_state_instance_fn = () => {

    const isFilter = ref(false);
    const show_q_date_component = ref(false);
    const selected_date_tab = ref(0);
    const show_more_query = ref(false);
    const queryform_form = ref({});
    // 针对时间选择器 ，真正 
    const startTimeFrom = ref("");
    // 展示日期数据 
    const query_form_date_arr = ref([]);
    const query_form_date_arr1 = ref([]);
    const history_date = ref("");
    const other_date = ref("");
    const timer = ref("");
    const current_h = ref("");
    const first_day_startTimeFrom = ref("");

    const disabledDate = (date) => {
        return date && date.valueOf() > Date.now() - 12 * 60 * 60 * 1000;
    };

    const otherDisabledDate = (date) => {
        return date && date.valueOf() < Date.now();
    };
    return {

        isFilter,
        show_q_date_component,
        selected_date_tab,
        show_more_query,
        queryform_form,
        // 针对时间选择器 ，真正
        startTimeFrom,
        // 展示日期数据
        query_form_date_arr,
        query_form_date_arr1,
        history_date,
        other_date,
        timer,
        current_h,
        first_day_startTimeFrom,
        disabledDate,
        otherDisabledDate,

    };
};
