import { get_router_cache } from "src/output/common/store-common.js";

import {
  set_router_cache_by_path_and_value,
  get_router_cache_value_by_path,
} from "project/caopan/components/router_cache/handler/module/index.js";
import { watcher_register } from "project/caopan/components/router_cache/handler/watch/index.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";


export const project_caopan_components_router_cache_handler_componsable_index_componsable_fn =
  (raw_payload) => {

    let router_cache = get_router_cache();
    const payload = {
      ...raw_payload,

      router_cache,
    };

    // 使用全局watch componsable处理watch相关事务
    src_componsable_util_watcher_register_componsable_fn(
      watcher_register(payload)
    );
    return {
      set_router_cache_by_path_and_value: (path, value) => set_router_cache_by_path_and_value(payload, path, value),
      get_router_cache_value_by_path: (path) => get_router_cache_value_by_path(payload, path),
    };
  };
