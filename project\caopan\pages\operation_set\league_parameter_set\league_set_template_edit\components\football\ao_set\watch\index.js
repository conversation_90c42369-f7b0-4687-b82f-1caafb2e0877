
watch: {
    tab_change = (payload,val) => {
        if (val == "Regular") {
          data_type.value = 1;
        } else if (val == "Extra") {
          data_type.value = 2;
        } else {
          data_type.value = null;
        }
      },
      rc: {
        immediate: true,
        export const handler = (payload,n) => {
          if (n) {
            tabs.value = [
              {
                name: "Regular",
                label: "Regular",
                active: true,
                badge: false,
              },
            ];
            tab_change.value = "Regular";
          } else {
            onInitData();
          }
        },
      },
    }