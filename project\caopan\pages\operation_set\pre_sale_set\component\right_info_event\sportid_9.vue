<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date           : 2021-01-30 17:18:46
 * @LastEditTime: 2021-10-05 11:51:32
 * @LastEditors: Please set LastEditors
 * @Description    : 排球
 * @FilePath: /src/pages/operation_set/pre_sale_set/component/right_info_event/sportid_9.vue
-->
<template>
  <div class="text-panda-text-light">
    <!-- 球队信息  下拉框筛选 -->
    <div
      class="wtHider row p-fwnw p-mxw100p p-jcsb height-40px line-height-40x p1-aic p-mb--xxs pl15x pr15x border-bottom-light"
    >
      <!-- 赛事阶段下拉框 -->
      <Select
        class="bg-panda-field-grey"
        size="small"
        style="width: 80px"
        v-model="btn_model"
      >
        <template v-for="item in time_options">
          <Option
            :key="item.value"
            :value="item.value"
            v-if="
              item.value <=
              (right_detail_obj.matchPeriodId > 50
                ? 10
                : right_detail_obj.matchPeriodId)
            "
          >
            {{ item.label }}
          </Option>
        </template>
      </Select>
      <!-- 数据源选择下拉框 -->
      <Select
        class="bg-panda-field-grey p-ml--xxxs"
        size="small"
        style="width: 95px; margin: 7px 0"
        v-model="data_source"
      >
        <template v-for="item in data_source_opts">
          <Option
            v-if="ws_all_dataSourceCode.includes(item.value)"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </Option>
        </template>
      </Select>
      <div class="mt5x">
        <img
          :src="`/svg/9/9_1_${score_status ? 'b' : 'l'}.svg`"
          alt=""
          @click="score_status = !score_status"
          title="得分"
          class="h18x cp"
        />
      </div>
      <!-- 点击图标切换事件流排序 同时切换图标  9_1_b.svg -->
    </div>
    <!-- 事件流 -->
    <div
      class="p1-oxa p-f1 p-px--dm pande-text-788299 ml15x mr15x"
      style="height: 530px"
      v-if="event_list && event_list.length"
    >
      <div
        v-for="(i, index) in score_status
          ? event_list
          : event_list.filter((x) => x.eventCode == 'volleyball_score_change')"
        :key="index"
      >
        <div v-if="i.dataSourceCode == data_source">
          <!-- 判断是否选中数据源 -->
          <div v-if="btn_model == '-1' ? true : i.matchPeriodId == btn_model">
            <!-- 判断是否选中赛事阶段 -->
            <div
              v-if="
                should_show.length == 0
                  ? true
                  : should_show.includes(i.eventCode)
              "
            >
              <!-- 判断是否选中图标显示 -->

              <div class="flex mt5x" v-if="i.homeAway && i.homeAway == 'home'">
                <div
                  class="flex p-jcsb p-r-w--md1 p-r-bgc--dark p-lh--xxs p-h--xxs"
                >
                  <!-- 主队 -->
                  <!-- 赢得第X分 -->
                  <span class="p-f1 p-ellipsis p-px--xxs">
                    <span
                      v-if="!compute_i18n_type"
                      :title="`赢得第${i.remark}分`"
                      >赢得第{{ i.remark }}分</span
                    >
                    <span v-else :title="`Win ${i.remark} ' point`"
                      >Win {{ i.remark }} ' point</span
                    >
                  </span>
                  <!-- 比分 图标 -->
                  <span
                    style="width: 45px"
                    class="p-dif p-jcc p-fb--sm items-center"
                    v-if="i.eventCode === 'volleyball_score_change'"
                  >
                    <!-- 主队进球显示 -->
                    <span style="width: 15px" class="tac">
                      {{ i.homeFirstNumber }}
                    </span>
                    <!-- 图标显示 -->
                    <img
                      style="height: 14px"
                      :src="computed_icon(i, event_keys)"
                      alt=""
                      v-if="i.eventCode == 'volleyball_score_change'"
                    />
                    <span style="width: 15px" v-else></span>
                    <!-- 客队进球显示 -->
                    <span style="width: 15px" class="tac">
                      {{ i.awayFirstNumber }}
                    </span>
                  </span>
                </div>
                <q-icon
                  name="play_arrow"
                  style="
                    font-size: 35px;
                    height: 20px;
                    transform: translateX(-12px);
                  "
                  class="p-r-tc--dark"
                ></q-icon>
              </div>
              <div
                class="flex p-jcfe mt5x"
                v-if="i.homeAway && i.homeAway == 'away'"
              >
                <div
                  class="flex p-jcsb p-r-w--md1 p-r-bgc--dark p-lh--xxs p-h--xxs pr10x"
                >
                  <!-- 客队 -->
                  <span
                    class="p-dif p-jcc p-fb--sm items-center"
                    v-if="i.eventCode === 'volleyball_score_change'"
                  >
                    <span
                      style="width: 45px"
                      class="p-dif p-jcc p-fb--sm items-center"
                      v-if="i.eventCode === 'volleyball_score_change'"
                    >
                      <!-- 主队进球显示 -->
                      <span style="width: 15px" class="tac">
                        {{ i.homeFirstNumber }}
                      </span>
                      <!-- 图标显示 -->
                      <img
                        style="height: 14px"
                        :src="computed_icon(i, event_keys)"
                        alt=""
                        v-if="i.eventCode == 'volleyball_score_change'"
                      />
                      <span style="width: 15px" v-else></span>
                      <!-- 客队进球显示 -->
                      <span style="width: 15px" class="tac">
                        {{ i.awayFirstNumber }}
                      </span>
                    </span>
                  </span>
                  <!-- 赢得第X分 -->
                  <span v-if="!compute_i18n_type" :title="`赢得第${i.remark}分`"
                    >赢得第{{ i.remark }}分</span
                  >
                  <span v-else :title="`Win ${i.remark} ' point`"
                    >Win {{ i.remark }} ' point</span
                  >
                </div>
                <q-icon
                  name="play_arrow"
                  style="
                    font-size: 35px;
                    height: 20px;
                    transform: translateX(12px) rotate(0.5turn);
                  "
                  class="p-r-tc--dark p-ro1"
                ></q-icon>
              </div>
              <!-- 赛事阶段分割线 -->
              <div
                class="dashed"
                v-if="
                  computed_matchPeriodId_show(i.matchPeriodId) &&
                  period_id[i.matchPeriodId] == index
                "
              >
                <span class="after"></span>
                <span class="title">
                  {{ computed_matchPeriodId(i.matchPeriodId) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="row p-jcc pande-text-788299" style="height: 170px">
      <span>{{ $t("score_center.text_7") }}</span
      ><!-- 无实时事件流 -->
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { i18n_t } from "src/output/common/project-common.js";
import { project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_componsable_fn } from "project/caopan/pages/operation_set/pre_sale_set/component/right_info_event/mixins/componsable/index.js";
import { project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_variable_componsable_fn } from "project/caopan/pages/operation_set/pre_sale_set/component/right_info_event/mixins/componsable/variable.js";

const props = defineProps({
  right_detail_obj: {
    type: Object, // 指定为对象类型
    required: true, // 如果是必传属性
  },
  sport_id: {
    type: [String, Number], // 可以是字符串或数字
    required: true,
  },
  event_list: {
    type: Array, // 指定为数组类型
    default: () => [], // 默认值为空数组
  },
  period_id: {
    type: [String, Number], // 可以是字符串或数字
    default: null, // 默认值为 null
  },
});

const time_options = ref([
  { label: `${i18n_t("common.full_time")}`, value: "-1" }, // 全场
  { label: "SET1", value: "8" },
  { label: "SET2", value: "9" },
  { label: "SET3", value: "10" },
  { label: "SET4", value: "11" },
  { label: "SET5", value: "12" },
  { label: `${i18n_t("matchPeriodId.matchPeriodId2_40")}`, value: "441" }, // 加时赛
  { label: "SET111", value: "442" },
]);

const statistics_code_list = ref([
  //所有的比分统计类型
  { key: "matchScore", name: `${i18n_t("score_center.sportId_9_1")}` }, // 全场比分
  { key: "setScore", name: `${i18n_t("score_center.sportId_9_2")}` }, // 局比分
  { key: "serveScoresCount", name: `${i18n_t("score_center.sportId_9_3")}` }, // 发球比分
  { key: "serveErrorCount", name: `${i18n_t("score_center.sportId_9_4")}` }, // 发球失误次数
]);

const event_keys = ref([
  { key: ["current_serve_volleyball"] }, // 当前发球方
  { key: ["volleyball_score_change"] }, // 得分
  { key: ["match_status"] }, // 赛事状态
]);

const score_status = ref(true); // 高亮 还是灰色  以及过滤数据
const payload =
  project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_variable_componsable_fn();
const {
  data_source_opts,
  btn_model,
  data_source,
  route,
  ws_match_list_score_center,
  ws_all_dataSourceCode,
  should_show,
} = payload;

const {
  computed_icon,
  computed_matchPeriodId_show,
  computed_matchPeriodId,
} =
  project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_componsable_fn(
    {
      right_detail_obj: props.right_detail_obj,
      sport_id: props.sport_id,
      event_list: props.event_list,
      period_id: props.period_id,
      ...payload,
      time_options,
      statistics_code_list,
      score_data_1,
      event_keys,
    }
  );
// 切换数据源  更新 ws 请求
watch(data_source.value, (val) => {
  if (!val) return;
  sessionStorage.setItem("ws_dataSourceCode", val);
});
// 监听赛事切换
watch(props.right_detail_obj, (val) => {
  data_source.value = val.dataSourceCode;
  btn_model.value = "-1";
  sessionStorage.setItem("ws_dataSourceCode", val.dataSourceCode);
});
// 监听比分切换
watch(ws_match_list_score_center, (val) => {
  if (!val || typeof val != "object") return;
  let { data } = val;
  if (!Array.isArray(data) || data.length == 0) return;
  let id_type = "matchInfoId";
  if (["match_manage", "event_review"].includes(route.name)) {
    // 赛程管理 事件审核 id_type 字段对应id  预开售 开售页面取matchInfoId
    id_type = "id";
  }
  data.forEach((item) => {
    if (item.matchId == props.right_detail_obj[id_type]) {
      data_source_opts.value.forEach((qst) => {
        if (!item.allDataSourceCode || !item.dataSourceCode) return;
        ws_all_dataSourceCode.value = item.allDataSourceCode;
        if (sessionStorage.getItem("ws_dataSourceCode")) {
          data_source.value = sessionStorage.getItem("ws_dataSourceCode");
        }
      });
    }
  });
});
</script>

<style lang="scss" scoped>
@import "src/pages/operation_set/pre_sale_set/component/right_info_event/common.scss";
</style>
