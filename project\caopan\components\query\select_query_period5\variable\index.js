import { ref } from "vue";
export const create_base_state_instance_fn = () => {
  const show_q_date_component = ref(false);
  const selected_date_tab = ref(22);
  const show_more_query = ref(false);
  const queryform_form = ref({});
  // 针对时间选择器 ，真正
  const startTimeFrom = ref("");
  const query_form_date_arr = ref([]);
  const history_date = ref("");
  const other_date = ref("");
  const timer = ref("");
  const current_h = ref("");
  const first_day_startTimeFrom = ref("");
  const current_ = new Promise((res) => {
    current_promise.value = res;
  });
  const date_option = ref({});
  const date_option2 = ref({});
  const optional_events_num = ref(0);
  const routeName = ref("");
  const hover_refresh = ref(false);
  return {
    show_q_date_component,
    selected_date_tab,
    show_more_query,
    queryform_form,
    startTimeFrom,
    query_form_date_arr,
    history_date,
    other_date,
    timer,
    current_h,
    first_day_startTimeFrom,
    current_,
    date_option,
    date_option2,
    optional_events_num,
    routeName,
    hover_refresh,
  };
};
