
import { create_base_state_instance_fn } from "project/caopan/components/query/select_query_period/variable/index.js";
import { src_componsable_global_variable_componsable_fn } from "src/output/common/componsable-common.js";

export const project_caopan_components_query_select_query_period_select_query_period_composable_variable_fn = () => {

  const select_query_period_state = create_base_state_instance_fn()
  const global_obj = src_componsable_global_variable_componsable_fn();
  const { route } = global_obj;

  let select_query_period_variable_state = {
    ...select_query_period_state,
    route
  }


  return select_query_period_variable_state
}