<!--
 * <AUTHOR> Rank
 * @Date           : 2020-11-08 15:21:05
 * @Description    : 运营管理-联赛排序
 * @FilePath: /project/caopan/pages/operations_management/league_ranking/index.vue
-->

<template>
  <div class="use-ivew">
    <div>
      <!-- 赛种展示 -->
      <select-sport-type></select-sport-type>
    </div>
    <div class="row justify-between" :style="{ height: `${window_size_info.height - 43}px` }">
      <div class="bg-panda-base-light4 border-right-light2" style="width: 268px; height: 100%" v-show="left_show">
        <div class="border-bottom-light2 height-34px line-height-34px bg-panda-sidebar-head test-panda-sidebar-head"
          style="padding: 0 10px">
          <!-- 更多筛选条件 -->
          {{ i18n_t("report_common.search1") }}
        </div>
        <div style="padding: 5px 10px" class="test-panda-sidebar-lable">
          <!-- 联赛名称 -->
          <div class="row justify-between items-center q-mt-md">
            <div class="test-panda-sidebar-lable">
              {{ i18n_t("operations.event_sequencing.c_2") }}
            </div>
            <iSelect v-model.trim="tournament_name" filterable clearable :remote-method="get_tournament_name_data"
              :loading="tournament_loading" style="width: 180px" @on-change="set_tournament_name"
              @on-clear="tournament_name_clear" :placeholder="i18n_t('leagueSet.hint.t1')">
              <!-- placeholder 请输入联赛中文或英文名称 -->
              <iOption v-for="(option, index) in tournament_data" :value="JSON.stringify(option)"
                :key="`option_${option.id}_${index}`" :label="option.tournamentName || option.tournamentEname">
                <span class="tournament-option-id"> {{ option.id }} </span>{{ option.tournamentName ||
                  option.tournamentEname }}
              </iOption>
            </iSelect>
            <!-- 这点只是自己使用，只有开发环境和预开补环境开发 -->
            <div class="p-dib justify-center text-panda-text-light set-bth" v-if="process()">
              <q-btn class="btn panda-btn-light-dense" style="padding: 0 10px" @click="all_show()">{{
                i18n_t("operations.league_ranking.t1")
                }}<!-- 一键展示 --></q-btn>
              <q-btn class="btn panda-btn-dark-dense q-ml-sm" style="padding: 0 10px" @click="all_cancel()">{{
                i18n_t("operations.league_ranking.t2") }}
                <!-- 一键取消 --></q-btn>
            </div>
          </div>
        </div>
      </div>
      <div class="col border-right-light2 position-relative">
        <!-- 联赛等级 sport_id 赛种id-->
        <leve-lv :sport_id="params.sportId" @get_level="get_level" :sport_level="params.tournamentLevel + ''">
          <!-- <div #left> -->
          <template #left>
            <span @click="left_show = !left_show" :class="left_show
                ? 'panda-right-info-toogle-button active cp'
                : 'panda-right-info-toogle-button cp'
              ">
              <q-tooltip>
                <div class="q-tooltop-arrow" :x-placement="'bottom'"></div>
                <span>{{
                  left_show
                    ? i18n_t("common.hideLeft")
                    : i18n_t("common.displayLeft")
                }}</span>
                <!-- 显示左侧信息 收起信息 -->
              </q-tooltip>
              <span class="panda-right-info-toogle-button-inner"></span>
            </span>
          </template>

          <!-- <div #right> -->
          <template #right>
            <!-- 优先级排序 v-model="visible"-->
            <iPoptip placement="bottom" v-model="visible">
              <i-switch v-if="btn_permissions('league_ranking_newbie_sort:edit')" v-model="sta_switch" :true-value="1"
                :false-value="0" size="small" @on-change="priority_change" />
              <span class="table_a" v-if="btn_permissions('league_ranking_newbie_sort:edit')">{{
                i18n_t("playManagement.modify_sort_table") }}</span>
              <!-- <div #content> -->
              <template #content>
                <div class="list_box">
                  {{ i18n_t("playManagement.modify_all_table") }}
                </div>
                <div class="list_felx">
                  <q-btn size="xs" @click="Sure_textanda()"
                    class="btn mr10x text-panda-text-base panda-btn-primary-hover">
                    <!-- 确定 -->
                    {{ i18n_t("credit_controls_page.history_49") }}
                  </q-btn>
                  <q-btn size="xs" @click="Cancel_textanda()"
                    class="btn ml10x text-panda-text-base panda-btn-primary-hover">
                    {{ i18n_t("common.cancel") }}
                    <!-- 取消 -->
                  </q-btn>
                </div>
              </template>
            </iPoptip>
            <!-- 修改排序值 -->
            <span class="panda-btn-light-dense q-mx-sm text-panda-text-white" @click="modify_playrules_order()" v-if="
              !is_modifying_playrules_order &&
              btn_permissions('league_ranking_newbie_sort:edit')
            " style="height: 22px">{{ i18n_t("playManagement.modify_sort_value") }}</span>
            <!-- 保存排序值 -->
            <span v-if="is_modifying_playrules_order" class="panda-btn-light-dense q-mr-sm text-panda-text-white"
              @click="submit_modify_playrules_order()" style="height: 22px">
              <!-- {{ i18n_t("playManagement.save_sort_values") }} -->
              {{ i18n_t("common.save") }}
            </span>
            <!-- 取消 -->
            <span v-if="is_modifying_playrules_order" class="panda-btn-dark-dense q-mr-sm"
              @click="abort_modify_playrules_order()" style="height: 22px; padding: 0 8px">{{
                i18n_t("playManagement.cancel") }}</span>
          </template>
        </leve-lv>
        <div class="table-f p-pr" ref="table">
          <table-no-data ref="table-no-data" :width="`100%`" :tabledata_loading="tabledata_loading"
            :table_no_data_visable="data_list.length == 0"
            :style="{ height: '100%', width: '100%', left: 0, top: '36px' }"
            :loading_style="{ height: '100%', width: '100%' }" style="width: 100%"></table-no-data>
          <!-- 列表展示 -->
          <!-- 高尔夫，赛车运动，板球，飞镖，趣味，娱乐，其他，球种 去掉拖拽排序 -->
          <i-table :columns="[28, 33, 37, 38, 50, 18, 40].includes(params.sportId)
              ? columnsChampion
              : columns
            " :data="data_list" :height="window_size_info.height - 117" row-key="id" :row-class-name="row_class_name"
            @on-row-click="row_click" :disabled-hover="table_hover" class="ivew-table-custom">
            <!-- <template slot-scope="{ row, index }" #sort> -->
            <template #sort="{ row, index }">
              <div class="p-pr">
                <span v-show="!is_modifying_playrules_order && !row.show_input"
                  class="lh24x d-inline-block RobotoBold1">
                  {{ row.sort }}</span>
                <!-- 序号 -->
                <i-input v-model="row.orderNo_10" placeholder="" style="width: 88px" class="index-set"
                  v-show="is_modifying_playrules_order" type="number" @on-enter="set_sort_beg(row, index)"
                  @on-blur="set_sort_beg(row, index)" />
                <i-input v-model="row.orderNo_10" placeholder="" style="width: 88px" class="index-set"
                  v-if="row.show_input" type="number" @on-enter="set_sort_beg(row, index)"
                  @on-blur="set_sort_beg(row, index)" />
              </div>
            </template>
            <!--  联赛名称-->
            <template #tournamentName="{ row }">
              <table-cell-ellipsis :str_all="row.tournamentName"
                :tooltip_position="{ anchor: 'bottom left', self: 'top left' }" :offset="[-10, -10]" class="RobotoBold1"
                :cursor="false"></table-cell-ellipsis>
            </template>
            <!-- 联赛英文名称 -->
            <template #tournamentEname="{ row }">
              <table-cell-ellipsis :str_all="row.tournamentEname" :offset="[-10, -10]"
                :tooltip_position="{ anchor: 'bottom left', self: 'top left' }" class="RobotoBold1"
                :cursor="false"></table-cell-ellipsis>
            </template>
            <template #web_show="{ row, index }">
              <div class="row panda-text-blue justify-center">
                <!-- 新手版展示 -->
                <i-switch v-if="btn_permissions('league_ranking_newbie_switch:view')"
                  v-model="data_list[index].versionNewStatus" :loading="row.swith_loading" size="small"
                  @on-change="swith_change(row, index)" :disabled="!btn_permissions('league_ranking_newbie_switch:edit')
                    " />
              </div>
            </template>
            <!-- <template slot-scope="{ row, index }" #data5> -->
            <template #data5="{ row, index }">
              <div class="row panda-text-blue justify-center">
                <template v-if="![28, 33, 37, 38, 50, 18, 40].includes(params.sportId)">
                  <!-- 通用参数设置 -->
                  <span class="operation-icon" v-if="btn_permissions('league_ranking_newbie_sort:edit')">
                    <span class="panda_manage1 vamd" @click="table_sort(row, index, 1)" v-show="row.sort != 1"></span>
                    <span class="panda_manage3 vamd" @click="table_sort(row, index, 2)"
                      v-show="row.sort != total"></span>
                    <span class="panda_manage4 vamd" @click="table_sort(row, index, 3)" v-show="row.sort != 1"></span>
                  </span>
                  <!-- 滚球置顶 -->
                  <!-- {{ params.sportId }} -->
                  <span v-if="
                    params.sportId === 1 &&
                    btn_permissions('league_ranking_newbie_pinTop:edit')
                  " class="drag-srot cp mr15x" @click="top_morning(row, 1)">{{ i18n_t("menus.lm_10.children8_2")
                    }}</span>
                  <!-- 早盘置顶 -->
                  <span v-if="
                    params.sportId === 1 &&
                    btn_permissions('league_newbie_pinTop:edit')
                  " class="drag-srot cp mr15x" @click="top_morning(row, 2)">{{ i18n_t("menus.lm_10.children8_1")
                    }}</span>
                  <!-- 拖拽排序 -->
                  <span class="drag-srot cp mr15x" @mousedown="click_back(row)"
                    v-if="btn_permissions('league_ranking_newbie_sort:edit')">{{ i18n_t("operations.league_ranking.c_5")
                    }}
                  </span>
                </template>
                <!-- 跨页排序 -->
                <template v-if="!row.show_input">
                  <span class="drag-srot cp" @click="cross_page_sort(row, index)"
                    v-if="btn_permissions('league_ranking_newbie_sort:edit')">{{ i18n_t("operations.league_ranking.c_6")
                    }}
                  </span>
                </template>
                <template v-if="row.show_input">
                  <!-- 保存排序值 -->
                  <span class="panda-btn-light-dense q-mr-sm text-panda-text-white mr15x"
                    @click.stop="submit_set_table_sort(row, index)" style="height: 22px">
                    {{ i18n_t("common.save") }}
                  </span>
                  <!-- 取消 -->
                  <span class="panda-btn-dark-dense q-mr-sm" @click="abort_modify_playrules_order()"
                    style="height: 22px; padding: 0 8px">{{ i18n_t("playManagement.cancel") }}</span>
                </template>
              </div>
            </template>
          </i-table>
          <!-- 分页 -->
          <i-page :total="total" show-elevator show-sizer show-total :page-size-opts="[10, 20, 50, 100, 200, 500]"
            :page-size="params.size" :current="params.start" @on-change="change_page_num"
            @on-page-size-change="change_page_size" class="row items-center justify-center bottom-page" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// import mixins from "project/caopan/mixins/index.js";
import { defineAsyncComponent } from "vue";
import tableCellEllipsis from "project/caopan/components/table/table_cell_ellipsis/index.vue"; //提示框
import { i18n_t, btn_permissions } from "src/output/common/project-common.js";
import selectSportType from "/project/saicheng/components/query/selectSportType/selectSportType1.vue";
import leveLv from "src/components/league_ranking/component/level/index.vue"; //联赛等级
import { columnsChampion } from "src/components/league_ranking/config/columns.js"; //列表展示columns
import { src_components_league_ranking_variable_componsable_fn } from "src/components/league_ranking/componsable/variable.js";
import { src_components_league_ranking_componsable_fn } from "src/components/league_ranking/componsable/index.js";
const tableNoData = defineAsyncComponent(() =>
  import("project/caopan/components/table/tableNoData.vue")
);
const base_payload = src_components_league_ranking_variable_componsable_fn();
const {
  window_size_info,
  sta_switch,
  // 优先级接口参数
  ts,
  // 优先级排序
  visible,
  //修改排序值
  is_modifying_playrules_order,
  //当前联赛等级
  checked_level,
  //列表数据
  data_list,
  columns,
  //加载状态
  tabledata_loading,
  //筛选联赛名称
  tournament_name,
  //筛选出来的内容
  tournament_data,
  //模糊查搜索中
  tournament_loading,
  //params参数
  params,
  //列表数量
  total,
  //防抖
  is_post,
  // 当前点击row的id
  row_click_id,
  //侧边筛选
  left_show,
  //table_hover提示
  table_hover,
  //输入排序验证
  sort_trigger,
  //上一次请求的params
  old_params,
} = base_payload;
const {
  get_level,
  click_back,
  row_click,
  set_tournament_name,
  tournament_name_clear,
  all_show,
  all_cancel,
  cancel_btn,
  Cancel_textanda,
  table_sort,
  submit_set_table_sort,
  cross_page_sort,
  set_sort_beg,
  abort_modify_playrules_order,
  submit_modify_playrules_order,
  modify_playrules_order,
  priority_change,
  process,
  change_page_num,
  change_page_size,
  confirm_btn,
  get_tournament_name_data,
  Sure_textanda,
  swith_change,
  top_morning,
} = src_components_league_ranking_componsable_fn({ ...base_payload });
</script>

<style lang="scss" scoped>
@import url(src/components/league_ranking/css/index.scss);
</style>
