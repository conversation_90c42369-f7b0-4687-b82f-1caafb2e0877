
export const query_form_date_tab_click = (payload, value) => {
    // payload 传递方法
    const { set_history_startTimeFrom, set_resp_serverTime, serverTime,  selected_date_tab, history_startTimeFrom, first_day_startTimeFrom, set_serverTime} = payload
    set_resp_serverTime()
    // 17847 #【操盘】lanko#柬埔寨地区晚上12点操盘后台自动登出
    let timestamp = new Date(serverTime.value).getTime()
    let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(
      serverTime.value
    );
    let [y_, m_, d_, h_, mm_, s_] = format_date_base_gmt_add_8(
      timestamp
    );
    selected_date_tab.value = value;
    if (!history_startTimeFrom.value) {
      set_history_startTimeFrom(first_day_startTimeFrom.value);
    }
    if (d_ == d && serverTime.value) {
      emit_query_period_change_not_tab_8();
    } else {
      if (!serverTime.value) {
        set_serverTime(timestamp)
        emit_query_period_change_not_tab_8();
        return
      } else {
        Message.error(`${i18n_t('mixins.hint_4')}`);//'未登录或已过期，请重新登录'
        set_serverTime(timestamp)
        window.location.href = '/'
      }
    }
  }