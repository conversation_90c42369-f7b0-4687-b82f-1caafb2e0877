import { api_system_set } from "src/api/index.js";
import { Message, lodash, show_msg } from "src/output/common/project-common.js";

/**
 * @description:默认设置接口
 * @param {*} levelid
 * @return {*}
 */
const defalut_set_ok = (payload, res) => {
  const { edit_data } = payload;
  let { code, msg, data } = lodash.get(res, "data");
  if (code == 200) {
    edit_data.value = JSON.parse(JSON.stringify(data));
  } else {
    show_msg(code, msg);
  }
};
export const defalut_set = async (payload, type) => {
  const { edit_data } = payload;
  // type 2 默认设置
  if (type == 2) {
    try {
      let res = await api_system_set.get_defaultConfig();
      defalut_set_ok(payload, res);
    } catch (err) {
      console.log(err);
      Message.error(err);
    }
  } else {
    edit_data.value = {
      volumePercentage: null, // 漏单比例值
      levelId: [], // 用户风控标签
      bqStatus: 2, // 投注特征标签漏单比例设置
      qjStatus: 2, // 全局漏单比例设置
      minMoney: null, // 投注额区间小值
      maxMoney: null, // 投注额区间大值
      merchantsIds: null, // 商户ID
    };
  }
};
