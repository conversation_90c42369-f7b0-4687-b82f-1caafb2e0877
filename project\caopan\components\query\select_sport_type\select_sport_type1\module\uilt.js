
import {lodash} from 'src/output/common/project-common.js';
import { get_champion_activem_atchNum } from 'src/output/common/store-common.js';

// 获取球种徽标数量
export const badge_num = (id) => {
  const num = lodash.get(get_champion_activem_atchNum().value, `typeDetails[${id}]`)
  if (!num) {
    return '';
  }
  const numericCount = Number(num);
  if (isNaN(numericCount) || numericCount <= 0) {
    return '';
  }
  return numericCount > 99 ? '99+' : String(numericCount);
}