import { onMounted } from "vue";
import {
  change,
  submit,
  hidden_select,
  show_select,
  select_item,
  select_trader,
  checkAllGroupChange,
  toggle_modal,
  reset,
  init_query,
  handleCheckAll,
} from "project/caopan/components/query/match_left_query4/module/other.js";
import { emit_generator } from "project/caopan/components/query/match_left_query4/emit/index.js";
import { watcher_register } from "project/caopan/components/query/match_left_query4/watch/index.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";

export const project_caopan_components_query_match_left_query4_componsable_fn =
  (raw_payload) => {
    const emits = emit_generator(raw_payload);
    const payload = {
      ...raw_payload,
      ...emits,
    };

    src_componsable_util_watcher_register_componsable_fn(
      watcher_register(payload)
    );

    onMounted(() => {
      init_query(payload);
    });
    return {
      change: () => change(payload),
      submit: () => submit(payload),
      hidden_select: () => hidden_select(payload),
      show_select: () => show_select(payload),
      select_item: (item) => select_item(payload, item),
      select_trader: (item) => select_trader(payload, item),
      checkAllGroupChange: (data) => checkAllGroupChange(payload, data),
      toggle_modal: () => toggle_modal(payload),
      reset: (num) => reset(payload, num),
      handleCheckAll: () => handleCheckAll(payload),
    };
  };
