<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date           : 2021-01-29 20:55:56
 * @LastEditTime : 2021-06-23 23:49:44
 * @LastEditors  : Please set LastEditors
 * @Description    : 网球
 * @FilePath     : /trader-tool-pro/src/pages/operation_set/pre_sale_set/component/right_info_event/sportid_5.vue
-->
<template>
  <div>
    <!-- 主客队比分统计 -->
    <div>
      <score-statistics
        :time_options="time_options"
        :statistics_code_list="statistics_code_list"
        :right_detail_obj="right_detail_obj"
        @change_ws_data_source="change_ws_data_source"
      ></score-statistics>
    </div>
    <!-- 事件流 -->
    <div
      class="row mt10x p-jcsb stHider p-lh--sm p-mb--xxs pl15x pr15x border-bottom-light border-top-light"
    >
      <Select
        class="bg-panda-field-grey"
        size="small"
        style="width: 100px; height: 24px; margin: 7px 0"
        v-model="btn_model_1"
      >
        <template v-for="item in time_options">
          <Option
            :key="item.value"
            :value="item.value"
            v-if="
              item.value <=
              (right_detail_obj.matchPeriodId > 50
                ? 10
                : right_detail_obj.matchPeriodId)
            "
          >
            {{ item.label }}
            <!-- 赛事阶段下拉框 -->
          </Option>
        </template>
      </Select>
      <div class="flex p1-aic p-lh--lx p-gutter-mr4x">
        <img
          class="cp h18x"
          :src="
            get_icon_url({
              val: 1,
              sport_id: sport_id,
              status: get_icon_status(icon_opts.includes(1)),
            })
          "
          alt=""
          :title="$t('score_center.sportId_5_9')"
          @click="light_icon({ icon_opts, val: 1 })"
        />
        <img
          class="cp h18x"
          :src="
            get_icon_url({
              val: 2,
              sport_id: sport_id,
              status: get_icon_status(icon_opts.includes(2)),
            })
          "
          alt=""
          :title="$t('score_center.sportId_5_10')"
          @click="light_icon({ icon_opts, val: 2 })"
        />
        <img
          class="cp h18x"
          :src="
            get_icon_url({
              val: 3,
              sport_id: sport_id,
              status: get_icon_status(icon_opts.includes(3)),
            })
          "
          alt=""
          :title="$t('score_center.sportId_5_11')"
          @click="light_icon({ icon_opts, val: 3 })"
        />
        <img
          class="cp h18x"
          :src="
            get_icon_url({
              val: 4,
              sport_id: sport_id,
              status: get_icon_status(icon_opts.includes(4)),
            })
          "
          alt=""
          :title="$t('score_center.sportId_5_14')"
          @click="light_icon({ icon_opts, val: 4 })"
        />
        <img
          class="cp h18x"
          :src="
            get_icon_url({
              val: 5,
              sport_id: sport_id,
              status: get_icon_status(icon_opts.includes(5)),
            })
          "
          alt=""
          :title="$t('score_center.sportId_5_15')"
          @click="light_icon({ icon_opts, val: 5 })"
        />
      </div>
    </div>
    <div
      class="p1-oxa p-f1 p-px--dm pande-text-788299 ml15x mr15x"
      style="height: 300px"
      v-if="event_list && event_list.length"
    >
      <div v-for="(i, index) in event_list" :key="index">
        <div v-if="i.dataSourceCode == data_source">
          <!-- 判断是否选中数据源 -->
          <div
            v-if="btn_model_1 == '-1' ? true : i.matchPeriodId == btn_model_1"
          >
            <!-- 判断是否选中赛事阶段 -->
            <div
              v-if="
                should_show.length == 0
                  ? true
                  : should_show.includes(i.eventCode)
              "
            >
              <!-- 判断是否选中图标显示 -->
              <div class="flex mt5x" v-if="i.homeAway && i.homeAway == 'home'">
                <div
                  class="flex p-jcsb p-r-w--md1 p-r-bgc--dark p-lh--xxs p-h--xxs"
                >
                  <!-- 赢得第X分 -->
                  <span class="p-f1 p-ellipsis p-px--xxs">
                    <span
                      v-if="!compute_i18n_type"
                      :title="`赢得第${i.remark}分`"
                      >赢得第{{ i.remark }}分</span
                    >
                    <span v-else :title="`Win ${i.remark} ' point`"
                      >Win {{ i.remark }} ' point</span
                    >
                    <!-- 显示角球 红牌 黄牌 换人 次数数 -->
                  </span>
                  <!-- 比分 图标 -->
                  <span
                    style="width: 45px"
                    class="p-dif p-jcc p-fb--sm items-center"
                    v-if="i.eventCode === 'tennis_score_change'"
                  >
                    <!-- 主队进球显示 -->
                    <span style="width: 15px" class="tac">
                      {{ i.homeSecondNumber }}
                    </span>
                    <!-- 图标显示 -->
                    <img
                      style="height: 14px"
                      :src="computed_icon(i, event_keys)"
                      alt=""
                    />
                    <!-- 客队进球显示 -->
                    <span style="width: 15px" class="tac">
                      {{ i.awaySecondNumber }}
                    </span>
                  </span>

                  <span
                    class="p-dif p-jcc p-fb--sm items-center"
                    v-else-if="i.eventCode === 'break'"
                  >
                    <span>
                      {{ i.homeFirstNumber }}
                      <!-- 主队破发显示 -->
                    </span>
                    <img
                      style="height: 14px"
                      :src="computed_icon(i, event_keys)"
                      alt=""
                    />
                    <span>
                      {{ i.awayFirstNumber }}
                      <!-- 客队破发显示 -->
                    </span>
                  </span>
                  <span class="p-dif p-jcc p-fb--sm items-center" v-else>
                    <span>
                      {{ i.eventHomeNumber }}
                      <!-- 主队进球显示 -->
                    </span>
                    <img
                      style="height: 14px"
                      :src="computed_icon(i, event_keys)"
                      alt=""
                    />
                    <span>
                      {{ i.eventAwayNumber }}
                      <!-- 客队进球显示 -->
                    </span>
                  </span>
                </div>
                <q-icon
                  name="play_arrow"
                  style="
                    font-size: 35px;
                    height: 20px;
                    transform: translateX(-12px);
                  "
                  class="p-r-tc--dark"
                ></q-icon>
              </div>
              <div
                class="flex p-jcfe mt5x"
                v-if="i.homeAway && i.homeAway == 'away'"
              >
                <div
                  class="flex p-jcsb p-r-w--md1 p-r-bgc--dark p-lh--xxs p-h--xxs pr10x"
                >
                  <span
                    class="p-dif p-jcc p-fb--sm items-center"
                    v-if="i.eventCode === 'tennis_score_change'"
                  >
                    <span
                      style="width: 45px"
                      class="p-dif p-jcc p-fb--sm items-center"
                      v-if="i.eventCode === 'tennis_score_change'"
                    >
                      <!-- 主队进球显示 -->
                      <span style="width: 15px" class="tac">
                        {{ i.homeSecondNumber }}
                      </span>
                      <!-- 图标显示 -->
                      <img
                        style="height: 14px"
                        :src="computed_icon(i, event_keys)"
                        alt=""
                      />
                      <!-- 客队进球显示 -->
                      <span style="width: 15px" class="tac">
                        {{ i.awaySecondNumber }}
                      </span>
                    </span>
                  </span>

                  <span
                    class="p-dif p-jcc p-fb--sm items-center"
                    v-else-if="i.eventCode === 'break'"
                  >
                    <span>
                      {{ i.homeFirstNumber }}
                      <!-- 主队破发显示 -->
                    </span>
                    <img
                      style="height: 14px"
                      :src="computed_icon(i, event_keys)"
                      alt=""
                    />
                    <span>
                      {{ i.awayFirstNumber }}
                      <!-- 客队破发显示 -->
                    </span>
                  </span>
                  <span class="p-dif p-jcc p-fb--sm items-center" v-else>
                    <span>
                      {{ i.eventHomeNumber }}
                      <!-- 主队进球显示 -->
                    </span>
                    <img
                      style="height: 14px"
                      :src="computed_icon(i, event_keys)"
                      alt=""
                    />
                    <span>
                      {{ i.eventAwayNumber }}
                      <!-- 客队进球显示 -->
                    </span>
                  </span>

                  <!-- 赢得第X分 -->
                  <span v-if="!compute_i18n_type" :title="`赢得第${i.remark}分`"
                    >赢得第{{ i.remark }}分</span
                  >
                  <span v-else :title="`Win ${i.remark} ' point`"
                    >Win {{ i.remark }} ' point</span
                  >
                </div>
                <q-icon
                  name="play_arrow"
                  style="
                    font-size: 35px;
                    height: 20px;
                    transform: translateX(12px) rotate(0.5turn);
                  "
                  class="p-r-tc--dark p-ro1"
                ></q-icon>
              </div>
              <!-- 赛事阶段分割线 -->
              <div
                class="dashed"
                v-if="
                  computed_matchPeriodId_show(i.matchPeriodId) &&
                  period_id[i.matchPeriodId] == index
                "
              >
                <span class="after"></span>
                <span class="title">
                  {{ computed_matchPeriodId(i.matchPeriodId) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row p-jcc pande-text-788299" v-else>
      <span>{{ $t("score_center.text_7") }}</span
      ><!-- 无实时事件流 -->
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { i18n_t } from "src/output/common/project-common.js";
import scoreStatistics from "src/pages/operation_set/pre_sale_set/component/right_info_event/module/score_statistics.vue"; // 统计比分组件
import { project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_componsable_fn } from "project/caopan/pages/operation_set/pre_sale_set/component/right_info_event/mixins/componsable/index.js";
import { project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_variable_componsable_fn } from "project/caopan/pages/operation_set/pre_sale_set/component/right_info_event/mixins/componsable/variable.js";

const props = defineProps({
  right_detail_obj: {
    type: Object, // 指定为对象类型
    required: true, // 如果是必传属性
  },
  sport_id: {
    type: [String, Number], // 可以是字符串或数字
    required: true,
  },
  event_list: {
    type: Array, // 指定为数组类型
    default: () => [], // 默认值为空数组
  },
  period_id: {
    type: [String, Number], // 可以是字符串或数字
    default: null, // 默认值为 null
  },
});

const time_options = ref([
  { label: `${i18n_t("common.full_time")}`, value: "-1" },
  { label: "SET1", value: "8" },
  { label: "SET2", value: "9" },
  { label: "SET3", value: "10" },
  { label: "SET4", value: "11" },
  { label: "SET5", value: "12" },
]);

const statistics_code_list = ref([
  //所有的比分统计类型
  { key: "matchScore", name: `${i18n_t("score_center.sportId_5_1")}` }, // 盘比分
  { key: "setScore", name: `${i18n_t("score_center.sportId_5_2")}` }, //  局比分
  { key: "currentScore", name: `${i18n_t("score_center.sportId_5_3")}` }, //当前局比分
  {
    key: "servesScoredCount",
    name: `${i18n_t("score_center.sportId_5_4")}`,
  }, //发球得分次数
  {
    key: "servesFaultCount",
    name: `${i18n_t("score_center.sportId_5_5")}`,
  }, //发球失败次数
  {
    key: "breakSuccessCount",
    name: `${i18n_t("score_center.sportId_5_6")}`,
  }, //破发成功次数
  {
    key: "breakPointCount",
    name: `${i18n_t("score_center.sportId_5_7")}`,
  }, //破发点
  {
    key: "breakSuccessRate",
    name: `${i18n_t("score_center.sportId_5_8")}`,
  }, //破发率
]);

const map_opts = ref({
  1: ["game_end"], //显示保发
  2: ["tennis_score_change"], //显示得分
  3: ["double_fault"], ////双发失误
  4: ["ace"], // ace罚球
  5: ["break"], // 破发
});

const event_keys = ref([
  { key: ["game_end"] },
  { key: ["tennis_score_change"] },
  { key: ["double_fault"] },
  { key: ["ace"] },
  { key: ["break"] },
]);

const payload =
  project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_variable_componsable_fn(
    {
      map_opts,
    }
  );
const { icon_opts, btn_model_1, compute_i18n_type, data_source, should_show } =
  payload;

const {
  change_ws_data_source,
  get_icon_url,
  get_icon_status,
  light_icon,
  computed_matchPeriodId_show,
  computed_matchPeriodId,
  computed_icon,
} =
  project_caopan_pages_operation_set_pre_sale_set_component_right_info_event_mixins_componsable_fn(
    {
      time_options,
      statistics_code_list,
      map_opts,
      event_keys,
      right_detail_obj: props.right_detail_obj,
      sport_id: props.sport_id,
      event_list: props.event_list,
      period_id: props.period_id,
    }
  );
</script>

<style lang="scss" scoped>
@import "src/pages/operation_set/pre_sale_set/component/right_info_event/common.scss";
</style>
