import { onMounted } from 'vue';
import {
  sport_type_click,
  compute_show_num,
  compute_show_champion_plays,
  compute_base_per_sportType_style,
  mutation_sport_type_constant,
  findValidSport,
  cpomputed_show_badge,
} from 'project/caopan/components/query/select_sport_type/select_sport_type1/module/index.js';
import {badge_num} from 'project/caopan/components/query/select_sport_type/select_sport_type1/module/uilt.js';
import { rebuild_sport_type_constant } from 'project/caopan/components/query/select_sport_type/select_sport_type1/api-request/rebuild_sport_type_constant.js';
import { 
  project_caopan_componsable_router_cache_componsable_fn,
  src_componsable_util_watcher_register_componsable_fn,
} from 'src/output/common/componsable-common.js';
import { watcher_register } from 'project/caopan/components/query/select_sport_type/select_sport_type1/watch/index.js';
import { computed_generator } from 'project/caopan/components/query/select_sport_type/select_sport_type1/computed/index.js';

export const project_caopan_components_query_select_sport_type_componsable_fn = raw_payload => {
  const { get_router_cache_value_by_path } = project_caopan_componsable_router_cache_componsable_fn(raw_payload);

  const { sport_type_constant, window_size_info, get_sport_type_visible } = computed_generator();

  const payload = {
    ...raw_payload,
    rebuild_sport_type_constant,
    findValidSport,
    sport_type_constant,
  };
  onMounted(() => {
    compute_show_num(payload); // 是否展示赛事数量
    compute_show_champion_plays(payload); // 是否展示冠军红点
    compute_base_per_sportType_style(payload); // 处理样式
    mutation_sport_type_constant(payload); // 根据路由展示头部球类  将头部球类存入vuex
    //   // rebuild_sport_type_constant();  //
  });

  src_componsable_util_watcher_register_componsable_fn(watcher_register(payload));

  return {
    sport_type_click: (item, index) => sport_type_click(payload, item, index),
    cpomputed_show_badge: (id) => cpomputed_show_badge(payload, id),
    badge_num: (id) => badge_num(id),
    sport_type_constant,
    window_size_info,
  };
};
