import { api_operate } from "src/api/index.js";
import { Message, lodash } from "src/output/common/project-common.js";
import { computed_table } from "src/components/league_ranking/module/computed.js";
import { clearSort } from "src/components/league_ranking/module/other.js";
/**
 * @description: 获取列表数据
 * @param {type}
 * @return {type}
 */
export const get_data_list = (payload) => {
  const {
    tabledata_loading,
    params,
    is_modifying_playrules_order,
    total,
    old_params,
    is_post,
    data_list,
  } = payload;
  tabledata_loading.value = true; //加载状态
  // if (is_modifying_playrules_order.value) {
  //   params.value.size = Math.ceil(total.value / 10) * 10 || 50
  // }
  api_operate
    .getTournamentList(params.value)
    .then((res) => {
      let code = lodash.get(res, "data.code");
      let msg = lodash.get(res, "data.msg");
      let data = lodash.get(res, "data.data");
      if (code == 200 || code == "0000000") {
        clearSort(payload);
        old_params.value = lodash.cloneDeep(params.value);
        tabledata_loading.value = false; //加载状态
        is_post.value = false; //防抖
        data_list.value = data.records ? computed_table(payload, data.records) : []; //列表数据
        if (!is_modifying_playrules_order.value) {
          total.value = data.total ? data.total * 1 : 0; //列表总条数
        }
        console.log(data_list, '+data_list0')
      } else {
        Message.error(`${msg}`);
      }
    })
    .catch((err) => {
      console.log(err);
      tabledata_loading.value = false; //加载状态
      is_post.value = false;
      total.value = 0; //总条数
      Message.error(`${err}`);
    });
};
