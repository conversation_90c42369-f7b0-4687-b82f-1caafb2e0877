
import {ref,computed,watch,reactive,useTemplateRef} from 'vue'
///payload上下文
export  const  computed_generator=(payload)=>{
  const { options,old_value,select } = payload;

  ///TIPS：换成这样的变量并返回
  const select_hidden = computed(() => {
    let options_len = options.value.length,
      select_len = select.value.length;
    if (options_len === 1) return;
    if (
      Array.isArray(select.value) &&
      select.value.includes("all") &&
      (!old_value.value.includes("all") || select_len == 1) // 判断勾选全部
    ) {
      select.value = options.value.map(x => x.value);
      old_value.value = [...select.value];
      emit("input", select.value);
    } else if (
      Array.isArray(select.value) &&
      !select.value.includes("all") &&
      old_value.value.includes("all") // 判断不勾选全部
    ) {
      select.value = [];
      old_value.value = [...select.value];
      emit("input", select.value);
    } else if (
      select_len + 1 === options_len &&
      !select.value.includes("all")
    ) {
      // 判断勾选其他所有
      select.value.unshift("all");
      old_value.value = [...select.value];
      emit("input", select.value);
    } else if (
      select_len + 1 === options_len &&
      select.value.includes("all")
    ) {
      // 判断不勾选其他所有
      select.value.splice(
        select.value.findIndex(x => x === "all"),
        1
      );
      old_value.value = [...select.value];
      emit("input", select.value);
    } else {
      emit("input", select.value);
    }
  });

  return {
    select_hidden,
  }
}
