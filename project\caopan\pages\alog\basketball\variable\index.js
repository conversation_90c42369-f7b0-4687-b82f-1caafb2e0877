import{ref }  from 'vue' ;
import axios from "src/api/common/axioswarpper.js"; //axios他实例封装
import { dataSorce as dataSorce_default, match_timer_status as match_timer_status_default} from "project/caopan/pages/alog/basketball/config/config.js";
export const  create_base_state_instance_fn=(payload)=>{
  const WEB_ENV = axios.prototype.WS_DOMAIN_FRNGKONG_1
  const showMore = ref(false)  ;
  const input_data = ref({})  ;
  const reverse_input_data = ref({})  ;
  const reverse = ref({})  ;
  const is_post_reverse = ref(false)  ;
  const is_post_calculate = ref(false)  ;
  const is_post_apply = ref(false)  ;
  const init_status = ref(false)  ;
  const dataList = ref([])  ;
  const websocket_connection_1_url = ref(WEB_ENV)  ;
  const text_timer_num = ref("")  ;
  const text_period = ref("")  ;
  const lineVersion = ref(false)  ;
  const categorySetId = ref(11001)  ;
 // 必须按照这个数组排序

  const is_action_ws = ref(false)  ;
  const rateMargin = ref(0)  ;
  const oldParams_sup_ge = ref({})  ;
  const CMinputParams = ref({})  ;
  const old_ao_RevParam = ref({})  ;
  const old_reverse = ref({})  ;
  const applyNum = ref(0)  ;
  const applyNumStatus = ref(false)  ;
  const applyNumTimer = ref(null)  ;
  const select_mode = ref(false)  ;
   //选择哪种模式 412 410 220
 const  mode = ref("412") ;
  const stageObj = ref({})  ;
   //用户是否修改了input框
 const  is_change = ref(false) ;
   //是否点击了copy按钮
 const  followedStatus = ref(0) ;
   // 是否开启点击reverse按钮之后自动copy--> apply操作
 const  autoApply = ref(false) ;
  const matchType = ref('0')  ;
  const aoId = ref("")  ;
 // 隐藏原始赔率状态
  const hidenOrodds = ref(false)  ;
  const dataSorceValue = ref("LS")  ;
  const pause_status = ref(false)  ;
  const edit_stauts = ref(false)  ;
  const wsParams = ref({
    command: "40001",
    para: {},
  })  ;
  // const match_timer_status = ref({
  //   // 0 为比分中心，1为手动比分
  //   isManualSocre: 0,
  //   // 结算时间分钟
  //   periodTimer: "0",
  //   // 阶段 0 早 1滚
  //   period: 0,
  //   checked_value: 0,
  // })  ;
  const dataSorce =ref(dataSorce_default) 
  const match_timer_status =ref(match_timer_status_default)
  return {
    showMore,
    input_data,
    reverse_input_data,
    reverse,
    is_post_reverse,
    is_post_calculate,
    is_post_apply,
    init_status,
    dataList,
    websocket_connection_1_url,
    text_timer_num,
    text_period,
    lineVersion,
    categorySetId,
   // 必须按照这个数组排序
    is_action_ws,
    rateMargin,
    oldParams_sup_ge,
    CMinputParams,
    old_ao_RevParam,
    old_reverse,
    applyNum,
    applyNumStatus,
    applyNumTimer,
    select_mode,
   //选择哪种模式 412 410 220
    mode,
    stageObj,
   //用户是否修改了input框
    is_change,
   //是否点击了copy按钮
    followedStatus,
   // 是否开启点击reverse按钮之后自动copy--> apply操作
    autoApply,
    matchType,
    aoId,
   // 隐藏原始赔率状态
    hidenOrodds,
    dataSorceValue,
    pause_status,
    edit_stauts,
    wsParams,
    match_timer_status,
    dataSorce
  }
}
