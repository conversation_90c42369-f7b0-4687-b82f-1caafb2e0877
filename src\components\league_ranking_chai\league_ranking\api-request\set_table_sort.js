import { api_operate } from "src/api/index.js";
import { Message, lodash } from "src/output/common/project-common.js";
/**
 * @description: 排序更改接口
 * @param {*} params 参数
 * @param {*} callBack 成功后的回调
 * @return {*}
 */
export const set_table_sort = (payload, params, callBack) => {
  const { tabledata_loading } = payload;
  tabledata_loading.value = true; //加载状态
  api_operate
    .updateTournament(params)
    .then((res) => {
      tabledata_loading.value = false; //加载状态
      let code = lodash.get(res, "data.code");
      let msg = lodash.get(res, "data.msg");
      if (code == 200 || code == "0000000") {
        Message.success(`${msg}`);
        callBack();
      } else {
        console.log(res);
        Message.error(`${msg}`);
      }
    })
    .catch((err) => {
      console.log(err);
      Message.error(`${err}`);
    });
};
