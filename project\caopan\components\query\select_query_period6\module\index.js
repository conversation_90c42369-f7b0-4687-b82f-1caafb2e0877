export const compute_current_h_gmt_add_8 = (payload) => {
  const {
    get_serverTime,
    current_h,
    current_promise,
    first_day_startTimeFrom,
    show_live_odd,
    show_yesterday,
    query_form_date_arr,
    show_q_date_component,
    selected_date_tab,
    history_date,
    get_history_startTimeFrom,
    icon_count,
  } = payload;
  let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(get_serverTime().value);
  current_h.value = Number(h);
};
export const init_query_form_date_arr = (payload) => {
  const {
    get_serverTime,
    current_h,
    current_promise,
    first_day_startTimeFrom,
    show_live_odd,
    show_yesterday,
    query_form_date_arr,
    show_q_date_component,
    selected_date_tab,
    history_date,
    get_history_startTimeFrom,
    icon_count,
  } = payload;
  let arr = [];
  let timestamp = new Date().getTime();
  if (get_serverTime().value) {
    typeof current_promise.value == "function" && current_promise();
  }
  let [y1, m1, d1, h1, mm1, s1] = format_date_base_gmt_add_8(
    get_serverTime().value ? get_serverTime().value : timestamp
  );
  // 当天中午12点
  let fmt_8_h_12 = new Date(`${y1}-${m1}-${d1} 12:00:00`).getTime();
  let t_s = ""; // 开始时间戳
  if (Number(h1) < 12) {
    // 美国的前一天 的凌晨零点   开始时间 是中国前一天的中午12点
    t_s = fmt_8_h_12 - 24 * 60 * 60 * 1000;
  } else {
    // 美国的同一天 的凌晨零点   开始时间 是中国 当天的中午12点
    t_s = fmt_8_h_12;
  }
  /**
   * 因为 t_s 转换后 日月  再 中国和美国一样的 ，所以 不在做处理
   */
  let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(t_s);

  first_day_startTimeFrom.value = t_s;

  if (route.name !== "trader_manage_liveOddSupport") {
    for (let i = 0; i < 1; i++) {
      let time_str = t_s + i * 24 * 60 * 60 * 1000;
      let time = new Date(time_str);
      let [y_, m_, d_, h_, mm_, s_] = format_date_base_gmt_add_8(time_str);

      arr.push({
        value: i,
        label: `${m_}${i18n_t("selectPeriodTab.Tab_1")}${d_}${i18n_t(
          "selectPeriodTab.Tab_2"
        )}`,
        true_label: `${m_}${i18n_t("selectPeriodTab.Tab_1")}${d_}${i18n_t(
          "selectPeriodTab.Tab_2"
        )}`,
        nav_label: `${m_}${i18n_t("selectPeriodTab.Tab_1")}${d_}${i18n_t(
          "selectPeriodTab.Tab_2"
        )}`,
        startTimeFrom: time.getTime(),
        tab: i,

        historyFlag: 0,
        week_str: format_week(time.getTime()),
      });
    }

    arr[0].label = i18n_t("selectPeriodTab.Tab_4");
    // arr[1].label =  i18n_t('selectPeriodTab.Tab_5');
    arr.push({
      value: 7,
      tab: 7,
      label: i18n_t("selectPeriodTab.Tab_18"),
      startTimeFrom: new Date(t_s + 1 * 24 * 60 * 60 * 1000).valueOf(),
      time: new Date(t_s + 1 * 24 * 60 * 60 * 1000).valueOf(),
    });
  }

  if (show_live_odd.value) {
    if (route.name !== "trader_manage_morning_o") {
      arr.unshift({
        value: 9,
        label: i18n_t("common.inPlay"),
        true_label: `${m}${i18n_t("selectPeriodTab.Tab_1")}${d}${i18n_t(
          "selectPeriodTab.Tab_2"
        )}`,
        nav_label: i18n_t("common.inPlay"),
        startTimeFrom: t_s,
        tab: 9,
        historyFlag: 0,
        week_str: format_week(t_s),
      });
    }
  }
  //昨日
  if (show_yesterday.value) {
    let [__y, __m, __d, __h, __mm, __ss] = format_date_base_gmt_add_8(
      t_s - 24 * 60 * 60 * 1000
    );
    arr.unshift({
      value: 10,
      label: `${__m}${i18n_t("selectPeriodTab.Tab_1")}${__d}${i18n_t(
        "selectPeriodTab.Tab_2"
      )}`,
      true_label: `${__m}${i18n_t("selectPeriodTab.Tab_1")}${__d}${i18n_t(
        "selectPeriodTab.Tab_2"
      )}`,
      nav_label: `${__m}${i18n_t("selectPeriodTab.Tab_1")}${__d}${i18n_t(
        "selectPeriodTab.Tab_2"
      )}`,
      startTimeFrom: +new Date(t_s - 24 * 60 * 60 * 1000),
      tab: 10,
      historyFlag: 0,
      week_str: format_week(t_s - 24 * 60 * 60 * 1000),
    });
  }
  query_form_date_arr.value = arr;
};
export const watch_date_and_change_query_form_date_arr = (payload) => {
  const {
    get_serverTime,
    current_h,
    current_promise,
    first_day_startTimeFrom,
    show_live_odd,
    show_yesterday,
    query_form_date_arr,
    show_q_date_component,
    selected_date_tab,
    history_date,
    get_history_startTimeFrom,
    icon_count,
  } = payload;
  let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(get_serverTime().value);

  current_h.value = mm;
};
export const comoute_showrightdetail_by_router = (payload) => {};
export const handle_history_date_confirm = (payload, val) => {
  const {
    get_serverTime,
    current_h,
    current_promise,
    first_day_startTimeFrom,
    show_live_odd,
    show_yesterday,
    query_form_date_arr,
    show_q_date_component,
    selected_date_tab,
    history_date,
    get_history_startTimeFrom,
    icon_count,
  } = payload;
  show_q_date_component.value = false;
  emit_query_period_change_when_tab_8();
};

export const emit_query_period_change_not_tab_8 = (payload) => {
  const {
    get_serverTime,
    current_h,
    current_promise,
    first_day_startTimeFrom,
    show_live_odd,
    show_yesterday,
    query_form_date_arr,
    show_q_date_component,
    selected_date_tab,
    history_date,
    get_history_startTimeFrom,
    icon_count,
  } = payload;
  // 历史 数据  支持 选定 具体时间 和 起始时间 后做请求 ，避免重复请求
  let item = lodash.cloneDeep(query_form_date_arr.value);
  let val = lodash.find(item, (o) => {
    return o.value == selected_date_tab.value;
  });
  if (selected_date_tab.value == 9 && show_live_odd.value) {
    val.liveOddSupport = 1;
  }
  emit("query_period_change", val);
};
export const emit_query_period_change_when_tab_8 = (payload) => {
  const {
    get_serverTime,
    current_h,
    current_promise,
    first_day_startTimeFrom,
    show_live_odd,
    show_yesterday,
    query_form_date_arr,
    show_q_date_component,
    selected_date_tab,
    history_date,
    get_history_startTimeFrom,
    icon_count,
  } = payload;
  let val = {};
  let ts = Date.parse(history_date.value);
  set_history_startTimeFrom(ts);
  val = {
    startTimeFrom: ts,
  };
  val.tab = selected_date_tab.value;
  val.historyFlag = 1;
  val.week_str = format_week(ts);
  val.value = 8;
  val.label = `${i18n_t("selectPeriodTab.Tab_11")}`;

  let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(
    Date.parse(history_date.value)
  );
  val.history_date = history_date.value;
  val.true_label = `${m}${i18n_t("selectPeriodTab.Tab_1")}${d}${i18n_t(
    "selectPeriodTab.Tab_2"
  )}`;
  val.nav_label = `${m}${i18n_t("selectPeriodTab.Tab_1")}${d}${i18n_t(
    "selectPeriodTab.Tab_2"
  )}`;
  set_breadcrumbsPart3([val.label]); // 和头部一致
  emit("query_period_change", val);
};
export const query_form_date_tab_click = (payload, value, auto_order) => {
  const {
    get_serverTime,
    current_h,
    current_promise,
    first_day_startTimeFrom,
    show_live_odd,
    show_yesterday,
    query_form_date_arr,
    show_q_date_component,
    selected_date_tab,
    history_date,
    get_history_startTimeFrom,
    icon_count,
  } = payload;
  if (value == 7 && !auto_order) {
    // 其他早盘回调
    emit("on_other_date_select");
    return;
  }
  set_resp_get_serverTime();
  // 17847 #【操盘】lanko#柬埔寨地区晚上12点操盘后台自动登出
  let timestamp = new Date(get_serverTime().value).getTime();
  let [y, m, d, h, mm, s] = format_date_base_gmt_add_8(get_serverTime().value);
  let [y_, m_, d_, h_, mm_, s_] = format_date_base_gmt_add_8(timestamp);
  selected_date_tab.value = value;
  if (!get_history_startTimeFrom.value) {
    set_history_startTimeFrom(first_day_startTimeFrom.value);
  }
  if (d_ == d && get_serverTime().value) {
    emit_query_period_change_not_tab_8();
  } else {
    if (!get_serverTime().value) {
      set_get_serverTime(timestamp);
      emit_query_period_change_not_tab_8();
      return;
    } else {
      Message.error(`${i18n_t("mixins.hint_4")}`); //'未登录或已过期，请重新登录'
      set_get_serverTime(timestamp);
      window.location.href = "/";
    }
  }
};
export const icon_click = (payload) => {
  const {
    get_serverTime,
    current_h,
    current_promise,
    first_day_startTimeFrom,
    show_live_odd,
    show_yesterday,
    query_form_date_arr,
    show_q_date_component,
    selected_date_tab,
    history_date,
    get_history_startTimeFrom,
    icon_count,
  } = payload;
  if (icon_count.value) {
    emit("icon_click");
  }
};
