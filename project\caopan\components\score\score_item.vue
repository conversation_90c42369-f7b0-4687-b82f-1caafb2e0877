<!--
 * @FilePath: /project/caopan/components/score/score_item.vue
 * @Description: 列表比分模块
-->
<template>
  <div class="score-wrap">
    <span class="half-score">{{ computed_match_all_score(row_data) }}</span
    ><!-- 处理全场比分 -->
    <span class="ml5x">{{ computed_lastPeriod_score(row_data) }}</span
    ><!-- 处理半场比分 -->
  </div>
</template>

<script setup>

  import { lodash, i18n_t } from 'src/output/common/project-common.js';

  const props = defineProps({
    row_data: Object,
  });

  const computed_match_all_score = item => {
    let str = '-';
    if (!item) return str;
    if (lodash.get(item, 'score.home', '') + '') {
      return `${item.score.home}-${item.score.away}`;
    }
    return str;
  };

  //处理半场比分      如果加时赛 或 点球 展示加时或点球  没有的话展示半场比分
  const computed_lastPeriod_score = item => {
    let str = '';
    if (!item) return str;
    if (lodash.get(item, 'penaltyShootout.home', '') + '') {
      // 加时赛 点球大战 比分   over_time加时    penalty点球   banchang半场
      let name = item.periodScore_flag ? i18n_t('common.banchang') : i18n_t('common.over_time');
      str = `(${name} ${item.lastPeriodScore.home}-${item.lastPeriodScore.away} ${i18n_t('common.penalty')} ${item.penaltyShootout.home}-${
        item.penaltyShootout.away
      })`;
    } else if (lodash.get(item, 'lastPeriodScore.home', '') + '' && ![6, 31].includes(item.matchPeriodId)) {
      // 足球 手球  上半场6  中场休息31  不展示阶段比分

      // 半场比分
      str = `(${item.lastPeriodScore.home}-${item.lastPeriodScore.away})`;
    }
    return str;
  };
</script>

<style lang="scss" scoped>
  .score-wrap {
    display: inline-block;
    width: 100px;
    max-width: 120px;
  }
</style>
