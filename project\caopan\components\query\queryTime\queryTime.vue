<!--
 * <AUTHOR> pasta
 * @Date         : 2021-01-31 18:56:44
 * @Description  :
 * @FilePath: /project/caopan/components/query/queryTime.vue
-->
<template>
  <div class="content-time row items-center bg-panda-sport-type-primary use-ivew">
    <q-btn dense unelevated no-caps
      class="btn time-btn q-ml-sm panda-border-grey panda-query-date-tab-label panda-btn-primary-hover text-panda-text-light"
      :class="[timeType_num == (item.value + (item.type ? item.type : '')) ? 'active-btn' : 'panda-btn-dark-dense']"
      v-for="(item, index) in btnList" :key="index" @click="item_click(item)">
      {{ item.label }}
    </q-btn>
    <iDatePicker v-if="timeType_num == 'custom'" v-model="times" class="q-ml-sm" format="yyyy-MM-dd" type="daterange"
      placement="bottom-start" placeholder="" separator=" ~ " v-bind:options="optionsDate" :editable="false"
      @on-change="time_on_change" style="width: 260px;" />
  </div>
</template>

<script setup>
// import mixins from "project/caopan/mixins/index.js"; 


import { project_caopan_components_query_query_time_variable_componsable_fn }
  from "project/caopan/components/query/queryTime/componsable/variable.js";
import { project_caopan_components_query_query_time_componsable_fn }
  from "project/caopan/components/query/queryTime/componsable/index.js";
const props = defineProps({
  // 按钮列表
  btnList: {
    type: Array,
    default: () => []
  },
  // 默认选中
  defaulTime: {
    type: String,
    default: () => '999'
  },
  // 自定义时间的默认时间
  customTime: {
    type: Array,
    default: () => []
  }
})
//最基础的payload
const base_payload =
  project_caopan_components_query_query_time_variable_componsable_fn();

///解构出 参数，符合 template 所需要的
const {
  index,
  optionsDate,

} = base_payload;
const {
  time_on_change,
} = project_caopan_components_query_query_time_componsable_fn({
  ...base_payload
});
</script>

<style lang="scss" scoped>
@import url("project/caopan/components/query/queryTime/css/index-scoped.scss");
</style>
