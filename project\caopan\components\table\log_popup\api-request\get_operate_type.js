import {
  api_events_analyse,
  api_risk_control,
  api_order,
  api_settlement_center,
} from "src/api/index.js";

  import { Message, lodash, language_value } from "src/output/common/project-common";
    // 网球操作类型
    export const getOperateType = (payload) => {

      const { config, loading, isPost } = payload

      api_events_analyse
        .postTennisLogOperateType()
        .then((res) => {
          let { code, msg, data } = lodash.get(res, "data");
          if (code == 200) {
            data.forEach((el) => {
              el.value = language_value(el.names);
              el.label = language_value(el.names);
            });
            config.value.operateTypeList = data;
          } else {
            console.log(msg);
            Message.error(msg);
          }
        })
        .catch((err) => {
          console.error(err);
          Message.error(err);
        })
        .finally(() => {
          isPost.value = false;
          loading.value = false;
        });
    }
