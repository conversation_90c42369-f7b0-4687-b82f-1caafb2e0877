import { api_match } from "src/api/index.js";
import {
  lodash,
} from "src/output/common/project-common.js";

//  查询操盘手
export const init_trader_data = async (payload, traderName) => {
  const { liveTraderList } = payload;
  if (traderName) {
    try {
      let res = await api_match.post_preSale_queryTraderByLike({ traderName });
      let code = lodash.get(res, "data.code");
      if (code == 200) {
        let arr = lodash.get(res, "data.data") || [];
        liveTraderList.value = arr;
      }
    } catch (e) {
      console.error(e);
    } finally {
    }
  }
};
