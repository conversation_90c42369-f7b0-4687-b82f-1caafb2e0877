.red-back {
  top: -5px;
  left: 16px;
  background-color: #f44336;
  padding: 3px 4px;
  border-radius: 10px;
  height: 15px;
  line-height: 10px
}

.bg-panda-query-date-active .panda-query-date-tab-label:hover {
  background: #00a99766 !important;
  color: #fff !important;
  border: 0 none;
}

.text-panda-date-light.panda-query-date-tab-label:hover {
  color: var(--q-color-panda-primary) !important;
  border: solid 1px var(--q-color-panda-primary);
}

.p-icon-position {
  height: 100%;
  padding-left: 7px;
  padding-top: 5px;
}

.p-icon-bg {
  width: 24px;
  height: 24px;
  background: #28303F;
  border-radius: 2px;
  margin-left: 20px;
}

.text-panda-date-base {
  color: var(--q-color-panda-text-base);
}

.text-panda-date-light {
  color: var(--q-color-panda-text-light)
}

.panda-border-1px {
  border: solid 1px var(--q-color-panda-secondary);
}

.panda-query-date-tab-label {
  padding: 4px 15px;
}

.bg-panda-query-date-active {
  font-weight: 700;
}

.bg-panda-query-date-active .panda-query-date-tab-label {
  background-color: var(--q-color-panda-primary);
  border: none
}

.panda_icon_alert:before {
  color: #00AA98;
}

.panda_show_more {
  position: relative;
  display: inline-block;
}

.panda_show_more:before {
  position: absolute;
  bottom: -6px;
  right: 0;
  left: 0;
  margin: 0 auto;
  content: '';
  display: block;
  width: 0;
  height: 2px;
  background-color: var(--q-color-panda-primary);
  transition: all 0.3s;
}

.panda_show_more:hover:before {
  width: 100%;
}

.panda_show_more:hover {
  color: var(--q-color-panda-primary);

  [class^='panda_']:before {
    color: var(--q-color-panda-primary);
  }
}

.period__icon-badge {
  display: inline-block;
  min-width: 24px;
  height: 14px;
  line-height: 14px;
  background-color: #E23C39;
  color: #fff;
  border-radius: 7px;
  font-size: 12px;
  font-weight: normal;
  text-align: center;
  transform: translate(-3px, -5px);
}