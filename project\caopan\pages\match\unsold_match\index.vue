<!--
 * @Date           : 2020-06-06 17:28:26
 * @FilePath: /project/caopan/pages/match/unsold_match/index.vue
 * @description    :仅早盘开售 主页面
 * @Author: darwin
-->
<template>
  <div class="full-height full-width">
    <div>
      <select-sport-type></select-sport-type>
    </div>

    <main class="row p-fwnw">
      <!-- 左侧查询区域begin -->
      <div
        v-show="show_more_query"
        class="bg-panda-base-light4 text-panda-text-light p-fs0 p1-bs--1"
        style="border-right:1px solid var(--q-color-panda-border-color-secondary);margin-left: 1px"
        :style="{ width: show_more_query ? '250px' : '0' }"
      >
        <more-query
          v-if="get_dsc_options().length > 0"
          ref="moreQuery4"
          :current_match_status_constant="match_sell_status_arr"
          :odds_status_arr="odds_status_constant"
          :league_areaList="league_areaList"
          :operation_group="operation_group"
          :params="query_module"
          :sources_data="get_dsc_options()"
          :current_match_level_constant="current_match_level_constant"
          :league_name="league_name"
          @query="handle_query"
          @toggle_modal="toggle_modal"
        ></more-query>
      </div>
      <!-- 左侧查询区域end -->

      <!-- 列表主区域begin -->
      <div :style="main_component_style">
        <div class="p-mw--sm p-fg1">
          <!-- 查询条件区域  -->
          <div
            class="col-12 "
          >
            <!-- 日期选择组件 -->
            <select-query-period
              :default_select="query_time"
              :show_show_more_query_btn="false"
              :right_bnt="(right_bnt = true)"
              :bgclass="'bg-panda-expand-bg-color'"
              @query_period_change="query_period_change"
              @show_more_query_change="show_more_query_change"
            ></select-query-period>
            <!-- 刷新按钮 -->
            <div class="mr-10 yjks">
               <span @click="which_refresh" :class="bunlodin?'newActive':''"  class="panda_icon_refresh refresh_hover"></span>
            </div>
          </div>
          <!-- 表格展示开始 -->
          <div>
            <table-no-data
              ref="table-no-data"
              :table_no_data_visable="show_table_no_data"
              :tabledata_loading="tabledata_loading"
              :style="{ width: '100%', height: 'calc(100% - 77px)' }"
            ></table-no-data>
            <div class="panda-table-father">
              <q-table
                v-table_fixed_columns="{ left: 8 }"
                :data="tabledata"
                :columns="tablecolumns"
                dense
                table-class="tablescrollTop"
                row-key="name"
                class="bg-panda-base-dark text-panda-text-light panda-table panda-sticky-header-table full-width"
                table-header-class="panda-table panda-table-col-text-indent"
                :separator="separator"
                :pagination.sync="pagination"
                :no-data-label="data_label"
                :table-style="
                  `max-height: ${scroll_area_height}px; height: ${scroll_area_height}px `
                "
              >
                <template v-slot:body="props">
                  <tablerow
                    :props="props"
                    :matchStatus="matchStatus"
                    :play_data="play_data"
                    :is_top_#0
                    :tablecolumns="tablecolumns"
                    :show_right_detail_obj="show_right_detail_obj"
                    :select_sportId="select_sportId"
                    :queryform_form="queryform_form"
                    :isFavorite="isFavorite"
                    :match_sell_status_arr="match_sell_status_arr"
                    @table_item_click_expanded="table_item_click_expanded"
                    @item_click="table_item_click"
                    @p_ups="p_ups"
                    @submit_success="submit_success"
                    @collect_num="collect_num"
                  ></tablerow>
                </template>
              </q-table>
            </div>
          </div>
          <!-- 表格展示结束 -->
        </div>
      </div>
      <!-- 列表主区域end -->
    </main>
    <!-- 点击球队名称 查看赔率弹窗  开始 -->
    <q-dialog v-model="Pup_dialog">
      <div style="min-width:600px;height: auto;position: absolute" class="bg-panda-secondary" ref="touchAutomatic">
        <div class="dialog_hider" style="text-align: right;width:90%;height: 31px;" >
          <q-btn dense style="position: absolute;right: 0;" flat icon="close" clickable v-close-popup/>
        </div>
        <div class="dialog_title bg-panda-base-light pl10x border-bottom-light border-top-light" style="line-height: 30px;">
          <span>{{ compute_team_table_item_show_name(name_obj.leagueName) }}</span>
        </div>
        <div class="row border-bottom-col" style="line-height: 30px;">
          <div class="col-3 pl10x border-right-col"> {{ i18n_t("traderTable.qTable_2") }} </div>
          <div class="col pl10x border-right-col" v-for="(item, index) in pup_data" :key="`pup_data_${index}`">
            {{
            item.marketCategoryId == 1
            ? i18n_t("traderTable.qTable_3")
            : item.marketCategoryId == 2
            ? i18n_t("traderTable.qTable_12")
            : i18n_t("traderTable.qTable_11")
            }}
          </div>
        </div>
        <div class="row border-bottom-col">
          <div class="col-3 pl10x border-right-col" style="line-height:22px;">
            <p>
              <span>{{  compute_team_table_item_show_name(name_obj.homeTeamNames) }}</span>
            </p>
            <p>
              <span>{{ compute_team_table_item_show_name(name_obj.awayTeamNames) }}</span>
            </p>
            <p>{{ i18n_t("traderTable.with") }}</p>
          </div>
          <div v-for="(item, index) in pup_data" :key="`pup_data_${index}`" class="col border-right-col">
            <div v-show="item.marketCategoryId == 1 && item.standardMarketOddsBOList && item.standardMarketOddsBOList.length > 0">
              <div
                v-for="(i, index2) in item.standardMarketOddsBOList"
                :key="`standard_market_oddsBO_list_${index2}`"
                class="row"
              >
                <span
                  class="nameExpressionValue mr5x"
                  style="color: #E93D3D;width:30px;display: inline-block;"
                >
                  <span v-show="index2 == 0">{{ item.marketValue }}</span>
                </span>
                <span class="fieldOddsOriginValue">{{ i.oddsValue }}</span>
              </div>
            </div>

            <div v-show="item.marketCategoryId == 2 && item.standardMarketOddsBOList && item.standardMarketOddsBOList.length > 0">
              <div
                v-for="(i, index2) in item.standardMarketOddsBOList"
                :key="`standard_market_oddsBO_list_${index2}`"
                class="row"
              >
                <span
                  class="nameExpressionValue mr5x"
                  style="color: #E93D3D;width:30px;display: inline-block;"
                >
                  <span v-show="index2 == 0">{{ item.marketValue }}</span>
                </span>
                <span class="fieldOddsOriginValue">{{ i.oddsValue }}</span>
              </div>
            </div>

            <div v-show="item.marketCategoryId == 4 && item.standardMarketOddsBOList && item.standardMarketOddsBOList.length > 0">
              <div
                v-for="(i, index2) in item.standardMarketOddsBOList"
                :key="`standard_market_oddsBO_list_${index2}`"
                class="row"
              >
                <span class="nameExpressionValue mr5x" style="color: #E93D3D;width:30px;display: inline-block;">
                  <span v-show="index2 == 0">{{ item.marketValue }}</span>
                </span>
                <span class="fieldOddsOriginValue">{{ i.oddsValue }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </q-dialog>
    <!-- 点击球队名称 查看赔率弹窗  结束 -->
    <!-- 特殊关注组管理弹窗 开始  点击左侧查询组件特殊关注图标触发 -->
    <iModal
      v-model="show_modal"
      width="620"
      :footer-hide="true"
      :title="i18n_t('selectPeriodTab.Tab_15')"
      class="reset-modal use-ivew"
    >
      <iTransfer
        :data="left_keys"
        :target-keys="target_keys"
        :list-style="list_style"
        :titles="[i18n_t('selectPeriodTab.Tab_16'), i18n_t('selectPeriodTab.Tab_17')]"
        filterable
        @on-change="handle_change"
        class="live-itransfer"
      >
      </iTransfer>
    </iModal>
    <!-- 特殊关注组管理弹窗 结束 -->
  </div>
</template>
<style>
 @import url('project\caopan\pages\match\unsold_match\css\index.scss');
</style>
<style lang="scss" scoped>
  @import url('project\caopan\pages\match\unsold_match\css\index-scope.scss');
</style>
<script>
  import {mapGetters, mapActions} from "vuex";
  import {api_match} from "src/api/index.js";
  import mixins from "project/caopan/mixins/index.js";
  import matchmixin from "project/caopan/mixins/pages/match/match.js";
  import { tablecolumns_config } from "project/caopan/pages/match/unsold_match/config/config.js";
  import selectQueryPeriod from "project/caopan/components/query/select_query_period3.vue";
  import tableColWidth from "project/caopan/mixins/style/tableColWidth.js";
  import matchManageMixin from "project/caopan/mixins/pages/match/matchManage.js";
  import rightinfomixin from "project/caopan/mixins/layout/rightinfo.js";
  import tablepagemixin from "project/caopan/mixins/table/index.js";
  import cacheV2 from "project/caopan/mixins/custom/module/cacheV2.js";
  import {formatDate} from "project/caopan/util/index.js";
  import websocket_base from "project/caopan/mixins/websocket/websocket_base.js";
  import tableheader from "project/caopan/components/table/tableheader.vue";
  import tablerow from "project/caopan/pages/match/unsold_match/component/tablerow.vue";
  import {dom} from "quasar";
  import moreQuery from "project/caopan/components/query/match_left_query4.vue"//左侧搜索
  import selectSportType from "project/caopan/components/query/select_sport_type/select_sport_type1.vue"//球类筛选
  import pandaPagination from "project/caopan/components/pagination/paginationV2.vue"//分页
  import tableNoData from "project/caopan/components/table/tableNoData.vue"
  import tableCellEllipsis from "project/caopan/components/table/table_cell_ellipsis.vue"//提示框

  const {height, width} = dom;
  import {i18n} from "src/boot/common/i18n";
  import axios from "src/api/common/axioswarpper"

  import Plsy_id from "../common/plsy_id";
  import {format_date_base_time2} from "project/caopan/util/index";

  export default {
    mixins: [
      ...mixins,
      ...tablepagemixin,
      matchmixin,
      matchManageMixin,
      tableColWidth,
      cacheV2,
      websocket_base,
      rightinfomixin
    ],
    components: {
      moreQuery,
      selectSportType,
      selectQueryPeriod,
      tableheader,
      pandaPagination,
      tablerow,
      tableNoData,
      tableCellEllipsis,
    },
    data() {
      return {
        league_name: [],//联赛名称
        state: 1,
        // 特殊关注组管理开始
        show_modal: false,
        left_keys: [],
        target_keys: [],
        list_style: {
          width: "250px",
          height: "300px"
        },
        // 特殊关注组管理结束

        play_data: {
          startTimeFrom: 0,
          matchStatus: "Enable",
          live: 0,
          page: 0,
          size: 1000
        },
        pup_data: [],
        name_obj: {},
        Pup_dialog: false, //查看赔率弹窗开关
        earlyTrading: 0,
        matchStatus: "Enable",
        queryform_form: {
          pageSize: 1000,
          currentPage: 1
        },
        show_more_query: false,
        default_select: {value: 0},
        queryform_module: {
          dataSourceCode: [],
          id: 0,
          matchSellStatus: "",
          pageSize: 1000,
          currentPage: 1
        },
        scroll_area_height: "",
        show_right_detail: false,
        show_right_detail_obj: {},
        tablecolumns: tablecolumns_config,
        tabledata: [],
        query_time: 0, //20为自选赛事
        label_cache: "",
        // websocket_connection_1_url: WEB_ENV,
        /**左侧查询开始 */
        query_module: {
          matchSellStatus: "", //筛选条件
          // searchType: "", //筛选条件
          regionId: "", //联赛区域
          preTraderDepartmentId: "", //部门ID
          userId: "", //搜索用户名
          matchManageId: "" //标准赛事id
        },
        league_areaList: [],
        operation_group: [],
        match_sell_status_arr: [//赛事状态
          {label: i18n_t("common.all"), value: ""},
          {label: i18n_t("saleTable.sa_15"), value: "Unsold"},
          {label: i18n_t("saleTable.sa_37"), value: "Overdue_Unsold"},
          {label: i18n_t("saleTable.sa_38"), value: "Apply_Delay"},
          {label: i18n_t("saleTable.sa_14"), value: "Sold"},
          {label: i18n_t("saleTable.sa_39"), value: "Cancel_Sold"},
          {label: i18n_t("saleTable.sa_40"), value: "Apply_Cancel_Sold"},
          {label: i18n_t("saleTable.sa_41"), value: "Stop_Sold"}
        ],
        isFavorite: 0,//是否查询收藏赛事 0 不是 1 是
        timer: null,
        bunlodin:false,  //  刷新按钮
      };
    },
    computed: {
      ...mapGetters({
        serverTime: "get_serverTime",
        breadcrumbsPart3: "get_breadcrumbsPart3",
        sport_type_constant: "get_sportType_array",
        get_sportRegion_array: "get_sportRegion_array",
      }),
    },
    watch: {
      export const get_sportRegion_array = (payload,val) => {
        league_areaList.value = val || [];
      },
      select_sportId: {
        export const handler = (payload,arg) => {
          set_footer_fn();
          get_my_subordinate();
        },
        immediate: true
      },
    },
    created() {
      set_sportRegion_array_force();
      set_dataSource_obj();
      get_tab();
      // init_right_detail_status(2);
      tablecolumns.value = rebuild_tablecolumns_config(tablecolumns.value);
    },
    mounted() {
      timer.value = setInterval(() => {
        init_tabledata();
      }, 60000);
    },
    methods: {
      ...mapActions([
        "set_optional_events_num",
        "set_sportRegion_array_force",
        'set_dataSource_obj',// 设置数据源
      ]),
      export const query_tournament_info = (payload,obj) => {//联赛名称接口
        let params = {
          sportId: obj.sportId,//运动种类id
          startTimeFrom: obj.startTimeFrom,//开始时间
          endTimeFrom: obj.endTimeFrom,//结束时间
          sellType: obj.marketType,//盘口类型  PRE :早盘    LIVE :滾球
          isFavorite: obj.isFavorite,//是否查询收藏赛事 0 不是 1 是
          live: obj.live,//是否滚球赛事  0 不是滚球赛事    1 滚球赛事
          matchStatus: obj.matchStatus,//开售状态    如果是历史赛事  就是End  别的都是 Enable
        };
        api_match.queryTournamentInfo(params).then(({data}) => {
          league_name.value = Array.isArray(data.data) ? data.data : [];
        });
      },
      export const preMatchTime = (payload,val) => {
        return formatDate(val, "mm.dd") + "  " + formatDate(val, "hh:ii");
      },
      /* 数据来源开始 */
      export const get_dsc_options = (payload) => {
        if (dataSource_obj.value) {
          let res = [];
          dataSource_obj.value.data_sources_constant_not_pa.filter(item => item.id).forEach(x => {
            let item = {value: x.code, label: x.fullName};
            res.push(item);
          });
          return res;
        }
        return [];
      },
      // 刷新按钮
      export const which_refresh = (payload) => {
        bunlodin.value=true
        init_tabledata_force();
      },
      // 分页
      export const change = (payload,val) => {
        queryform_form.value.currentPage = val;
      },
      export const size_change = (payload,val) => {
        queryform_form.value.pageSize = val;
      },
      // 获取操盘部门所有人员列表
      export const get_special_group_person = (payload) => {
        api_match
          .getSpecialGroupPerson({sportId: select_sportId.value})
          .then(({data}) => {
            left_keys.value = data.data && data.data.length > 0 ? init_data3(data.data) : [];
            target_keys.value = init_data4();
          });
      },
      export const init_data3 = (payload,arr) => {
        arr.forEach(item => {
          item.label = item.userName + '  -  ' + item.orgName + '  -  ' + (item.positionName || '待定');
          item.key = item.userId;
        });
        return arr;
      },
      export const init_data4 = (payload) => {
        let targetA = [];
        left_keys.value.map(item => {
          item.isSpecial == 1 && targetA.push(item.userId);
        });
        return targetA;
      },
      // 获取我的部下组织树
      export const get_my_subordinate = (payload) => {
        api_match
          .getMySubordinate()
          .then(({data}) => {
            operation_group.value = data.data && data.data.length > 0 ? init_data2(data.data) : [];
            get_special_group_person();
          });
      },
      export const init_data2 = (payload,arr) => {
        arr.forEach(item => {
          item.label = item.name + '(' + item.users + ')';
          item.value = item.id;
          item.children &&
          item.children.length > 0 &&
          item.children.forEach(item2 => {
            item2.label = item2.name + '(' + item2.users + ')';
            item2.value = item2.id;
          });
          item.children.unshift({label: i18n_t("common.all"), value: item.id});//全部
        });
        arr.unshift({label: i18n_t("common.all"), value: -1});//全部
        return arr;
      },
      // 特殊关注组管理
      export const toggle_modal = (payload) => {
        show_modal.value = !show_modal.value;
      },
      export const handle_change = (payload,new_target_keys) => {
        target_keys.value = new_target_keys;
        api_match
          .saveSpecialGroupPerson({
            sportId: select_sportId.value,
            persons: new_target_keys.length > 0 ? new_target_keys.join(",") : ""
          })
          .then(({data}) => {
            if (data.code == 200) {
              show_msg(data.code,data.msg);
              operation_group.value[operation_group.value.length - 1].value =
                new_target_keys.length > 0 ? new_target_keys.join(",") : "";
              moreQuery4.value.cascader[0] =
                new_target_keys.length > 0 ? new_target_keys.join(",") : "";
            } else {
              show_msg(data.code,data.msg);
            }
          });
      },
      // 查看赔率
      export const p_ups = (payload,row) => {
        name_obj.value = {};
        name_obj.value = {
          leagueName: row.leagueName,
          homeTeamNames: row.homeTeamNames,
          awayTeamNames: row.awayTeamNames
        };
        let params = {
          marketType: "PRE",
          matchInfoId: row.matchId || row.referenceId
        };
        api_match.post_getPlayOdds(params).then(({data}) => {
          if (data.code == 200) {
            pup_data.value = (data.data && init_data(data.data)) || [];
            if (pup_data.value.length > 0) {
              init_data();
              Pup_dialog.value = true;
            } else {
              Pup_dialog.value = false;
              Message.success(i18n_t("saleTable.sa_224"));//暂无赔率
            }
          } else {
            Pup_dialog.value = false;
            Message.success(i18n_t("saleTable.sa_224"));//暂无赔率
          }
        });
      },
      export const init_data = (payload,arr) => {
        arr &&
        arr.forEach((item, index) => {
          let standardMarketOddsBOList = [];
          arr[index].standardMarketOddsBOList &&
          arr[index].standardMarketOddsBOList.map(item2 => {
            (item2.oddsType == 1 || item2.oddsType == "Over") &&
            (standardMarketOddsBOList[0] = item2);
            (item2.oddsType == 2 || item2.oddsType == "Under") &&
            (standardMarketOddsBOList[1] = item2);
            item2.oddsType == "X" && (standardMarketOddsBOList[2] = item2);
          });
          arr[index].standardMarketOddsBOList = standardMarketOddsBOList;
        });
        return arr;
      },
      export const submit_success = (payload,val) => {
        init_tabledata(val);
      },
      export const set_footer_fn = (payload,label) => {
        if (sport_type_constant.value) {
          let remark = sport_type_constant.value.find(n => {
            return n.id == select_sportId.value;
          });
          set_footer_breadcrumbs_part_all(
            [i18n_t("menus.lm_8.children4")],
            [remark.introduction],
            [],
            [label_cache.value || label]
          );
        }
      },
      export const get_tab = (payload) => {
        if (route.query.tab) {
          query_time.value = Number(route.query.tab);
        }
      },
      export const recompute_beginTim_show_label = (payload,val) => {
        let obj = lodash.find(
          tablecolumns.value,
          o => o.name == "beginTime"
        );
        let label = `${val.label}`;
        label_cache.value = label || "";
        isEarlyTrading.value = label == i18n_t("selectPeriodTab.Tab_6") ? 1 : 0;
        matchStatus.value =
          label == i18n_t("selectPeriodTab.Tab_11") ? "End" : "Enable";
        obj.label =
          val.value === 7 ? val.label : `${val.nav_label}(${val.week_str})`;
        set_footer_fn(label);
      },
      export const init_tabledata = (payload,val) => {
        init_tabledata_before();
        let params = compute_init_tabledata_params();
        params.size = 1000;
        tabledata.value = []
        api_match.post_standardMarketSell_list(params).then(res => {
          init_tabledata_after(res);
          let code = res.data.code;
          if (code == 200) {
            let arr = res.data.data && res.data.data.data && res.data.data.data.records ? res.data.data.data.records : [];
            tabledata.value = arr.length > 0 ? rebuild_sale_page_tabledata_to_need2(arr).filter(item => item.preRiskManagerCode && !item.liveRiskManagerCode) : [];
            deal_persell_table_item_default_selected(tabledata.value, val);
            total.value = res.data.data && res.data.data.data && res.data.data.data.total ? res.data.data.data.total * 1 : 0;
            bunlodin.value=false
          }
        });
      },
      export const collect_num = (payload,obj) => {// 切换收藏刷新数据
        if (isFavorite.value == 1) {
          init_tabledata()
        } else {
          if (obj.type == 1) {
            tabledata.value[obj.index].favoriteStatus = obj.favoriteStatus;
          } else {
            tabledata.value.forEach((item, index) => {
              if (item.tournamentId == obj.tournamentId) {
                item.favoriteStatus > 0 && (item.favoriteStatus = obj.favoriteStatus);
                item.tournamentFavoriteStatus = obj.favoriteStatus;
              }
            })
          }
          get_collect_num();
        }
      },
      export const get_collect_num = (payload) => {// 获取收藏个数
        let params = compute_init_tabledata_params();
        params.page = 1;
        params.isFavorite = 1;
        params.live = 0;
        let [y, m, d, h, mm, s] = format_date_base_time2(serverTime.value);
        let startTimeFrom;
        if (h < 12) {
          startTimeFrom = new Date(`${y}-${m}-${d - 1}` + ' 12:00:00').getTime()
        } else {
          startTimeFrom = new Date(`${y}-${m}-${d}` + ' 12:00:00').getTime()
        }
        params.startTimeFrom = startTimeFrom;
        params.size = 1000;
        api_match.post_standardMarketSell_list(params).then(({data}) => {
          if (data.code == 200) {
            set_optional_events_num(data.data.data.total || 0);
          }
        })
      },
      // 左侧查询
      export const handle_query = (payload,args) => {
        if (!args && typeof args !== "object") return;
        query_module.value = {...query_module.value, ...args};
        tosearch_queryform();
      },
      export const compute_init_tabledata_params = (payload,i) => {
        let params = {
          page: queryform_form.value.currentPage,
          size: queryform_form.value.pageSize,
          sportId: select_sportId.value,
          isErlyTrading: isEarlyTrading.value, //是否其它早盘
          matchStatus: matchStatus.value, //是否历史赛程
          marketType: "PRE", //PRE 早盘 LIVE 滚求
          isFavorite: isFavorite.value //是否查询收藏赛事 0 不是 1 是
        };
        if (i) {
          params.matchInfoId = i;
        }
        params = {...params, ...query_module.value};

        play_data.value = {
          startTimeFrom: params.startTimeFrom,
          matchStatus: matchStatus.value,
          page: queryform_form.value.currentPage,
          size: queryform_form.value.pageSize
        };
        query_tournament_info(params);
        return params;
      },
      export const query_period_change = (payload,val) => {
        if (val) {
          isFavorite.value = val.value == 20 ? 1 : 0;
          query_module.value.startTimeFrom = val.startTimeFrom;
          query_module.value.endTimeFrom = val.endTimeFrom;
          query_module.value.live = val.value == 21 ? 1 : 0; //开售列表接口增加入参是否滚球赛事字段：live（1：是  0：否 ）
          play_data.value.live = val.value == 21 ? 1 : 0; //开售列表接口增加入参是否滚球赛事字段：live（1：是  0：否 ）
          default_select.value = val;
          recompute_beginTim_show_label(val);
          tosearch_queryform();
        }
      },
      export const show_more_query_change = (payload,val) => {
        show_more_query.value = val;
        compute_scrollarea_style();
      },
      export const table_item_click = (payload,item) => {
        show_right_detail_obj.value = item;
        show_right_detail.value = true;
      },
      export const compute_scrollarea_style = (payload) => {
        nextTick(() => {
          let hs = window_size_info.value.height - 36 - 18 - 41;
          scroll_area_height.value = hs - 26;
        });
      },
    },
    beforeDestroy() {
      clearInterval(timer.value);
      timer.value = null;
    }
  };
</script>
