.text-panda-date-base{
  color: var(--q-color-panda-text-base);
}
.text-panda-date-light{
 color:#d5dee7 !important
}
.panda-border-1px{
  border: solid 1px var(--q-color-panda-secondary) ;
}

.bg-panda-query-date-active .panda-query-date-tab-label {
  background-color: var(--q-color-panda-primary);
  border:none
}


.bg-panda-query-date-active .panda-query-date-tab-label:hover{
    background:#00a99766 !important;
    color:#fff !important;
    border:0 none;
}

.text-panda-date-light.panda-query-date-tab-label:hover{
     color: var(--q-color-panda-primary) !important;
     border: solid 1px var(--q-color-panda-primary);
}
.panda_show_more {
  position: relative;
  display: inline-block;
}

.panda_show_more:before {
  position: absolute;
  bottom: -6px;
  right: 0;
  left: 0;
  margin: 0 auto;
  content: '';
  display: block;
  width: 0;
  height: 2px;
  background-color: var(--q-color-panda-primary);
  transition: all 0.3s;
}

.panda_show_more:hover:before {
  width: 100%;
}

.panda_show_more:hover {
  color: var(--q-color-panda-primary);
  [class^='panda_']:before {
    color: var(--q-color-panda-primary)
  }
}

.period__icon-badge {
  display: inline-block;
  min-width: 13px;
  height: 13px;
  background-color: #FF494A;
  color: #fff;
  border-radius: 50%;
  font-size: 13px;
  line-height: 13px;
  font-weight: normal;
  text-align: center;
  transform: translate(-5px, -5px);
}
