import { onMounted } from "vue";
import { drag } from "src/components/league_ranking/module/index.js";
import { Sure_textanda } from "src/components/league_ranking/api-request/Sure_textanda.js";
import { swith_change } from "src/components/league_ranking/api-request/swith_change.js";
import { top_morning } from "src/components/league_ranking/api-request/top_morning.js";
import { get_tournament_name_data } from "src/components/league_ranking/api-request/get_tournament_name_data.js";
import { process } from "src/components/league_ranking/module/other.js";
import {
  priority_change,
  modify_playrules_order,
  submit_modify_playrules_order,
  abort_modify_playrules_order,
  set_sort_beg,
  cross_page_sort,
  submit_set_table_sort,
  table_sort,
  cancel_btn,
  Cancel_textanda,
  all_cancel,
  all_show,
  tournament_name_clear,
  set_tournament_name,
  row_click,
  click_back,
  get_level,
  row_class_name,
} from "src/components/league_ranking/module/handle.js";
import {
  change_page_num,
  change_page_size,
} from "src/components/league_ranking/module/page.js";
import { watcher_register } from "src/components/league_ranking/watch/index.js";
import { src_componsable_util_watcher_register_componsable_fn } from "src/output/common/componsable-common.js";

export const src_components_league_ranking_componsable_fn = (raw_payload) => {
  const payload = { ...raw_payload };
  onMounted(() => {
    drag(payload);
  });
  // src_componsable_util_watcher_register_componsable_fn(
  //   watcher_register(payload)
  // );
  return {
    get_level: (n) => get_level(payload, n),
    click_back: (row) => click_back(payload, row),
    row_click: (item, index) => row_click(payload, item, index),
    set_tournament_name: (item) => set_tournament_name(payload, item),
    tournament_name_clear: () => tournament_name_clear(payload),
    all_show: () => all_show(payload),
    all_cancel: () => all_cancel(payload),
    cancel_btn: (row) => cancel_btn(payload, row),
    Cancel_textanda:()=> Cancel_textanda(payload),
    table_sort: (row, index, type) => table_sort(payload, row, index, type),
    submit_set_table_sort: (row, index) => submit_set_table_sort(payload, row, index),
    cross_page_sort: (row, index) => cross_page_sort(payload, row, index),
    set_sort_beg: (row, index) => set_sort_beg(payload, row, index),
    abort_modify_playrules_order: () => abort_modify_playrules_order(payload),
    submit_modify_playrules_order: () => submit_modify_playrules_order(payload),
    modify_playrules_order: () => modify_playrules_order(payload),
    priority_change: (status) => priority_change(payload, status),
    row_class_name: (row, index) => row_class_name(payload, row, index),
    process: () => process(payload),
    change_page_num: (v) => change_page_num(payload, v),
    change_page_size: (v) => change_page_size(payload, v),
    confirm_btn: (row) => confirm_btn(row),
    get_tournament_name_data: (value) => get_tournament_name_data(payload, value),
    Sure_textanda: () => Sure_textanda(payload),
    swith_change: (row, index, stant) => swith_change(payload, row, index, stant),
    top_morning: (row, val) => top_morning(row, val),
  };
};
