import { api_system_set } from "src/api/index.js";
import { Message, lodash,show_msg } from "src/output/common/project-common.js";
import {get_data } from "project/caopan/pages/basic_setting/data_setting/api-request/get_data.js"
const update_switch_ok=(payload,res) => {

  let {code,msg} = lodash.get(res,'data')
  show_msg(code,msg)
  get_data(payload)
}
    // 漏单总开关
    export const update_switch = async(payload) => {
      const {
        qjStatus,
    } = payload
      console.log(qjStatus.value)
      let param={
        switchCode:"LOUDAN", // 固定参数
        status:qjStatus.value
      }
     
      try {
        let res=await  api_system_set.get_editSwitch(param)
        update_switch_ok(payload,res)
      } catch (err) {
        console.log(err)
        Message.error(err)
      }
    
   
    }
