export const watcher_register = (payload) => {
  const {
    show_more_query,
    current_h,
    route,
    default_select,
    matchFixDate,
  } = payload;
  return [
    ///多个watch，透过array传出,可以直接使用payload上下文
    watch(
      () => show_more_query,
      () => {
        emit("show_more_query_change", show_more_query.value);
      }
    ),

    watch(
      () => current_h,
      (val) => {
        init_query_form_date_arr();
      }
    ),

    watch(
      () => route.name,
      (val) => {
        init_query_form_date_arr();
        if (val == "trader_manage_liveOddSupport") {
          query_form_date_tab_click(9);
        } else if (val == "trader_manage_morning_o") {
          query_form_date_tab_click(0);
        } else {
          query_form_date_tab_click(default_select.value);
        }
      },
      {
        deep: true,
        immediate: true,
      }
    ),

    watch(
      () => matchFixDate,
      (newValue) => {
        if (newValue) {
          query_form_date_tab_click(7, true);
        }
      }
    ),
  ];
};
