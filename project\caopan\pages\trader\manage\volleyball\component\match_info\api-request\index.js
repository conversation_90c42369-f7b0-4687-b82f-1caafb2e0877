import { api_trader } from "src/api/index.js";

//收藏请求
export const update_collect = (payload, data, Status) => {
  api_trader.updateCollect(data).then((res) => {
    let code = lodash.get(res, "data.code");
    if (code === 200) {
      Message.success(
        Status
          ? `${i18n_t("traderTable.text101")}${res.data.msg}`
          : i18n_t("traderTable.text102")
      );
      //   props.value.row.matchCollectStatus = Status;
      emit("update_collect", Status, data, 3); //删除收藏的联赛
    } else {
      Message.error(`${res.data.data}`);
    }
  });
};
