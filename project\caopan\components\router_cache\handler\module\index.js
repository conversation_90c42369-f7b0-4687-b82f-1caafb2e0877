import { lodash } from "src/output/common/project-common.js";
import {
  set_router_cache,
} from "src/output/common/store-common.js";

/**
 * @description: 根据路径设置路由缓存
 * @param {String}} path
 * @param {any} value
 */
export const set_router_cache_by_path_and_value = (payload, path, value) => {
  const { route } = payload;
  set_router_cache({
    value: value,
    path: path ? `${route.name}.${path}` : `${route.name}`,
  });
};

/**
 * @description: 根据路径获取路由值
 * @param {String} path
 */
export const get_router_cache_value_by_path = (payload, path) => {
  const { router_cache, route, select_sportId } = payload;
  let return_val = lodash.get(
    router_cache.value,
    path ? `${route.name}.${path}` : `${route.name}`
  );
  if(return_val){
    return return_val;
  }
    return select_sportId
};

export const when_router_cache_change = (payload) => {};
