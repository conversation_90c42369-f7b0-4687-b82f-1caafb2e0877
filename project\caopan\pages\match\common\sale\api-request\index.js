import { format_date_base_time2 } from "app/project/saicheng/util";
import { api_match, api_sc_match } from "src/api/index.js";
import { delete_empty_property_with_exclude, lodash, Message, show_msg } from "src/output/common/project-common";
import { init_data2, init_data3, init_data4 } from "project/caopan/pages/match/common/sale/module/init.js";
import { set_optional_events_num } from "src/store-202412/page/optional_events.js";
import { init_data } from "project/caopan/pages/match/sale/module/index.js";

// 获取收藏个数
export const get_collect_num = payload => {
  const { select_sportId, pre_or_live, serverTime } = payload;

  let params = {
    page: 1,
    size: 10,
    sportId: select_sportId.value,
    isErlyTrading: 0,
    matchStatus: "Enable",
    marketType: pre_or_live.value, // PRE 早盘   LIVE 滚球,
    isFavorite: 1,
    matchSellStatus: "",
    regionId: "",
    preTraderDepartmentId: "",
    userId: "",
    matchManageId: "",
    live: 0,
    teamName: "",
    isSpecialPersons: 0,
    orgIdOrPersons: "",
    dataSourceCodeList: null,
    tournamentLevel: "",
    tournamentIdList: [],
    searchType: "",
  };
  let [y, m, d, h, mm, s] = format_date_base_time2(serverTime.value || new Date().getTime());
  let startTimeFrom;
  if (h < 12) {
    startTimeFrom = new Date(`${y}-${m}-${d - 1}` + " 12:00:00").getTime();
  } else {
    startTimeFrom = new Date(`${y}-${m}-${d}` + " 12:00:00").getTime();
  }
  params.startTimeFrom = startTimeFrom;
  startTimeFrom &&
    api_match.post_standardMarketSell_list(params).then(({ data }) => {
      if (data.code == 200) {
        set_optional_events_num(data.data.data.total || 0);
      }
    });
};

// 导出数据
export const export_data = payload => {
  const { route } = payload;
  const { compute_init_tabledata_params } = payload;

  let params = {};
  let url = "";
  switch (route.name) {
    // 预售历史数据
    case "pre_sale_set":
      params = delete_empty_property_with_exclude(compute_init_tabledata_params(payload));
      url = "/preSale/exportGet";
      break;
    //导出早盘滚球历史数据
    case "match_live":
    case "match_sale":
      params = compute_init_tabledata_params(payload);
      url = "/preLiveMatchSell/exportGet";
      break;
    default:
      params = null;
      break;
  }
  if (params && url) {
    api_sc_match.post_excel_export(params, url);
  }
};

// 获取左侧 联赛名称 联赛区域  赛事级别
export const query_tournament_info = (payload, obj) => {
  const { league_areaList, match_levels, league_name } = payload;

  let params = {
    sportId: obj.sportId, //运动种类id
    startTimeFrom: obj.startTimeFrom, //开始时间
    endTimeFrom: obj.endTimeFrom, //结束时间
    sellType: obj.marketType, //盘口类型  PRE :早盘    LIVE :滾球
    isFavorite: obj.isFavorite, //是否查询收藏赛事 0 不是 1 是
    live: obj.live, //是否滚球赛事  0 不是滚球赛事    1 滚球赛事
    matchStatus: obj.matchStatus, //开售状态    如果是历史赛事  就是End  别的都是 Enable
  };
  // 联赛名称
  api_match.queryTournamentInfo(params).then(({ data }) => {
    league_name.value = Array.isArray(data.data) ? data.data : [];
  });
  // 联赛区域  赛事级别
  api_match.post_standardMarketSell_match_getSellTournamentRegionList(params).then(res => {
    let code = res.data.code;
    if (code == 200) {
      league_areaList.value = res.data.data.regionList || [];
      match_levels.value = res.data.data.tournamentLevels || [];
      let all = {
        id: "",
        introduction: "全部",
        introductionEn: "All",
      };
      league_areaList.value.unshift(all);
    }
  });
};

//点击主客队时赔率弹窗
export const p_ups = (payload, row) => {
  const { name_obj, pup_data, Pup_dialog, pre_or_live } = payload;
  console.log('p_ups 方法被调用，row:', row);
  console.log('Pup_dialog 当前值:', Pup_dialog.value);
  console.log('Pup_dialog 对象:', Pup_dialog);
  console.log('payload 中的所有键:', Object.keys(payload));

  name_obj.value = {};
  name_obj.value = {
    leagueName: row.leagueName,
    homeTeamNames: row.homeTeamNames,
    awayTeamNames: row.awayTeamNames,
  };
  let params = {
    marketType: pre_or_live.value, // PRE 早盘   LIVE 滚球
    matchInfoId: row.matchId || row.referenceId,
  };

  console.log('API 请求参数:', params);

  api_match.post_getPlayOdds(params).then(({ data }) => {
    console.log('API 响应数据:', data);

    if (data.code == 200) {
      pup_data.value = (data.data && init_data(data.data)) || [];
      console.log('处理后的 pup_data:', pup_data.value);

      if (pup_data.value.length > 0) {
        init_data();
        console.log('设置 Pup_dialog 为 true');
        Pup_dialog.value = true;

        // 添加调试：检查设置后的值
        console.log('设置后 Pup_dialog 值:', Pup_dialog.value);

        // 添加延迟检查，确保 Vue 响应式更新
        setTimeout(() => {
          console.log('延迟检查 Pup_dialog 值:', Pup_dialog.value);
        }, 100);

      } else {
        console.log('pup_data 为空，但强制设置 Pup_dialog 为 true 进行测试');
        // 临时测试：即使没有数据也显示弹窗
        Pup_dialog.value = true;
        // Message.success(i18n_t("saleTable.sa_224")); //暂无赔率
      }
    } else {
      console.log('API 返回错误，code:', data.code);
      Pup_dialog.value = false;
      Message.success(i18n_t("saleTable.sa_224")); //暂无赔率
    }
  }).catch(error => {
    console.error('API 请求失败:', error);
    Pup_dialog.value = false;
  });
};

// 获取特殊关注人员列表
export const handle_change = (payload, keys) => {
  const { target_keys, operation_group, moreQuery4 } = payload;

  target_keys.value = keys;
  let persons_keys = keys.length > 0 ? keys.join(",") : "";
  api_match
    .saveSpecialGroupPerson({
      sportId: select_sportId.value,
      persons: persons_keys,
    })
    .then(({ data }) => {
      if (data.code == 200) {
        Message.success(`${i18n_t("mixins.hint_3")}`);
        operation_group.value[operation_group.value.length - 1].value = persons_keys;
        moreQuery4.value.cascader[0] = persons_keys;
      } else {
        Message.error(data.msg);
      }
    });
};

// 重新开赛 确定按钮
export const rest_match = payload => {
  const { reopen_data, state, state_list, show_r } = payload;
  const { init_tabledata } = payload;

  reopen_data.value.matchStatus = state.value == 2 ? null : state.value;
  reopen_data.value.matchStatusName = state_list.value.filter(item => item.val == state.value)[0].txt;
  reopen_data.value.matchStatusSourceCode = reopen_data_source.value;
  api_match
    .reopenMatch(reopen_data.value)
    .then(res => {
      let { code, msg } = lodash.get(res, "data");
      if (code == 200) {
        show_msg(code, msg);
        // [todo]
        init_tabledata();
        show_r.value = false;
      } else {
        show_msg(code, msg);
      }
    })
    .catch(e => {
      Message.error(e);
    });
};

// 获取操盘部门所有人员列表
export const get_special_group_person = payload => {
  const { select_sportId, left_keys, target_keys } = payload;

  api_match.getSpecialGroupPerson({ sportId: select_sportId.value }).then(({ data }) => {
    left_keys.value = data.data && data.data.length > 0 ? init_data3(data.data) : [];
    target_keys.value = init_data4(payload);
  });
};

// 获取我的部下组织树
export const get_my_subordinate = payload => {
  const { operation_group } = payload;

  api_match.getMySubordinate().then(({ data }) => {
    operation_group.value = data.data && data.data.length > 0 ? init_data2(data.data) : [];
    get_special_group_person(payload);
  });
};
