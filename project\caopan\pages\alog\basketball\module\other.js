export const timer_uptate = (payload) => {
  get_init_list();
  // 修改成rev
  // get_reverse()
}
export const timer_uptate1 = (payload) => {
  // get_init_list()
  // 修改成rev
  get_reverse();

}


  // 0.2 0.3二次确认弹出框
  export const second_confirmation = (payload,text) => {
    Message.destroy();
    Message.info({
      duration: 0,
      closable: true,
      render: (h) => {
        return h(
          "div",
          {
            class: ["row"],
          },
          [
            h("div", {}, [text]),
            h(
              "iButton",
              {
                class: ["q-ml-sm"],
                props: {
                  dense: "dense",
                  unelevated: "unelevated",
                  noCaps: true,
                },
                on: {
                  click: () => {
                    get_data_list("apply", "confirm");
                    Message.destroy();
                  },
                },
              },
              i18n_t("common.confirm")
            ),
          ]
        );
      },
    });
  }

  export const is_error = (payload,value) => {


    if (!value) {
      return false;
    }
    if (isNaN(value)) {
      return true;
    }
    return false;
  }


  export const get_bg_color = (payload,item, item1) => {



    if (item1 > 5) {
      return "";
    }
    return `bg-${item}`;
  }


  export const returnFloat = (payload,num) => {



    if(num < 10){
     return '0'+num
    }else{
      return num
    }
  }
