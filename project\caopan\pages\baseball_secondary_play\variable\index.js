import { ref } from "vue";
import { lodash } from "src/output/common/project-common.js";
import { matchVolumeList } from "project/caopan/pages/trader/manage/config/config.js";

export const create_base_state_instance_fn = payload => {
  // 比赛进行时间
  const secondsMatchStart = ref("");
  //赛事时间倒计时
  const MatchStart = ref(lodash.cloneDeep(secondsMatchStart.value));
  //定时器变量
  const settime = ref(null);
  //倒计时触发器开关
  const flag_ = ref(false);
  const params = ref({
    matchId: "2233034",
    // 1 滚球 0 早盘
    liveOddBusiness: "0",
    categorySetIds: [],
  });
  // x_row_playId: x_row_playId
  // const  // x_row_playId = ref(// x_row_playId:) ;/
  const dataList = ref([]);
  const playSetList = ref([]);
  // '纯投注额' : 1   '纯赔付额': 2    '混合型': 3
  // const  // '纯投注额' = ref(// '纯投注额' :) ;
  const matchVolumeId = ref(1);
  // 展开状态
  const value2 = ref([]);
  const teamList = ref([]);
  const matchSnapshot = ref(null);
  // 比赛阶段
  const period = ref(0);
  // 赛事状态
  const matchStatus = ref(null);
  // 操盘赛事状态 0开 2关 1封 11锁
  const operate_match_status = ref(0);
  const eventCode = ref("");
  const beginTime = ref("");
  const data_loading = ref(false);
  const dataSourceCode = ref("");
  const dataSourceCode_playId = ref("");
  const dataSource_model = ref(false);
  // 初次请求
  const postIndex = ref(0);
  // M+A操盘模式
  const tradeType = ref(null);
  // 基准分
  const match_score = ref("");
  const open_show = ref(false);
  const open_show_params = ref({});
  const playSetScore = ref("");
  const checkedPlaysetId = ref([]);
  // MTS状态
  const riskManagerCode = ref("");
  // 局数
  const setNum = ref(0);
  // 总局数
  const roundType = ref(0);
  const scoreList = ref([]);
  // 发球方
  const servesFirst = ref("");
  // const time = ref(item.value);

  return {
    MatchStart,
    settime,
    flag_,
    params,
    dataList,
    playSetList,
    matchVolumeId,
    value2,
    teamList,
    matchSnapshot,
    secondsMatchStart,
    period,
    matchStatus,
    operate_match_status,
    eventCode,
    beginTime,
    data_loading,
    dataSourceCode,
    dataSourceCode_playId,
    dataSource_model,
    postIndex,
    tradeType,
    match_score,
    open_show,
    open_show_params,
    playSetScore,
    checkedPlaysetId,
    riskManagerCode,
    matchVolumeList,
    setNum,
    roundType,
    scoreList,
    servesFirst,
    period,
  };
};
