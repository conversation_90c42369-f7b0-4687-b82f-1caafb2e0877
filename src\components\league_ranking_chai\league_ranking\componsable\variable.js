import { src_componsable_global_variable_componsable_fn } from "src/output/common/componsable-common.js";
import { create_base_state_instance_fn } from "src/components/league_ranking/variable/index.js";
import { computed_generator } from "src/components/league_ranking/computed/index.js";
export const src_components_league_ranking_variable_componsable_fn = () => {
  const base_state = create_base_state_instance_fn();
  const global_obj = src_componsable_global_variable_componsable_fn();
  const all_computed = computed_generator();
  // 基础上下文组装
  const payload = {
    ...base_state,
    ...global_obj,
    ...all_computed,
  };
  return payload;
};
