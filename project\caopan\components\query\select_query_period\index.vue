<template>
    <div :class="`row line-height-32px items-center full-width  ${has_border ? 'border-bottom-light' : ''
        }`
        " style="height:34px;">
        <div v-for="(items, indexs) in query_form_date_arr" :key="`query_form_date_${indexs}`"
            @click="query_form_date_tab_click(items.value)"
            :class="selected_date_tab == items.value ? 'bg-panda-query-date-active' : ''"
            class="float-left text-panda-text-dark text-center cursor-pointer">
            <span class="panda-py-4px panda-px-5px border-radius-2px panda-query-date-tab-label mr-10 panda-border-grey"
                :class="selected_date_tab == items.value
                        ? 'text-panda-date-base'
                        : 'text-panda-date-light'
                    ">{{ items.label }}</span>
        </div>
    </div>
</template>
<script setup>


const props = defineProps({
    right_bnt: "",
    default_select: {
      type: Number,
      default: 0
    },
    has_border: {
      type: Boolean,
      default: true
    },
    more_query: {
      type: Boolean,
      default: false
    },
    icon_count: {
      type: Number,
      default: 0
    },
    show_live_odd: {
      type: Boolean,
      default: false
    },
    show_yesterday: {
      type: Boolean,
      default: false
    },
});


///引入 variable—composable-fn
import { project_caopan_components_query_select_query_period_componsable_fn } from 'project/caopan/components/query/select_query_period/componsable/index.js'
//
const global_obj = project_caopan_components_query_select_query_period_componsable_fn({props});

const { query_form_date_arr, query_form_date_tab_click } = global_obj

</script>
<style lang="scss" scoped>
@import url('./css/index.scss');

</style>