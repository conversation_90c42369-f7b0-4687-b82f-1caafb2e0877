import { api_login, api_trader, api_order} from "src/api/index.js";
import { lodash} from "src/output/common/project-common.js"
import router from 'project/caopan/router/index.js'
 /**
 * @description: 未结算赛事 红点统计接口调用
 * @param {*}
 * @return {*}
 */
 export  const getUnsettleMatchSize = (payload)=>{
  const {event_to_be_settled_total} = payload
  api_order.getUnsettleMatchSize({sportId: -1}).then(res => {
    let { code, data, msg } = lodash.get(res, "data");
    if(code == 200) {
      data.forEach(item => {
        if(item.sportId == '-1') event_to_be_settled_total.value = item.unsettleMatchSize || 0
      })

    }else {
      console.error(msg)
      // Message.error(msg)
    }
  }).catch(err => {
    console.error(err)
    // Message.error(`${err}`)
  })
}

export  const login_out = (payload)=>{
  api_login.getLogout({ appId: 10006 }).then(res => {
    let code = lodash.get(res, "data.code");
    console.log(569, code);
    if (code === "00000000") {
      // 清除注单中心缓存条件等
      // localStorage.clear();
      localStorage.removeItem("wsb_data_5");
      localStorage.removeItem("wsb_data_2");
      localStorage.removeItem("wsb_data_1");
      localStorage.removeItem("ismesage");
      localStorage.removeItem("Skin");
      localStorage.removeItem("timemodel");
      localStorage.removeItem("pandeTime");
      sessionStorage.clear();
      console.log('r----', router)
      router.push({
        name: "login",
        // params: {
        //   out: true
        // }
      });
      setTimeout(()=> {
        window.location.reload();
      })
    }
  });
}

/**
 * @description: betcancel 红点统计接口调用
 * @param {*}
 * @return {*}
 */
export  const get_bet_cancel_total = (payload)=>{
  const {betcancel_total} = payload
  api_order.get_queryIsHandle().then(res => {
    let sus = lodash.get(res, "data.code");
    let data = lodash.get(res, "data.data");
    let msg = lodash.get(res, "data.msg");
    if(sus == 200) {
      // betcancel赋值处理
      betcancel_total.value = data.total
    }else {
      console.error(msg)
      // Message.error(msg)
    }
  }).catch(err => {
    console.error(err)
    // Message.error(`${err}`)
  })
}

//阅读提示消息
export  const marquee_click = (payload,item)=>{

  // if(item.id) {
  api_trader.post_readMessage({"messageId":item.id}).then(res=>{
  let code = lodash.get(res, "data.code");
  if(code==200){
    let announ_cement = lodash.cloneDeep(get_announ_cement.value);
    get_announ_cement.value.forEach((iet,index)=>{
      if(iet.id == item.id){
        Message.success(i18n_t('menu_settings.t49'));//'已阅读'
        announ_cement.splice(index,1)
      }
    })
    set_announ_cement(announ_cement)
  }
})
}
