
import {change_refreshStatus1} from 'project/caopan/pages/alog/basketball/config/common/module/index';
import {categorySetIds} from 'project/caopan/pages/alog/basketball/config/config.js';
import {get_data_list} from 'project/caopan/pages/alog/basketball/api-request/get_data_list.js';
import {lodash} from 'src/output/common/project-common.js';

export const get_mode = (payload) => {
  const { mode, modeConfig } = payload;

  if (mode.value) {
    let obj = modeConfig.find((item) => item.mode == mode.value) || {};
    return obj.value;
  }
};
export const handle_select_mode = (payload, value) => {
  const { select_mode } = payload;

  console.log(value, "value");
  // mode.value = value;
  select_mode.value = false;
};
export const set_applyNumTimer = (payload) => {
  const { applyNumTimer, applyNum } = payload;

  applyNumTimer.value = setInterval(() => {
    applyNum.value = applyNum.value - 1;
    if (applyNum.value < 0) {
      applyNum.value = 0;
    }
  }, 1000);
};
export const websockt_fn = (payload) => {
  const { wsParams, is_action_ws } = payload;

  websocket_send(wsParams.value, 1);
  is_action_ws.value = true;
};

// 初始化绑定参数
export const clear_data = (payload) => {
  const {
    old_ao_RevParam,
    dataList,
    text_timer_num,
    text_period,
    input_data,
    reverse,
    match_timer_status,
  } = payload;

  old_ao_RevParam.value = {};
  dataList.value = [];
  text_timer_num.value = "";
  text_period.value = "";
  input_data.value = {};
  reverse.value = {};
  match_timer_status.value = {
    // 0 为比分中心，1为手动比分
    isManualSocre: 0,
    // 结算时间分钟
    periodTimer: "0",
    // 阶段 0 早 1滚
    period: 0,
    checked_value: 0,
  };
};

// 获取玩法数据
export const get_play_obj = (payload, playId, index) => {
  const { dataList } = payload;

  if (!dataList.value[index]) {
    return {};
  }
  let obj =
    dataList.value[index].marketSetVoList.find((item) => {
      if (item) {
        return item.marketSetId == playId;
      } else {
        return false;
      }
    }) || {};
  return obj;
};

// type1 Apply Reverse
// type2 123 参数123
export const get_log_value = (payload, obj, type1, type2) => {
  const { log_log_object } = payload;

  let arr = log_log_object.value[type1][type2] || [];
  let str = arr.map((item) => {
    return obj[item];
  });
  str = str.join("/");
  return str;
};
export const pause = (payload) => {
  const { pause_status } = payload;

  pause_status.value = !pause_status.value;
  if (!pause_status.value) {
    get_data_list(payload,"calc");
  }
};

// type 1 阶段 2 时间
export const get_text_timer = (payload, type) => {
  const { stageObj } = payload;

  if (stageObj.value.periodId) {
    if (type == 1) {
      let matchPeriodId = [31, 100]; // 后面需要带 _ 的赛事阶段
      let period = stageObj.value.periodId;
      if (matchPeriodId.includes(period)) {
        period = `${period}_`;
      }
      let text = i18n_t(`matchPeriodId.matchPeriodId2_${period}`); //获取赛事阶段
      return text;
    } else {
      return stageObj.value.periodRemainSec >= 0
        ? parseInt(stageObj.value.periodRemainSec / 60) +
            ":" +
            returnFloat(stageObj.value.periodRemainSec % 60)
        : "";
    }
  }
};
export const get_categorySetId = (payload) => {
  const { categorySetId } = payload;
  return categorySetIds[categorySetId.value].code;
};
export const get_show_type_red = (payload) => {
  const { categorySetId } = payload;

  return categorySetId.value != "10018_2";
};
export const change_input_data = (payload, n) => {
  const {
    input_data,

    applyReverseKeys1,
    is_change,
  } = payload;

  let obj1 = lodash.find(n, (o) => o.title == "FT") || {};
  let obj2 = lodash.find(n, (o) => o.title == "HT") || {};
  let obj3 = lodash.find(n, (o) => o.title == "Q1") || {};
  let obj4 = lodash.find(n, (o) => o.title == "Q3") || {};
  let values = obj1.values;
  let values2 = obj2.values;
  let values3 = obj3.values;
  let values4 = obj4.values;
  let obj = {};
  applyReverseKeys1.value.forEach((item, index) => {
    item.forEach((item1, index1) => {
      if (index == 0) {
        obj[item1] = values[index1];
      }
      if (index == 1) {
        obj[item1] = values2[index1];
      }
      if (index == 3) {
        obj[item1] = values3[index1];
      }
      if (index == 5) {
        obj[item1] = values4[index1];
      }
    });
  });
  console.log(11668, obj);
  Object.assign(input_data.value, obj);
  // Object.assign(old_ao_RevParam.value, obj);
  is_change.value = true; //说明用户修改了apply模块
};
export const change_select_data = (payload, n, type) => {
  const {
    input_data,
    reverse,
    is_change,
  } = payload;

  let obj1 = lodash.find(n, (o) => o.title == "HT") || {};
  let obj2 = lodash.find(n, (o) => o.title == "Q1") || {};
  let obj3 = lodash.find(n, (o) => o.title == "Q3") || {};
  let objs = {};
  objs.ht1IsSelected = obj1.using || "0";
  objs.q1IsSelected = obj2.using || "0";
  objs.q3IsSelected = obj3.using || "0";
  // return
  if (type == "reverse") {
    Object.assign(reverse.value, objs);
  } else {
    Object.assign(input_data.value, objs);
  }
  is_change.value = true; //说明用户修改了apply模块
};
export const copy_reverse = (payload) => {
  const { input_data, reverse, is_change, followedStatus, autoApply } = payload;

  followedStatus.value = 1;
  is_change.value = false; //只要copy就会重置这个
  input_data.value = { ...input_data.value, ...reverse.value };
  if (!autoApply.value) {
    get_data_list(payload,"calc");
  }
};
export const to_autoApply = (payload) => {
  const { matchType } = payload;

  if (matchType.value != "1") {
    get_data_list(payload,"apply");
  }
};
export const change_autoRev = (payload, e) => {
  const { refreshTimer1, refreshTime1, configTime1,refreshStatus1 } = payload;
  const { matchType } = payload.route.query;
  if (matchType == "1") {
    // 滚球：页面初始化的时候，如果autoRev是勾选的话，立马开始倒计时，并且勾选autoRev的时候不自动调Apply
    if (e) {
      console.log("开始倒计时");
      change_refreshStatus1(e);
    } else {
      clearInterval(refreshTimer1.value);
      console.log("取消倒计时");
      refreshTime1.value = configTime1.value;
    }
  } else {
    // 早盘：没有倒计时，如果autoRev是勾选的话，不做任何动作，并且勾选autoRev的时候自动调Apply
    to_autoApply();
  }
};
export const change_matchType = (payload) => {
  const {
    dataList,

    matchType,
  } = payload;

  // 清空赔率
  dataList.value = [];
  matchType.value = "1";
};
export const handle_hide = (payload) => {
  const { showMore } = payload;
  showMore.value = !showMore.value;
};
export const ws_40001_watcher_callback = (payload, n) => {

  const {
    pause_status,
    dataList,
    input_data,
    old_ao_RevParam,
    stageObj,

  } = payload
  let { data } = n;
  let { marketCategorySetVos = [], caclMarketParam = "" } = data;
  if (!pause_status.value) {
    // 篮球AOws替换逻辑根据marketSetId进行替换
    if (marketCategorySetVos.length) {
      dataList.value.forEach((item) => {
        marketCategorySetVos.forEach((market) => {
          if (item.setName == market.setName) {
            item.marketSetVoList.forEach((item1) => {
              market.marketSetVoList.forEach((market1) => {
                if (item1.marketSetId == market1.marketSetId) {
                  set(item1, "marketsEntityList", market1.marketsEntityList);
                }
              });
            });
          }
        });
      });

      let parsedCaclMarketParam = JSON.parse(caclMarketParam);
      input_data.value = Object.assign(input_data.value, parsedCaclMarketParam);
      old_ao_RevParam.value = Object.assign({}, input_data.value);
    } else {
      dataList.value = marketCategorySetVos;
    }
    stageObj.value.periodId = data.period;
    stageObj.value.periodRemainSec = data.marketTime;
  }
};
export const  hide_log = (payload)=>{
const {log_status} = payload
log_status.value = false;
}
