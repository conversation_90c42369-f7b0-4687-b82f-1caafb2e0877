<!--
 * @FilePath: /project/caopan/components/query/select_query_period6.vue
 * @Description:
-->
<template>
  <div
    :class="`row line-height-32px items-center full-width  ${
      has_border ? 'border-bottom-light' : ''
    }`"
    style="height: 34px"
  >
    <div
      v-for="(items, indexs) in query_form_date_arr"
      :key="`query_form_date_${indexs}`"
      @click="query_form_date_tab_click(items.value)"
      :class="
        selected_date_tab == items.value &&
        selected_date_tab != 7 &&
        !matchFixDate
          ? 'bg-panda-query-date-active'
          : ''
      "
      class="float-left text-panda-text-dark text-center cursor-pointer"
    >
      <span
        class="panda-py-4px panda-px-5px border-radius-2px panda-query-date-tab-label mr-10 panda-border-grey"
        :class="
          selected_date_tab == items.value
            ? 'text-panda-date-base'
            : 'text-panda-date-light'
        "
        >{{ items.label }}</span
      >
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  right_bnt: "",
  default_select: {
    type: Number,
    default: 0,
  },
  has_border: {
    type: Boolean,
    default: true,
  },
  more_query: {
    type: Boolean,
    default: false,
  },
  icon_count: {
    type: Number,
    default: 0,
  },
  show_live_odd: {
    type: Boolean,
    default: false,
  },
  show_yesterday: {
    type: Boolean,
    default: false,
  },
  matchFixDate: "",
});

///引入 variable—composable-fn
import { project_caopan_components_query_select_query_period6_select_query_period6_variable_composable_fn } from "project/caopan/components/query/select_query_period6/componsable/variable.js";

//最基础的payload
const base_payload =
  project_caopan_components_query_select_query_period6_select_query_period6_variable_composable_fn();

///解构出 参数，符合 template 所需要的
const {} = base_payload;

import { project_caopan_components_query_select_query_period6_select_query_period6_composable_fn } from "project/caopan/components/query/select_query_period6/componsable/index.js";

const {
  ///输出 template中@事件需要使用到的方法
} =
  project_caopan_components_query_select_query_period6_select_query_period6_composable_fn(
    {
      ...base_payload,
    }
  );

import { mapActions, mapGetters } from "vuex";
import alertdialogmixin from "project/caopan/mixins/module/alertdialogmixin.js";
import formartmixin from "project/caopan/mixins/module/formartmixin.js";
import rightinfomixin from "project/caopan/mixins/layout/rightinfo.js";
// import showrightdetail_getter from "project/caopan/mixins/layout/showrightdetail_getter.js";
import routerCache_handler from "project/caopan/mixins/routerCache/routerCache_handler.js";
export default {
  mixins: [
    alertdialogmixin,
    rightinfomixin,
    formartmixin,
    routerCache_handler,
    // showrightdetail_getter
  ],
  data() {
    return {};
  },
  computed: {},
  created() {
    // 控制更多查询初始值
  },
  props: {},
  mounted() {},
  watch: {},
  beforeDestroy() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
@import url("project/caopan/components/query/select_query_period6/css/index-scoped.scss");
</style>
