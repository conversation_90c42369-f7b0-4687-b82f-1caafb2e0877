<!--
 * @FilePath: /project/caopan/components/table/log_popup.vue
 * @Description: 日志弹框/open页面
-->
<template>
  <div
    style="height: auto; overflow: hidden"
    class="dragDom"
    :style="{ width: open ? '100%' : width, maxWidth: open ? '100%' : width }"
  >
    <div class="use-ivew">
      <div
        class="ivu-modal-header drag row justify-between"
        style="width: 100%"
        v-if="open != 3"
      >
        <span>{{
          table_data.length >= 1 ? table_data[0].categoryName : ""
        }}</span>
        <q-btn v-if="!open" dense flat icon="close" v-close-popup></q-btn>
      </div>
      <div
        class="test-panda-sidebar-head bg-panda-base-light2 border-bottom-light padding-section row justify-between items-center"
        v-if="search_state"
      >
        <!-- 搜索操作页面 -->
        <div class="row" :key="update_key">
          <!-- 赛种 / 赛事模板不展示-->
          <title-select
            :title="''"
            :list="sport_list"
            :keys="'sportId'"
            clearable
            :placeholder="i18n_t('common.sportType')"
            :styleObj="{ width: '80px' }"
            @emit_change_select="change_select"
            :className="''"
            v-if="url_data.name != 'sets'"
          />
          <!-- 赛事ID / 赛事模板不展示 -->
          <i-input
            class="ml10x"
            :keys="'matchManageId'"
            :placeholder="i18n_t('report_common.search29')"
            v-model="search_params.matchManageId"
            clearable
            outlined
            style="width: 100px"
            v-if="url_data.name != 'sets'"
          ></i-input>
          <!-- 操作页面 -->
          <title-select
            :title="''"
            :list="config.operatePageList"
            :keys="'operatePageCode'"
            :filterable="true"
            clearable
            :placeholder="i18n_t('log.table1')"
            :styleObj="{ width: '160px' }"
            :defaultData="search_params.operatePageCode"
            @emit_change_select="change_select"
            :className="'ml10x'"
          />
          <!-- 操作对象ID -->
          <iSelect
            v-if="objectNameListShow"
            class="bg-panda-field-grey regionId ml20x"
            size="small"
            style="width: 130px"
            :placeholder="i18n_t('log.table2')"
            v-model="search_params.objectId"
          >
            <iOption
              v-for="item in config.operateTypeList"
              :value="item.code"
              :key="item.index"
            >
              {{ item.value }}
            </iOption>
          </iSelect>
          <!--乒乓球（8）/棒球（3）/斯洛克（7）日志没有记录操作对象ID-->
          <i-input
            v-if="
              !objectNameListShow &&
              ![3, 7, 8, 4].includes(Number(url_data.query.sportId))
            "
            class="ml10x"
            :placeholder="i18n_t('log.table2')"
            v-model="search_params.objectId"
            clearable
            outlined
            style="width: 130px"
          ></i-input>
          <!-- 操作对象名称 -->
          <iSelect
            v-if="objectNameListShow"
            class="bg-panda-field-grey regionId ml20x"
            size="small"
            style="width: 120px"
            :placeholder="i18n_t('log.table3')"
            v-model="search_params.objectName"
          >
            <iOption
              v-for="item in config.operateTypeList"
              :value="item.code"
              :key="item.index"
            >
              {{ item.value }}
            </iOption>
          </iSelect>
          <i-input
            v-else
            class="ml10x"
            :placeholder="i18n_t('log.table3')"
            v-model="search_params.objectName"
            clearable
            outlined
            style="width: 120px"
          ></i-input>
          <!-- 操作对象扩展ID -->
          <!--乒乓球（8）/棒球（3）/斯洛克（7）日志没有记录操作对象扩展ID-->
          <i-input
            v-if="![3, 7, 8, 4].includes(Number(url_data.query.sportId))"
            class="ml10x"
            :placeholder="i18n_t('log.table5')"
            v-model="search_params.extObjectId"
            clearable
            outlined
            style="width: 130px"
          ></i-input>
          <!--操作对象扩展名称-->
          <i-input
            class="ml10x"
            :placeholder="i18n_t('log.table6')"
            v-model="search_params.extObjectName"
            clearable
            outlined
            style="width: 130px"
          ></i-input>
          <!-- 操作类型 -->
          <iSelect
            v-if="objectNameListShow"
            class="bg-panda-field-grey regionId ml10x"
            size="small"
            style="width: 140px"
            :placeholder="i18n_t('log.table8')"
            v-model="search_params.behavior"
          >
            <iOption
              v-for="item in config.operateTypeList"
              :value="item.code"
              :key="item.index"
            >
              {{ item.value }}
            </iOption>
          </iSelect>
          <title-select
            v-else
            :title="''"
            :list="config.operateTypeList"
            :keys="'behavior'"
            :filterable="true"
            clearable
            :placeholder="i18n_t('log.table8')"
            :styleObj="{ width: '110px' }"
            :defaultData="search_params.behavior"
            @emit_change_select="change_select"
            :className="'ml10x'"
          />
          <!-- 操作人 -->
          <i-input
            class="ml10x"
            :placeholder="i18n_t('log.table9')"
            :maxlength="20"
            v-model="search_params.userName"
            clearable
            outlined
            style="width: 100px"
          ></i-input>
          <iDatePicker
            v-model="search_params.operateStartTime"
            format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            confirm
            @on-change="changea"
            :placeholder="i18n_t('report_common.startTime')"
            placement="bottom-start"
            style="width: 130px"
            class="ml10x"
          ></iDatePicker>
          <span style="margin: 0 5px">-</span>
          <iDatePicker
            v-model="search_params.operateEndTime"
            format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            confirm
            :placeholder="i18n_t('report_common.endTime')"
            placement="bottom-start"
            style="width: 130px"
          ></iDatePicker>
          <q-btn
            no-caps
            class="btn text-panda-text-base panda-btn-primary-hover"
            style="padding: 0 10px; line-height: 20px; margin-left: 30px"
            :loading="isPost"
            @click="confirm()"
            >{{ i18n_t("common.confirm") }}
            <!-- 确定 -->
          </q-btn>
          <q-btn
            no-caps
            class="btn ml10x panda-btn-dark-dense"
            style="padding: 0 10px; line-height: 20px"
            @click="clear_params"
            >{{ i18n_t("event_list.c_2") }}</q-btn
          >
          <div
            class="search-item q-ml-sm row items-center"
            v-if="hrefLog.value"
          >
            <!-- 刷新 -->
            <span :class="['btn']" @click="get_log_data()">{{
              i18n_t("common.refresh")
            }}</span
            >&nbsp;&nbsp;&nbsp;
            <!-- 刷新 -->
            <span :class="['btn']" @click="$router.push('PA_to_the_bat')">{{
              i18n_t("setPage.text36")
            }}</span>
            <!-- 返回PA报球版 -->
          </div>
        </div>
        <div v-if="!hrefLog.value">
          <q-checkbox
            style="vertical-align: middle"
            v-model="traders_log_remember"
            @input="remember_func"
          />
          <span class="q-ml-xs">{{ i18n_t("common.remember") }} </span>
        </div>
      </div>
      <table-no-data
        ref="table-no-data"
        :tabledata_loading="loading"
        :table_no_data_visable="nodata"
        :style="{
          width: '100%',
          height: `${open ? scroll_area_height : height}px`,
        }"
        :loading_style="{ height: '100%', width: '100%' }"
      ></table-no-data>
      <iTable
        border
        :columns="columns"
        :data="table_data"
        :height="open ? scroll_area_height : height"
      >
        <template #paramName>
          <div
            class="position-relative"
            :class="row.fold_status ? '' : 'fold-content'"
          >
            <div v-if="newline">
              <div v-for="(item, index) in row.parameterName" :key="index">
                <span>{{ item }}</span>
              </div>
              <div
                class="position-absolute cursor-pointer"
                v-if="row.parameterName && row.parameterName.length > 2"
                style="right: 0; top: 0"
                @click="change_fold(index, 'operatePageName')"
              >
                <i-icon
                  :type="row.fold_status ? 'ios-arrow-down' : 'ios-arrow-up'"
                />
              </div>
            </div>
            <div v-else>
              <div>{{ row.parameterName }}</div>
            </div>
          </div>
        </template>
        <template  #extObjectName>
          <div v-if="newline">
            <div v-for="(item, index) in row.extObjectName" :key="index">
              <span>{{ item }}</span>
            </div>
          </div>
          <div v-else>
            <div>{{ row.extObjectName }}</div>
          </div>
        </template>
        <template  #parameterName>
          <p class="text" v-html="row.parameterName"></p>
        </template>
        <template  #afterVal>
          <p class="text" v-html="row.afterVal"></p>
        </template>
        <template  #operateTimeStr>
          <div>
            {{
              isNaN(Number(row.operateTimeStr))
                ? row.operateTimeStr
                : get_times(row.operateTimeStr)
            }}
          </div>
        </template>
        <template  #IP>
          <div>{{ row.ip ? row.ip : row.ipAddress }}</div>
        </template>
      </iTable>
      <!-- :style="{height:`${scroll_area_height}px`}" -->
      <!-- routerName == "redcat_plays" -->
      <i-page
        v-if="routerName != `redcat_plays`"
        :total="total"
        show-elevator
        show-sizer
        show-total
        :page-size="post_params.pageSize"
        :page-size-opts="[10, 20, 30, 40]"
        :current="post_params.pageNum"
        @on-change="change_page_num"
        @on-page-size-change="change_page_size"
        class="row items-center justify-center bottom-page"
      >
      </i-page>
      <i-page
        v-else
        :total="total"
        show-elevator
        show-sizer
        show-total
        :page-size="post_params.size"
        :page-size-opts="[10, 20, 30, 40]"
        :current="post_params.page"
        @on-change="change_page_num"
        @on-page-size-change="change_page_size"
        class="row items-center justify-center bottom-page"
      >
      </i-page>
    </div>
  </div>
</template>

<script setup>
import { defineAsyncComponent,toRefs } from 'vue'
const tableNoData = defineAsyncComponent(()=>import("project/caopan/components/table/tableNoData.vue"));
// import tableNoData from "project/caopan/components/table/tableNoData.vue";
const titleSelect = defineAsyncComponent(()=>import("project/caopan/pages/order_mange/center/components/title_select/title_select.vue"));
// import titleSelect from "project/caopan/pages/order_mange/center/index/components/title_select/title_select.vue";
import { project_caopan_components_table_log_popup_variable_componsable_fn } from "project/caopan/components/table/log_popup/componsable/variable.js";
import { project_caopan_components_table_log_popup_componsable_fn } from "project/caopan/components/table/log_popup/componsable/index.js";
import { src_componsable_global_variable_componsable_fn} from "src/output/common/componsable-common.js"

const props = defineProps({
  width: {
    type: [Number, String],
    default: "930px",
  }, //模态框宽度
  height: {
    type: [Number, String],
    default: 660,
  }, //模态框高度
  open: {
    type: [Number, String],
    default: () => 0,
  }, //打开类型  1和2都是弹框打开需要展示头部信息  3是不需要展示头部信息
  params: {
    type: Object,
    default: () => {
      return {};
    },
  }, //请求参数
  columnsType: {
    type: [Number, String],
    default: () => 2,
  }, //columns的类型
  minuHeight: {},
  search: {
    type: Boolean,
    default: () => false,
  }, //是否带搜索
  requestType: {
    type: [Number, String],
    default: 1,
  }, //请求接口类型 1 是赛事级   2是全等级
});
const { search, open,columnsType } = toRefs(props);
const global_obj =src_componsable_global_variable_componsable_fn()
const  {
  router,
  route,
  $q
}=global_obj
const varPayload = {...global_obj,search,open,columnsType}

const base_payload =
  project_caopan_components_table_log_popup_variable_componsable_fn(varPayload);

const {
  update_key,
  sport_list,
  config,
  search_params,
  isPost,
  loading,
  nodata,
  columns,
  table_data,
  total,
  post_params,
} = base_payload;

const {
  change_select,
  changea,
  clear_params,
  remember_func,
  change_page_num,
  change_page_size,
} = project_caopan_components_table_log_popup_componsable_fn({
  ...base_payload,
  ...varPayload,
  props,
});
</script>

<style lang="scss" scoped>
@import url(project/caopan/components/table/log_popup/css/index.scss);
</style>
