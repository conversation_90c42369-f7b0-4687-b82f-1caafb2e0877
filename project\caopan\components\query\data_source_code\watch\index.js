import { watch } from "vue";

import { init_queryform_form_dataSourceCode, emit_dataSourceCode_change, queryform_form_dataSourceCode_change } from "../module/index";

import { set_reset_query } from "src/output/common/store-common.js";

import { dataSource_obj } from "src/output/common/componsable-common.js"

export const watcher_register = (payload)=>{

  const { queryform_form, reset_query } = payload

  return [

    watch(()=>dataSource_obj,  (val) => {
      init_queryform_form_dataSourceCode(payload);
    }, 
    { deep: true, immediate: true}),
    
    watch(()=>queryform_form.value.dataSourceCode, ()=>{
      queryform_form_dataSourceCode_change(payload);
      emit_dataSourceCode_change(payload); 
    }),

    watch(()=>queryform_form.value.dataSourceCode_all, ()=>{
      queryform_form_dataSourceCode_change(payload);
      emit_dataSourceCode_change(payload); 
    }),

    watch(()=>reset_query.value, (val)=>{
      if (val == 2) {
        init_queryform_form_dataSourceCode(payload);
        set_reset_query(1);
      }
    })
  ]

}
